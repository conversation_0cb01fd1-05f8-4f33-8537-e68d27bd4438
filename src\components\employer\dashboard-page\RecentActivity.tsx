import { Card, Tabs, Title } from "@mantine/core";
import { FaBriefcase, FaCalendarAlt, FaUsers } from "react-icons/fa";
import RecentlyAppliedCandidates from "./RecentlyAppliedCandidates";
import RecentlyPostedJobs from "./RecentlyPostedJobs";
import UpcomingInterviews from "./UpcomingInterviews";

export default function RecentActivity() {
  return (
    <div className="mb-8">
      <Title order={3} className="mb-4 text-xl font-semibold text-blue-800">
        Recent Activity
      </Title>

      <Card withBorder radius="md" className="shadow-sm">
        <Tabs defaultValue="candidates">
          <Tabs.List>
            <Tabs.Tab value="candidates" leftSection={<FaUsers size={16} />}>
              Recent Candidates
            </Tabs.Tab>
            <Tabs.Tab value="jobs" leftSection={<FaBriefcase size={16} />}>
              Recent Jobs
            </Tabs.Tab>
            <Tabs.Tab
              value="interviews"
              leftSection={<FaCalendarAlt size={16} />}
            >
              Upcoming Interviews
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="candidates" pt="md">
            <RecentlyAppliedCandidates />
          </Tabs.Panel>

          <Tabs.Panel value="jobs" pt="md">
            <RecentlyPostedJobs />
          </Tabs.Panel>

          <Tabs.Panel value="interviews" pt="md">
            <UpcomingInterviews />
          </Tabs.Panel>
        </Tabs>
      </Card>
    </div>
  );
}
