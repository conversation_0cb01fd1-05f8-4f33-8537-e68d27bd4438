import { mockJobDetails } from "@/data/job-details";
import { PageContainer } from "@/design-system/components";
import { useThemeClasses } from "@/design-system/utils/theme-utils";
import {
  Box,
  Button,
  Card,
  Checkbox,
  Divider,
  FileInput,
  Group,
  Radio,
  SimpleGrid,
  Stack,
  Text,
  TextInput,
  Textarea,
  Title,
} from "@mantine/core";
import { useForm } from "@mantine/form";
import { notifications } from "@mantine/notifications";
import {
  BiBuildings,
  BiCloudUpload,
  BiEnvelope,
  BiPhone,
  BiUser,
} from "react-icons/bi";

interface ApplicationForm {
  fullName: string;
  email: string;
  phone: string;
  resume: File | null;
  coverLetter: string;
  questions: {
    [key: string]: string | string[];
  };
}

export default function CandidateJobApplicationPage() {
  const jobDetails = mockJobDetails;

  const form = useForm<ApplicationForm>({
    initialValues: {
      fullName: "",
      email: "",
      phone: "",
      resume: null,
      coverLetter: "",
      questions: {},
    },
    validate: {
      fullName: (value) =>
        value.length < 3 ? "Name must be at least 3 characters" : null,
      email: (value) =>
        /^\S+@\S+$/.test(value) ? null : "Invalid email address",
      phone: (value) =>
        value.length < 10 ? "Phone number must be at least 10 digits" : null,
      resume: (value) => (!value ? "Resume is required" : null),
      questions: (value: { [key: string]: string | string[] }) => {
        for (const q of jobDetails.questions) {
          if (q.required && !value[q.id]) {
            return "All required questions must be answered";
          }
        }
        return null;
      },
    },
  });

  const handleSubmit = (values: ApplicationForm) => {
    // Handle form submission
    console.log("Form values:", values);
    notifications.show({
      title: "Application Submitted",
      message: "Your application has been submitted successfully!",
      color: "green",
    });
  };

  const renderQuestion = (question: any) => {
    switch (question.type) {
      case "text":
        return (
          <TextInput
            key={question.id}
            label={question.question}
            placeholder="Your answer"
            required={question.required}
            {...form.getInputProps(`questions.${question.id}`)}
          />
        );
      case "textarea":
        return (
          <Textarea
            key={question.id}
            label={question.question}
            placeholder="Your answer"
            required={question.required}
            {...form.getInputProps(`questions.${question.id}`)}
          />
        );
      case "radio":
        return (
          <Radio.Group
            key={question.id}
            label={question.question}
            required={question.required}
            {...form.getInputProps(`questions.${question.id}`)}
          >
            <Group mt="xs">
              {question.options.map((option: string) => (
                <Radio key={option} value={option} label={option} />
              ))}
            </Group>
          </Radio.Group>
        );
      case "checkbox":
        return (
          <Checkbox.Group
            key={question.id}
            label={question.question}
            required={question.required}
            {...form.getInputProps(`questions.${question.id}`)}
          >
            <Group mt="xs">
              {question.options.map((option: string) => (
                <Checkbox key={option} value={option} label={option} />
              ))}
            </Group>
          </Checkbox.Group>
        );
      default:
        return null;
    }
  };

  return (
    <PageContainer
      breadcrumbItems={[
        { title: "Home", href: "/" },
        { title: "Jobs", href: "/candidate/jobs" },
        { title: "Job Details", href: `/candidate/jobs/${jobDetails.id}` },
        { title: "Apply" },
      ]}
      variant="candidate"
    >
      <Card radius="md" withBorder>
        <>
          <Group
            gap="xl"
            mb="xl"
            className={useThemeClasses(
              "border-b border-gray-200 pb-6",
              "border-b border-gray-700 pb-6",
            )}
          >
            <div
              className={useThemeClasses(
                "bg-primary/10 flex h-16 w-16 items-center justify-center rounded-full",
                "bg-primary/20 flex h-16 w-16 items-center justify-center rounded-full",
              )}
            >
              <BiBuildings className="text-primary h-8 w-8" />
            </div>
            <div>
              <Title
                order={2}
                className={useThemeClasses(
                  "text-2xl font-bold text-[#0f172a]",
                  "text-2xl font-bold text-gray-100",
                )}
              >
                Apply for {jobDetails.title}
              </Title>
              <Text className="text-primary-color mt-1 text-lg">
                at {jobDetails.company}
              </Text>
            </div>
          </Group>

          <form noValidate onSubmit={form.onSubmit(handleSubmit)}>
            <Stack gap="xl">
              <Box>
                <Title
                  order={3}
                  className={useThemeClasses(
                    "mb-4 text-lg text-[#0f172a]",
                    "mb-4 text-lg text-gray-200",
                  )}
                >
                  Personal Information
                </Title>
                <SimpleGrid cols={{ base: 1, sm: 2 }} spacing="md">
                  <TextInput
                    label="Full Name"
                    placeholder="Enter your full name"
                    leftSection={<BiUser />}
                    required
                    classNames={{
                      input: "focus:border-primary",
                      label: useThemeClasses(
                        "text-[#0f172a] font-medium",
                        "text-gray-300 font-medium",
                      ),
                    }}
                    {...form.getInputProps("fullName")}
                  />
                  <TextInput
                    label="Email"
                    placeholder="<EMAIL>"
                    leftSection={<BiEnvelope />}
                    required
                    classNames={{
                      input: "focus:border-primary",
                      label: useThemeClasses(
                        "text-[#0f172a] font-medium",
                        "text-gray-300 font-medium",
                      ),
                    }}
                    {...form.getInputProps("email")}
                  />
                </SimpleGrid>
                <TextInput
                  label="Phone Number"
                  placeholder="Enter your phone number"
                  leftSection={<BiPhone />}
                  required
                  mt="md"
                  classNames={{
                    input: "focus:border-primary",
                    label: useThemeClasses(
                      "text-[#0f172a] font-medium",
                      "text-gray-300 font-medium",
                    ),
                  }}
                  {...form.getInputProps("phone")}
                />
              </Box>

              <Box>
                <Title
                  order={3}
                  className={useThemeClasses(
                    "mb-4 text-lg text-[#0f172a]",
                    "mb-4 text-lg text-gray-200",
                  )}
                >
                  Application Documents
                </Title>
                <Stack gap="md">
                  <FileInput
                    label="Resume"
                    placeholder="Upload your resume"
                    leftSection={<BiCloudUpload />}
                    accept="application/pdf,application/msword"
                    required
                    classNames={{
                      input: "focus:border-primary",
                      label: useThemeClasses(
                        "text-[#0f172a] font-medium",
                        "text-gray-300 font-medium",
                      ),
                    }}
                    {...form.getInputProps("resume")}
                  />
                  <Textarea
                    label="Cover Letter"
                    placeholder="Write a brief cover letter..."
                    minRows={5}
                    classNames={{
                      input: "focus:border-primary",
                      label: useThemeClasses(
                        "text-[#0f172a] font-medium",
                        "text-gray-300 font-medium",
                      ),
                    }}
                    {...form.getInputProps("coverLetter")}
                  />
                </Stack>
              </Box>

              {jobDetails.questions.length > 0 && (
                <Box>
                  <Divider
                    my="lg"
                    label={
                      <Text
                        className={useThemeClasses(
                          "font-medium text-[#0f172a]",
                          "font-medium text-gray-300",
                        )}
                      >
                        Additional Questions
                      </Text>
                    }
                    labelPosition="center"
                  />
                  <Stack gap="lg" className="mt-6">
                    {jobDetails.questions.map((question) => (
                      <Box
                        key={question.id}
                        className={useThemeClasses(
                          "rounded-md bg-gray-50 p-4",
                          "rounded-md bg-dark-6 p-4",
                        )}
                      >
                        {renderQuestion(question)}
                      </Box>
                    ))}
                  </Stack>
                </Box>
              )}

              <Button type="submit" className="ms-auto !w-fit">
                Submit Application
              </Button>
            </Stack>
          </form>
        </>
      </Card>
    </PageContainer>
  );
}
