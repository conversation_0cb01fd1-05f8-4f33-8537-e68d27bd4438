import SectionHeading from "@/design-system/components/typography/SectionHeading";
import { type ReactNode } from "react";
import { type IconType } from "react-icons";

/**
 * @deprecated Use SectionHeading from design-system/components with variant="admin" instead
 */
export default function AdminSectionHeading({
  children,
  Icon,
  className,
}: {
  children?: ReactNode;
  Icon?: IconType;
  className?: string;
}) {
  return (
    <SectionHeading icon={Icon} className={className} variant="admin">
      {children}
    </SectionHeading>
  );
}
