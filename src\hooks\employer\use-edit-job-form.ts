import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import { useEffect } from "react";
import { z } from "zod";
import { editJobSchema } from "../../schemas/employer/edit-job-schema";

// Type for form values
type FormValues = z.infer<typeof editJobSchema>;

export default function useEditJobForm() {
  const form = useForm<FormValues>({
    initialValues: {
      jobTitle: "",
      minSalary: undefined,
      maxSalary: undefined,
      currency: "",
      location: "",
      jobType: "",
      jobDescription: "",
      jobRequirements: "",
      applicationDeadline: undefined,
      requiredExperience: "",
      jobAttachment: undefined,
      // companyName: "",
      // companyWebsite: "",
      // companyLogo: undefined,
      // aboutCompany: "",
      jobCategory: "",
      jobTags: [],
      benefits: [],
      showSalary: false,
      // enableExpiryNotification: false,
      // notificationEmail: "",
      // jobStatus: "",
    },
    validate: zodResolver(editJobSchema),
  });

  useEffect(() => {
    form.setValues({
      jobTitle: "Software Engineer",
      minSalary: 100000,
      maxSalary: 150000,
      currency: "USD",
      location: "remote",
      jobType: "full-time",
      jobDescription: "Write code and fix bugs",
      jobRequirements: "Good communication skills",
      applicationDeadline: new Date(),
      requiredExperience: "2 years",
      jobAttachment: undefined,
      // companyName: "Acme Inc.",
      // companyWebsite: "www.acme.com",
      // companyLogo: undefined,
      // aboutCompany: "We are a tech company",
      jobCategory: "web-development",
      jobTags: ["React", "Node.js", "MongoDB"],
      benefits: ["Healthcare", "Paid leave", "Flexible schedule"],
      showSalary: true,
      // enableExpiryNotification: true,
      // notificationEmail: "<EMAIL>",
      // jobStatus: "published",
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSubmit = (values: FormValues) => {
    console.log(values);
  };

  return {
    form,
    handleSubmit,
  };
}
