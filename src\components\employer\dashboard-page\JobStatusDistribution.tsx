import { SectionHeading } from "@/design-system/components";
import { Pie<PERSON><PERSON> } from "@mantine/charts";
import { Card } from "@mantine/core";

const jobStatusData = [
  { name: "Active", value: 8, color: "blue" },
  { name: "Pending", value: 4, color: "yellow" },
  { name: "Closed", value: 3, color: "red" },
];

export default function JobStatusDistribution() {
  return (
    <Card withBorder shadow="sm" padding="lg" radius="md">
      <SectionHeading variant="employer">
        Job Status Distribution
      </SectionHeading>
      <PieChart data={jobStatusData} withTooltip withLabels size={300} />
    </Card>
  );
}
