interface Cache {
  /***
   * Set a value in the cache
   */
  set: (key: string, value: any) => void;
  /***
   * Get a value from the cache
   */
  get(key: string): any;
  /***
   * clear cache
   */
  clear(): void;
}

export const cache: Cache = {
  /***
   * Set a value in the cache
   */
  set(key: string, value: any) {
    localStorage.setItem(key, JSON.stringify(value));
  },
  /***
   * Get a value from the cache
   */
  get(key: string) {
    return JSON.parse(localStorage.getItem(key)!);
  },
  /**
   * clear cache
   */
  clear() {
    localStorage.clear();
  },
};
