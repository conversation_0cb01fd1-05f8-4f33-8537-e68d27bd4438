// Define interface for resource
interface Resource {
  id: number;
  title: string;
  description: string;
  image: string;
  category: "article" | "video" | "guide";
  date: string;
  readTime: string;
}

import {
  Badge,
  Button,
  Card,
  Group,
  Image,
  Select,
  SimpleGrid,
  Tabs,
  Text,
  TextInput,
  useMantineColorScheme,
} from "@mantine/core";
import {
  FaArrowRight,
  FaBookOpen,
  FaFileAlt,
  FaSearch,
  FaVideo,
} from "react-icons/fa";

const resources: Resource[] = [
  {
    id: 1,
    title: "How to Create a Standout Resume",
    description:
      "Learn the essential elements of a professional resume that will catch employers' attention and help you land interviews.",
    image:
      "https://images.unsplash.com/photo-1586281380349-632531db7ed4?q=80&w=500&auto=format&fit=crop",
    category: "article",
    date: "June 15, 2023",
    readTime: "8 min read",
  },
  {
    id: 2,
    title: "Mastering the Job Interview",
    description:
      "Prepare for your next interview with these proven techniques and strategies to make a great impression.",
    image:
      "https://images.unsplash.com/photo-1573497161161-c3e73707e25c?q=80&w=500&auto=format&fit=crop",
    category: "video",
    date: "July 22, 2023",
    readTime: "12 min video",
  },
  {
    id: 3,
    title: "Negotiating Your Salary: A Complete Guide",
    description:
      "Learn how to confidently negotiate your salary and benefits package to ensure you're compensated fairly.",
    image:
      "https://images.unsplash.com/photo-1589939705384-5185137a7f0f?q=80&w=500&auto=format&fit=crop",
    category: "guide",
    date: "August 5, 2023",
    readTime: "15 min read",
  },
  {
    id: 4,
    title: "Building Your Professional Network",
    description:
      "Discover effective strategies for expanding your professional connections both online and offline.",
    image:
      "https://images.unsplash.com/photo-1521791136064-7986c2920216?q=80&w=500&auto=format&fit=crop",
    category: "article",
    date: "September 10, 2023",
    readTime: "10 min read",
  },
  {
    id: 5,
    title: "Remote Work Best Practices",
    description:
      "Tips and strategies for staying productive, maintaining work-life balance, and thriving in a remote work environment.",
    image:
      "https://images.unsplash.com/photo-1593642634367-d91a135587b5?q=80&w=500&auto=format&fit=crop",
    category: "guide",
    date: "October 18, 2023",
    readTime: "12 min read",
  },
  {
    id: 6,
    title: "LinkedIn Profile Optimization",
    description:
      "Learn how to optimize your LinkedIn profile to attract recruiters and showcase your professional brand.",
    image:
      "https://images.unsplash.com/photo-1611944212129-29977ae1398c?q=80&w=500&auto=format&fit=crop",
    category: "video",
    date: "November 5, 2023",
    readTime: "18 min video",
  },
];

const categoryIcons: Record<Resource["category"], React.ReactNode> = {
  article: <FaFileAlt size={16} />,
  video: <FaVideo size={16} />,
  guide: <FaBookOpen size={16} />,
};

export default function ResourcesPage() {
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  return (
    <div className={`py-20 ${isDark ? "bg-gray-900" : "bg-gray-50"}`}>
      <div className="container">
        <div className="mx-auto mb-16 max-w-3xl text-center">
          <Badge color="primary_color" size="lg" radius="sm" className="mb-4">
            Career Resources
          </Badge>
          <h1
            className={`mb-6 text-4xl font-bold tracking-tight sm:text-5xl ${isDark ? "text-white" : ""}`}
          >
            Grow Your Career with JobNest
          </h1>
          <p
            className={`mb-8 text-lg ${isDark ? "text-gray-300" : "text-gray-600"}`}
          >
            Explore our collection of resources designed to help you advance
            your career, improve your job search, and develop professional
            skills.
          </p>

          {/* Search and filter */}
          <div className="mx-auto mb-12 flex max-w-3xl flex-col gap-4 sm:flex-row">
            <TextInput
              placeholder="Search resources..."
              className="flex-1"
              leftSection={
                <FaSearch size={16} className={isDark ? "text-gray-400" : ""} />
              }
              classNames={{
                input: isDark
                  ? "border-gray-700 bg-gray-800 text-gray-200"
                  : "",
              }}
            />
            <Select
              placeholder="Filter by type"
              data={[
                { value: "all", label: "All Resources" },
                { value: "article", label: "Articles" },
                { value: "video", label: "Videos" },
                { value: "guide", label: "Guides" },
              ]}
              defaultValue="all"
              className="w-full sm:w-48"
              classNames={{
                input: isDark
                  ? "border-gray-700 bg-gray-800 text-gray-200"
                  : "",
                dropdown: isDark ? "bg-gray-800 border-gray-700" : "",
                option: isDark ? "text-gray-200 hover:bg-gray-700" : "",
              }}
            />
          </div>
        </div>

        {/* Featured resource */}
        <Card
          withBorder
          radius="md"
          className={`mb-16 overflow-hidden ${isDark ? "bg-gray-800 border-gray-700" : ""}`}
        >
          <div className="grid md:grid-cols-2">
            <div className="order-2 p-8 md:order-1">
              <Badge color="primary_color" radius="sm" className="mb-2">
                Featured
              </Badge>
              <h2
                className={`mb-4 text-3xl font-bold ${isDark ? "text-white" : ""}`}
              >
                The Complete Job Search Strategy for 2023
              </h2>
              <p
                className={`mb-6 ${isDark ? "text-gray-300" : "text-gray-600"}`}
              >
                Our comprehensive guide covers everything from resume
                preparation to interview techniques and salary negotiation.
                Learn the latest strategies that are working in today&apos;s
                competitive job market.
              </p>
              <Group gap="xs" className="mb-6">
                <Badge variant="dot" color="blue">
                  Guide
                </Badge>
                <Text size="sm" c={isDark ? "gray.4" : "dimmed"}>
                  Published: May 1, 2023
                </Text>
                <Text size="sm" c={isDark ? "gray.4" : "dimmed"}>
                  20 min read
                </Text>
              </Group>
              <Button
                rightSection={<FaArrowRight size={14} />}
                className={`${
                  isDark
                    ? "bg-primary-600 hover:bg-primary-700"
                    : "bg-primary-color hover:bg-primary-color/90"
                }`}
              >
                Read Guide
              </Button>
            </div>
            <div className="order-1 h-64 md:order-2 md:h-auto">
              <Image
                src="https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?q=80&w=800&auto=format&fit=crop"
                alt="Job Search Strategy"
                height="100%"
                className="h-full w-full object-cover"
              />
            </div>
          </div>
        </Card>

        {/* Resource categories */}
        <Tabs
          defaultValue="all"
          className="mb-12"
          classNames={{
            list: isDark ? "border-gray-700" : "",
            tab: isDark
              ? "text-gray-300 hover:bg-gray-700 data-[active]:border-primary-color data-[active]:text-primary-color"
              : "",
          }}
        >
          <Tabs.List>
            <Tabs.Tab value="all">All Resources</Tabs.Tab>
            <Tabs.Tab
              value="article"
              leftSection={
                <FaFileAlt
                  size={14}
                  className={isDark ? "text-gray-400" : ""}
                />
              }
            >
              Articles
            </Tabs.Tab>
            <Tabs.Tab
              value="video"
              leftSection={
                <FaVideo size={14} className={isDark ? "text-gray-400" : ""} />
              }
            >
              Videos
            </Tabs.Tab>
            <Tabs.Tab
              value="guide"
              leftSection={
                <FaBookOpen
                  size={14}
                  className={isDark ? "text-gray-400" : ""}
                />
              }
            >
              Guides
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="all" pt="xl">
            <SimpleGrid cols={{ base: 1, sm: 2, lg: 3 }} spacing="lg">
              {resources.map((resource) => (
                <ResourceCard key={resource.id} resource={resource} />
              ))}
            </SimpleGrid>
          </Tabs.Panel>

          {["article", "video", "guide"].map((category) => (
            <Tabs.Panel key={category} value={category} pt="xl">
              <SimpleGrid cols={{ base: 1, sm: 2, lg: 3 }} spacing="lg">
                {resources
                  .filter((resource) => resource.category === category)
                  .map((resource) => (
                    <ResourceCard key={resource.id} resource={resource} />
                  ))}
              </SimpleGrid>
            </Tabs.Panel>
          ))}
        </Tabs>

        {/* Newsletter signup */}
        <div
          className={`mx-auto max-w-3xl rounded-xl p-8 text-center shadow-lg ${
            isDark
              ? "bg-gray-800 border border-primary-700"
              : "bg-primary-color text-white"
          }`}
        >
          <h3
            className={`mb-4 text-2xl font-bold ${isDark ? "text-white" : ""}`}
          >
            Stay Updated
          </h3>
          <p className={`mb-6 ${isDark ? "text-gray-300" : ""}`}>
            Subscribe to our newsletter to receive the latest career resources,
            job search tips, and industry insights.
          </p>
          <div className="flex flex-col gap-4 sm:flex-row">
            <TextInput
              placeholder="Enter your email"
              className="flex-1"
              radius="md"
              classNames={{
                input: isDark
                  ? "border-gray-700 bg-gray-800 text-gray-200"
                  : "border-white/20 bg-white/10 text-white placeholder:text-white/70",
                root: !isDark ? "placeholder:text-white/70" : "",
              }}
            />
            <Button
              variant={isDark ? "filled" : "outline"}
              color={isDark ? "primary" : "white"}
              className={
                isDark ? "" : "border-white text-white hover:bg-white/10"
              }
            >
              Subscribe
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

function ResourceCard({ resource }: { resource: Resource }) {
  const { title, description, image, category, date, readTime } = resource;
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  return (
    <Card
      withBorder
      radius="md"
      className={`h-full overflow-hidden transition-shadow duration-300 hover:shadow-md ${
        isDark ? "bg-gray-800 border-gray-700" : ""
      }`}
    >
      <Card.Section>
        <Image src={image} height={200} alt={title} className="object-cover" />
      </Card.Section>

      <div className="p-6">
        <Group justify="space-between" className="mb-2">
          <Badge
            leftSection={categoryIcons[category]}
            color={
              category === "article"
                ? "blue"
                : category === "video"
                  ? "red"
                  : "green"
            }
          >
            {category.charAt(0).toUpperCase() + category.slice(1)}
          </Badge>
          <Text size="xs" c={isDark ? "gray.4" : "dimmed"}>
            {date}
          </Text>
        </Group>

        <Text
          fw={700}
          size="lg"
          className={`mb-2 line-clamp-2 ${isDark ? "text-white" : ""}`}
        >
          {title}
        </Text>

        <Text
          size="sm"
          c={isDark ? "gray.4" : "dimmed"}
          className="mb-4 line-clamp-3"
        >
          {description}
        </Text>

        <Group justify="space-between" mt="auto">
          <Text size="xs" c={isDark ? "gray.4" : "dimmed"}>
            {readTime}
          </Text>
          <Button
            variant="subtle"
            color={isDark ? "blue.4" : "blue"}
            size="xs"
            rightSection={<FaArrowRight size={12} />}
          >
            Read More
          </Button>
        </Group>
      </div>
    </Card>
  );
}
