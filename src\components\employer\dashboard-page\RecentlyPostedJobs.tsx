import { Badge, Button, Group, Text } from "@mantine/core";
import {
  FaCalendarAlt,
  FaEye,
  FaPencilAlt,
  FaUserFriends,
} from "react-icons/fa";

const recentJobs = [
  {
    id: 1,
    title: "Senior Software Engineer",
    status: "Active",
    postedDate: "2023-10-01",
    applicants: 12,
    location: "New York, NY",
  },
  {
    id: 2,
    title: "Product Manager",
    status: "Pending",
    postedDate: "2023-10-05",
    applicants: 8,
    location: "Remote",
  },
  {
    id: 3,
    title: "UX Designer",
    status: "Closed",
    postedDate: "2023-09-28",
    applicants: 15,
    location: "San Francisco, CA",
  },
  {
    id: 4,
    title: "Frontend Developer",
    status: "Active",
    postedDate: "2023-10-10",
    applicants: 6,
    location: "Chicago, IL",
  },
];

export default function RecentlyPostedJobs() {
  return (
    <div>
      {recentJobs.map((job) => (
        <div key={job.id} className="mb-4 border-b pb-4">
          <Group justify="space-between" wrap="nowrap" className="mb-2">
            <div>
              <Text fw={600}>{job.title}</Text>
              <Text size="sm" c="dimmed">
                {job.location}
              </Text>
            </div>
            <Badge
              size="lg"
              color={
                job.status === "Active"
                  ? "blue"
                  : job.status === "Pending"
                    ? "yellow"
                    : "red"
              }
            >
              {job.status}
            </Badge>
          </Group>

          <Group className="mt-2">
            <Group gap="xs">
              <FaCalendarAlt size={14} className="text-blue-500" />
              <Text size="sm">Posted: {job.postedDate}</Text>
            </Group>
            <Group gap="xs">
              <FaUserFriends size={14} className="text-blue-500" />
              <Text size="sm">{job.applicants} Applicants</Text>
            </Group>
          </Group>

          <Group className="mt-3">
            <Button variant="light" size="xs" leftSection={<FaEye size={12} />}>
              View
            </Button>
            <Button
              variant="outline"
              size="xs"
              leftSection={<FaPencilAlt size={12} />}
            >
              Edit
            </Button>
          </Group>
        </div>
      ))}

      <Group justify="center" className="mt-4">
        <Button variant="subtle">View All Jobs</Button>
      </Group>
    </div>
  );
}
