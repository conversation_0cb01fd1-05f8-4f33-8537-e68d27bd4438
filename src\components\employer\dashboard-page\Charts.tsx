import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@mantine/charts";
import { Card, Grid, Title } from "@mantine/core";

// Mock data for charts
const jobStatusData = [
  { name: "Active", value: 8, color: "blue" },
  { name: "Pending", value: 4, color: "yellow" },
  { name: "Closed", value: 3, color: "red" },
];

const candidateProgressData = [
  { label: "Hired", value: 30 },
  { label: "Interviewed", value: 20 },
  { label: "Shortlisted", value: 45 },
  { label: "Applied", value: 70 },
];

const applicationTrendData = [
  { date: "Jan", Applications: 12 },
  { date: "Feb", Applications: 18 },
  { date: "Mar", Applications: 15 },
  { date: "Apr", Applications: 22 },
  { date: "May", Applications: 28 },
  { date: "Jun", Applications: 32 },
  { date: "Jul", Applications: 38 },
  { date: "Aug", Applications: 42 },
  { date: "Sep", Applications: 35 },
  { date: "Oct", Applications: 48 },
  { date: "Nov", Applications: 50 },
  { date: "Dec", Applications: 45 },
];

export default function Charts() {
  return (
    <div className="mb-8">
      <Title order={3} className="mb-4 text-xl font-semibold text-blue-800">
        Analytics & Insights
      </Title>

      <Grid gutter="md">
        <Grid.Col span={{ base: 12, lg: 4 }}>
          <Card withBorder radius="md" className="h-full shadow-sm">
            <Title order={4} className="mb-4 text-lg font-semibold">
              Job Status Distribution
            </Title>
            <PieChart
              data={jobStatusData}
              withTooltip
              withLabels
              size={250}
              mx="auto"
              withLabelsLine
              labelsType="percent"
              labelsPosition="outside"
            />
          </Card>
        </Grid.Col>

        <Grid.Col span={{ base: 12, lg: 8 }}>
          <Card withBorder radius="md" className="h-full shadow-sm">
            <Title order={4} className="mb-4 text-lg font-semibold">
              Application Trends
            </Title>
            <AreaChart
              h={250}
              data={applicationTrendData}
              dataKey="date"
              series={[{ name: "Applications", color: "blue.6" }]}
              curveType="natural"
              withLegend
              withTooltip
              gridAxis="xy"
              yAxisProps={{ width: 50 }}
            />
          </Card>
        </Grid.Col>

        <Grid.Col span={12}>
          <Card withBorder radius="md" className="shadow-sm">
            <Title order={4} className="mb-4 text-lg font-semibold">
              Candidate Progress
            </Title>
            <BarChart
              h={250}
              data={candidateProgressData}
              dataKey="label"
              series={[{ name: "value", color: "blue.6" }]}
              withLegend
              withTooltip
              tickLine="y"
              yAxisProps={{ width: 50 }}
            />
          </Card>
        </Grid.Col>
      </Grid>
    </div>
  );
}
