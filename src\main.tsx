/***
 * styles
 */
import "./styles.ts";

/***
 * mantine
 */
import { MantineProvider } from "@mantine/core";
import { Notifications } from "@mantine/notifications";
import { theme } from "./design-system/utils/theme.tsx";

/***
 * react
 */
import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { BrowserRouter } from "react-router";

/***
 * components
 */
import App from "./App.tsx";

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <BrowserRouter>
      {/* MantineProvider is used to provide the theme to the entire application */}
      <MantineProvider
        theme={theme}
        defaultColorScheme="light"
        cssVariablesSelector=":root"
      >
        {/* App is the root component of the application */}
        <App />
        {/* Notifications are used to display success, error, and other messages to the user */}
        <Notifications position="top-right" zIndex={1000} />
      </MantineProvider>
    </BrowserRouter>
  </StrictMode>,
);
