import { candidateData } from "@/data/candidate-data";
import { <PERSON><PERSON>, Card, Group, Title } from "@mantine/core";
import { FaGithub, FaGlobe, FaLinkedin } from "react-icons/fa";
import { Link } from "react-router";

export default function CandidateLinks() {
  return (
    <Card withBorder radius="md" className="mt-6 p-6 shadow-sm">
      <Title order={2} className="mb-4 text-lg font-semibold">
        Links
      </Title>
      <Group>
        <Button
          component={Link}
          to={candidateData.linkedin}
          target="_blank"
          leftSection={<FaLinkedin size={16} />}
          variant="outline"
          size="sm"
        >
          LinkedIn
        </Button>
        <Button
          component={Link}
          to={candidateData.github}
          target="_blank"
          leftSection={<FaGithub size={16} />}
          variant="outline"
          size="sm"
        >
          GitHub
        </Button>
        <Button
          component={Link}
          to={candidateData.portfolio}
          target="_blank"
          leftSection={<FaGlobe size={16} />}
          variant="outline"
          size="sm"
        >
          Portfolio
        </Button>
      </Group>
    </Card>
  );
}
