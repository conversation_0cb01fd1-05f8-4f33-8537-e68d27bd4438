import {
  employerDataService,
  type Application,
  type Job,
} from "@/services/employer/employerDataService";
import { useCallback, useEffect, useState } from "react";

// Hook for managing jobs
export function useJobs() {
  const [jobs, setJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchJobs = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await employerDataService.getJobs();
      setJobs(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch jobs");
    } finally {
      setLoading(false);
    }
  }, []);

  const createJob = useCallback(async (jobData: Partial<Job>) => {
    try {
      const newJob = await employerDataService.createJob(jobData);
      setJobs((prev) => [newJob, ...prev]);
      return newJob;
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to create job");
      throw err;
    }
  }, []);

  const updateJob = useCallback(async (id: string, updates: Partial<Job>) => {
    try {
      const updatedJob = await employerDataService.updateJob(id, updates);
      if (updatedJob) {
        setJobs((prev) =>
          prev.map((job) => (job.id === id ? updatedJob : job)),
        );
      }
      return updatedJob;
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to update job");
      throw err;
    }
  }, []);

  const deleteJob = useCallback(async (id: string) => {
    try {
      const success = await employerDataService.deleteJob(id);
      if (success) {
        setJobs((prev) => prev.filter((job) => job.id !== id));
      }
      return success;
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to delete job");
      throw err;
    }
  }, []);

  useEffect(() => {
    fetchJobs();
  }, [fetchJobs]);

  return {
    jobs,
    loading,
    error,
    refetch: fetchJobs,
    createJob,
    updateJob,
    deleteJob,
  };
}

// Hook for managing applications
export function useApplications(jobId?: string) {
  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchApplications = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await employerDataService.getApplications(jobId);
      setApplications(data);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to fetch applications",
      );
    } finally {
      setLoading(false);
    }
  }, [jobId]);

  const updateApplicationStatus = useCallback(
    async (id: string, status: "pending" | "approved" | "rejected") => {
      try {
        const updatedApp = await employerDataService.updateApplicationStatus(
          id,
          status,
        );
        if (updatedApp) {
          setApplications((prev) =>
            prev.map((app) => (app.id === id ? updatedApp : app)),
          );
        }
        return updatedApp;
      } catch (err) {
        setError(
          err instanceof Error
            ? err.message
            : "Failed to update application status",
        );
        throw err;
      }
    },
    [],
  );

  useEffect(() => {
    fetchApplications();
  }, [fetchApplications]);

  return {
    applications,
    loading,
    error,
    refetch: fetchApplications,
    updateApplicationStatus,
  };
}

// Hook for job statistics
export function useJobStats() {
  const [stats, setStats] = useState({
    totalJobs: 0,
    activeJobs: 0,
    totalApplications: 0,
    pendingApplications: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await employerDataService.getJobStats();
      setStats(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch stats");
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  return {
    stats,
    loading,
    error,
    refetch: fetchStats,
  };
}

// Hook for filtering and searching
export function useJobFilters(jobs: Job[]) {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("");
  const [locationFilter, setLocationFilter] = useState<string>("");
  const [jobTypeFilter, setJobTypeFilter] = useState<string>("");

  const filteredJobs = jobs.filter((job) => {
    const matchesSearch =
      job.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      job.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = !statusFilter || job.status === statusFilter;
    const matchesLocation = !locationFilter || job.location === locationFilter;
    const matchesJobType = !jobTypeFilter || job.jobType === jobTypeFilter;

    return matchesSearch && matchesStatus && matchesLocation && matchesJobType;
  });

  const clearFilters = useCallback(() => {
    setSearchTerm("");
    setStatusFilter("");
    setLocationFilter("");
    setJobTypeFilter("");
  }, []);

  return {
    searchTerm,
    setSearchTerm,
    statusFilter,
    setStatusFilter,
    locationFilter,
    setLocationFilter,
    jobTypeFilter,
    setJobTypeFilter,
    filteredJobs,
    clearFilters,
  };
}

// Hook for application filters
export function useApplicationFilters(applications: Application[]) {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("");

  const filteredApplications = applications.filter((app) => {
    const matchesSearch =
      app.candidateName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      app.candidateEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
      app.jobTitle.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = !statusFilter || app.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const clearFilters = useCallback(() => {
    setSearchTerm("");
    setStatusFilter("");
  }, []);

  return {
    searchTerm,
    setSearchTerm,
    statusFilter,
    setStatusFilter,
    filteredApplications,
    clearFilters,
  };
}
