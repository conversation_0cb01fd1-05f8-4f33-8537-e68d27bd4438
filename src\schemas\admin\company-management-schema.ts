import { z } from "zod";

export const companyFilterSchema = z.object({
  search: z.string().optional(),
  status: z.enum(["all", "active", "pending", "blocked"]).optional(),
  industry: z.string().optional(),
  size: z.string().optional(),
  sortBy: z.enum(["name", "industry", "createdAt", "status"]).optional(),
  sortOrder: z.enum(["asc", "desc"]).optional(),
});

export const companyStatusUpdateSchema = z.object({
  status: z.enum(["active", "pending", "blocked"]),
  blockReason: z.string().optional(),
});
