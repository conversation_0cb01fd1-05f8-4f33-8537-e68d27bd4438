import { dummyJobs } from "@/data/jobs-data";
import { type Job } from "@/types";
import { create } from "zustand";

// Define the jobs store type
interface JobsStore {
  jobs: Job[];
  isLoading: boolean;
  error: string | null;
  setJobs: (jobs: Job[]) => void;
  setLoading: (isLoading: boolean) => void;
  setError: (error: string | null) => void;
}

// Create the jobs store
export const useJobsStore = create<JobsStore>((set) => ({
  jobs: dummyJobs,
  isLoading: false,
  error: null,
  setJobs: (jobs) => set({ jobs }),
  setLoading: (isLoading) => set({ isLoading }),
  setError: (error) => set({ error }),
}));

// Define the jobs view mode store type
interface JobsViewModeStore {
  viewMode: "grid" | "list";
  setViewMode: (viewMode: "grid" | "list") => void;
  toggleViewMode: () => void;
}

// Create the jobs view mode store
export const useJobsViewModeStore = create<JobsViewModeStore>((set) => ({
  viewMode: "grid",
  setViewMode: (viewMode) => set({ viewMode }),
  toggleViewMode: () =>
    set((state) => ({
      viewMode: state.viewMode === "grid" ? "list" : "grid",
    })),
}));
