import { <PERSON><PERSON>ontainer } from "@/design-system/components";
import { useThemeClasses } from "@/design-system/utils/theme-utils";
import {
  ActionIcon,
  Avatar,
  Badge,
  Button,
  Card,
  Divider,
  Group,
  LoadingOverlay,
  Stack,
  Text,
  Title,
  Tooltip,
} from "@mantine/core";
import { useState } from "react";
import {
  FaArrowLeft,
  FaBriefcase,
  FaCalendarAlt,
  FaDownload,
  FaEnvelope,
  FaGraduationCap,
  FaMapMarkerAlt,
  FaPhoneAlt,
  FaThumbsDown,
  FaThumbsUp,
  FaUser,
} from "react-icons/fa";
import { useNavigate, useParams } from "react-router";

// Import hooks and services
import { useApplicationById } from "@/hooks/employer/useEmployerData";

export default function EmployerApplicationDetailsPage() {
  const navigate = useNavigate();
  const params = useParams();
  const applicationId = params.application_id;

  // State for managing application status updates
  const [isUpdating, setIsUpdating] = useState(false);

  // Fetch application data
  const { application, loading, error, updateStatus } = useApplicationById(applicationId);

  // Handle status updates
  const handleStatusUpdate = async (status: "approved" | "rejected") => {
    if (!application) return;
    
    setIsUpdating(true);
    try {
      await updateStatus(status);
      // Show success message or redirect
    } catch (error) {
      console.error("Failed to update application status:", error);
    } finally {
      setIsUpdating(false);
    }
  };

  // Handle navigation back
  const handleGoBack = () => {
    navigate(-1);
  };

  // Handle resume download
  const handleDownloadResume = () => {
    if (application?.resumeUrl) {
      // In a real app, this would trigger a download
      window.open(application.resumeUrl, '_blank');
    }
  };

  // Handle contact actions
  const handleSendEmail = () => {
    if (application?.candidateEmail) {
      window.location.href = `mailto:${application.candidateEmail}`;
    }
  };

  const handleCall = () => {
    if (application?.candidatePhone) {
      window.location.href = `tel:${application.candidatePhone}`;
    }
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "approved":
        return "green";
      case "rejected":
        return "red";
      case "pending":
        return "yellow";
      default:
        return "gray";
    }
  };

  // Get candidate initials
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  if (loading) {
    return (
      <PageContainer variant="employer">
        <LoadingOverlay visible />
      </PageContainer>
    );
  }

  if (error || !application) {
    return (
      <PageContainer variant="employer">
        <Card withBorder radius="md" className="p-8 text-center">
          <Title order={3} className="mb-2">
            Application Not Found
          </Title>
          <Text c="dimmed" className="mb-4">
            The application you're looking for doesn't exist or has been removed.
          </Text>
          <Button onClick={handleGoBack} leftSection={<FaArrowLeft size={16} />}>
            Go Back
          </Button>
        </Card>
      </PageContainer>
    );
  }

  return (
    <PageContainer
      breadcrumbItems={[
        { title: "Home", href: "/" },
        { title: "Manage Jobs", href: "/employer/manage-jobs" },
        { title: application.jobTitle, href: `/employer/manage-jobs/${application.jobId}/applications` },
        { title: "Application Details" },
      ]}
      variant="employer"
    >
      <div className="relative">
        <LoadingOverlay visible={isUpdating} />

        {/* Header with Back Button */}
        <Group justify="space-between" className="mb-6">
          <Button
            variant="subtle"
            leftSection={<FaArrowLeft size={16} />}
            onClick={handleGoBack}
          >
            Back to Applications
          </Button>
          
          <Group gap="sm">
            <Button
              color="green"
              leftSection={<FaThumbsUp size={16} />}
              onClick={() => handleStatusUpdate("approved")}
              disabled={application.status === "approved" || isUpdating}
            >
              Approve
            </Button>
            <Button
              color="red"
              leftSection={<FaThumbsDown size={16} />}
              onClick={() => handleStatusUpdate("rejected")}
              disabled={application.status === "rejected" || isUpdating}
            >
              Reject
            </Button>
          </Group>
        </Group>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Candidate Info */}
          <div className="lg:col-span-2 space-y-6">
            {/* Candidate Overview */}
            <Card withBorder radius="md" p="lg">
              <Group justify="space-between" align="flex-start" className="mb-4">
                <Group align="flex-start">
                  <Avatar
                    size="xl"
                    radius="xl"
                    color={getStatusColor(application.status)}
                  >
                    {getInitials(application.candidateName)}
                  </Avatar>
                  <div>
                    <Title order={2} className="mb-1">
                      {application.candidateName}
                    </Title>
                    <Text size="lg" c="dimmed" className="mb-2">
                      {application.candidateEmail}
                    </Text>
                    {application.candidatePhone && (
                      <Text size="sm" c="dimmed">
                        {application.candidatePhone}
                      </Text>
                    )}
                    {application.candidateLocation && (
                      <Group gap="xs" mt="xs">
                        <FaMapMarkerAlt size={14} className="text-gray-500" />
                        <Text size="sm" c="dimmed">
                          {application.candidateLocation}
                        </Text>
                      </Group>
                    )}
                  </div>
                </Group>
                
                <Badge
                  size="lg"
                  color={getStatusColor(application.status)}
                  variant="light"
                >
                  {application.status.charAt(0).toUpperCase() + application.status.slice(1)}
                </Badge>
              </Group>

              {/* Contact Actions */}
              <Group gap="sm">
                <Button
                  variant="light"
                  leftSection={<FaEnvelope size={16} />}
                  onClick={handleSendEmail}
                >
                  Send Email
                </Button>
                {application.candidatePhone && (
                  <Button
                    variant="light"
                    leftSection={<FaPhoneAlt size={16} />}
                    onClick={handleCall}
                  >
                    Call
                  </Button>
                )}
                {application.resumeUrl && (
                  <Button
                    variant="light"
                    leftSection={<FaDownload size={16} />}
                    onClick={handleDownloadResume}
                  >
                    Download Resume
                  </Button>
                )}
              </Group>
            </Card>

            {/* Cover Letter */}
            {application.coverLetter && (
              <Card withBorder radius="md" p="lg">
                <Title order={3} className="mb-3">
                  Cover Letter
                </Title>
                <Text className="whitespace-pre-wrap leading-relaxed">
                  {application.coverLetter}
                </Text>
              </Card>
            )}
          </div>

          {/* Right Column - Application Details */}
          <div className="space-y-6">
            {/* Application Summary */}
            <Card withBorder radius="md" p="lg">
              <Title order={3} className="mb-4">
                Application Details
              </Title>
              
              <Stack gap="md">
                <div>
                  <Group gap="xs" className="mb-1">
                    <FaBriefcase size={14} className="text-blue-500" />
                    <Text size="sm" fw={600}>
                      Position
                    </Text>
                  </Group>
                  <Text size="sm" c="dimmed" className="ml-5">
                    {application.jobTitle}
                  </Text>
                </div>

                <div>
                  <Group gap="xs" className="mb-1">
                    <FaCalendarAlt size={14} className="text-blue-500" />
                    <Text size="sm" fw={600}>
                      Applied Date
                    </Text>
                  </Group>
                  <Text size="sm" c="dimmed" className="ml-5">
                    {application.appliedDate}
                  </Text>
                </div>

                {application.experience && (
                  <div>
                    <Group gap="xs" className="mb-1">
                      <FaUser size={14} className="text-blue-500" />
                      <Text size="sm" fw={600}>
                        Experience
                      </Text>
                    </Group>
                    <Text size="sm" c="dimmed" className="ml-5">
                      {application.experience}
                    </Text>
                  </div>
                )}

                {application.education && (
                  <div>
                    <Group gap="xs" className="mb-1">
                      <FaGraduationCap size={14} className="text-blue-500" />
                      <Text size="sm" fw={600}>
                        Education
                      </Text>
                    </Group>
                    <Text size="sm" c="dimmed" className="ml-5">
                      {application.education}
                    </Text>
                  </div>
                )}
              </Stack>
            </Card>
          </div>
        </div>
      </div>
    </PageContainer>
  );
}
