import {
  Badge,
  Button,
  Divider,
  Group,
  Modal,
  Stack,
  Text,
} from "@mantine/core";
import {
  FaBriefcase,
  FaBuilding,
  FaCalendar,
  FaDollarSign,
  FaMapMarkerAlt,
  FaTags,
  FaUser,
} from "react-icons/fa";

interface JobDetailsModalProps {
  opened: boolean;
  onClose: () => void;
  jobId: number | null;
  onApprove?: () => void;
  onReject?: () => void;
}

// Mock job data
const mockJobDetails = {
  id: 1,
  title: "Software Engineer",
  company: "Tech Corp",
  category: "Technology",
  location: "San Francisco, CA",
  status: "pending",
  postedDate: "2023-10-10",
  expiryDate: "2023-11-10",
  salary: "$100,000 - $130,000",
  jobType: "Full-time",
  description:
    "We are looking for a skilled software engineer to join our team...",
  requirements:
    "- 3+ years of experience in web development\n- Proficiency in JavaScript, React, and Node.js\n- Bachelor's degree in Computer Science or related field",
  benefits: ["Health insurance", "401(k) matching", "Flexible work hours"],
  tags: ["JavaScript", "React", "Node.js", "Full Stack"],
  postedBy: "<PERSON> (HR Manager)",
};

export default function JobDetailsModal({
  opened,
  onClose,
  // jobId,
  onApprove,
  onReject,
}: JobDetailsModalProps) {
  // In a real application, you would fetch job details based on jobId
  const job = mockJobDetails;

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "active":
        return "green";
      case "pending":
        return "yellow";
      case "expired":
        return "gray";
      case "rejected":
        return "red";
      default:
        return "blue";
    }
  };

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title="Job Details"
      size="lg"
      padding="xl"
    >
      <Stack gap="md">
        {/* Title and Status */}
        <Group justify="space-between" align="center">
          <Text size="xl" fw={600}>
            {job.title}
          </Text>
          <Badge
            color={getStatusBadgeColor(job.status)}
            variant="light"
            size="lg"
          >
            {job.status}
          </Badge>
        </Group>

        <Divider />

        {/* Company and Location */}
        <Group>
          <Group>
            <FaBuilding size={16} />
            <Text>{job.company}</Text>
          </Group>
          <Group>
            <FaMapMarkerAlt size={16} />
            <Text>{job.location}</Text>
          </Group>
        </Group>

        {/* Job Details */}
        <Group>
          <Group>
            <FaBriefcase size={16} />
            <Text>{job.jobType}</Text>
          </Group>
          <Group>
            <FaDollarSign size={16} />
            <Text>{job.salary}</Text>
          </Group>
        </Group>

        {/* Dates */}
        <Group>
          <Group>
            <FaCalendar size={16} />
            <Text>Posted: {job.postedDate}</Text>
          </Group>
          <Group>
            <FaCalendar size={16} />
            <Text>Expires: {job.expiryDate}</Text>
          </Group>
        </Group>

        <Divider />

        {/* Description */}
        <div>
          <Text fw={600} mb="xs">
            Job Description
          </Text>
          <Text>{job.description}</Text>
        </div>

        {/* Requirements */}
        <div>
          <Text fw={600} mb="xs">
            Requirements
          </Text>
          <Text style={{ whiteSpace: "pre-line" }}>{job.requirements}</Text>
        </div>

        {/* Benefits */}
        <div>
          <Text fw={600} mb="xs">
            Benefits
          </Text>
          <ul className="ml-5 list-disc">
            {job.benefits.map((benefit, index) => (
              <li key={index}>
                <Text>{benefit}</Text>
              </li>
            ))}
          </ul>
        </div>

        {/* Tags */}
        <div>
          <Group align="center">
            <FaTags size={16} />
            <Text fw={600}>Tags:</Text>
            {job.tags.map((tag, index) => (
              <Badge key={index} variant="light">
                {tag}
              </Badge>
            ))}
          </Group>
        </div>

        <Divider />

        {/* Posted By */}
        <Group>
          <FaUser size={16} />
          <Text>Posted by: {job.postedBy}</Text>
        </Group>

        {/* Action Buttons for Pending Jobs */}
        {job.status === "pending" && onApprove && onReject && (
          <Group mt="md" justify="flex-end">
            <Button
              variant="outline"
              color="red"
              onClick={() => {
                onReject();
                onClose();
              }}
            >
              Reject
            </Button>
            <Button
              color="green"
              onClick={() => {
                onApprove();
                onClose();
              }}
            >
              Approve
            </Button>
          </Group>
        )}
      </Stack>
    </Modal>
  );
}
