import { useThemeClasses } from "@/design-system/utils/theme-utils";
import { <PERSON>ge, Button, Card, Group, Text } from "@mantine/core";
import {
  FaBriefcase,
  FaCalendar,
  FaDollarSign,
  FaEye,
  FaMapMarkerAlt,
  FaUsers,
} from "react-icons/fa";

interface JobCardProps {
  job: {
    id: string;
    title: string;
    company?: string;
    location: string;
    jobType: string;
    salary?: {
      min: number;
      max: number;
      currency: string;
    };
    postedDate: string;
    applicants?: number;
    status?: "active" | "paused" | "closed" | "draft";
    description?: string;
  };
  onView?: (jobId: string) => void;
  onEdit?: (jobId: string) => void;
  showActions?: boolean;
  variant?: "default" | "compact";
}

export default function JobCard({
  job,
  onView,
  onEdit,
  showActions = true,
  variant = "default",
}: JobCardProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "green";
      case "paused":
        return "yellow";
      case "closed":
        return "red";
      case "draft":
        return "gray";
      default:
        return "blue";
    }
  };

  const formatSalary = (salary: { min: number; max: number; currency: string }) => {
    return `${salary.min.toLocaleString()} - ${salary.max.toLocaleString()} ${salary.currency}`;
  };

  return (
    <Card
      shadow="sm"
      radius="md"
      withBorder
      className={useThemeClasses(
        "transition-all duration-200 hover:shadow-md",
        "transition-all duration-200 hover:shadow-dark-md",
      )}
    >
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <Text
                size={variant === "compact" ? "md" : "lg"}
                fw={600}
                className={useThemeClasses(
                  "text-gray-800",
                  "text-gray-100",
                )}
              >
                {job.title}
              </Text>
              {job.status && (
                <Badge
                  color={getStatusColor(job.status)}
                  variant="light"
                  size="sm"
                >
                  {job.status.charAt(0).toUpperCase() + job.status.slice(1)}
                </Badge>
              )}
            </div>
            {job.company && (
              <Text
                size="sm"
                className={useThemeClasses(
                  "text-gray-600",
                  "text-gray-400",
                )}
              >
                {job.company}
              </Text>
            )}
          </div>
        </div>

        {/* Job Details */}
        <div className="grid grid-cols-1 gap-3 sm:grid-cols-2">
          <div className="flex items-center gap-2">
            <FaMapMarkerAlt
              size={14}
              className={useThemeClasses(
                "text-gray-500",
                "text-gray-400",
              )}
            />
            <Text size="sm" c="dimmed">
              {job.location}
            </Text>
          </div>
          <div className="flex items-center gap-2">
            <FaBriefcase
              size={14}
              className={useThemeClasses(
                "text-gray-500",
                "text-gray-400",
              )}
            />
            <Text size="sm" c="dimmed">
              {job.jobType}
            </Text>
          </div>
          {job.salary && (
            <div className="flex items-center gap-2">
              <FaDollarSign
                size={14}
                className={useThemeClasses(
                  "text-gray-500",
                  "text-gray-400",
                )}
              />
              <Text size="sm" c="dimmed">
                {formatSalary(job.salary)}
              </Text>
            </div>
          )}
          <div className="flex items-center gap-2">
            <FaCalendar
              size={14}
              className={useThemeClasses(
                "text-gray-500",
                "text-gray-400",
              )}
            />
            <Text size="sm" c="dimmed">
              Posted {job.postedDate}
            </Text>
          </div>
        </div>

        {/* Applicants (if available) */}
        {job.applicants !== undefined && (
          <div className="flex items-center gap-2">
            <FaUsers
              size={14}
              className={useThemeClasses(
                "text-blue-500",
                "text-blue-400",
              )}
            />
            <Text size="sm" fw={500} className="text-primary-color">
              {job.applicants} applicant{job.applicants !== 1 ? "s" : ""}
            </Text>
          </div>
        )}

        {/* Description (if provided and not compact) */}
        {job.description && variant !== "compact" && (
          <Text
            size="sm"
            c="dimmed"
            lineClamp={2}
            className={useThemeClasses(
              "text-gray-600",
              "text-gray-400",
            )}
          >
            {job.description}
          </Text>
        )}

        {/* Actions */}
        {showActions && (
          <Group gap="sm" mt="md">
            {onView && (
              <Button
                variant="light"
                size="sm"
                leftSection={<FaEye size={14} />}
                onClick={() => onView(job.id)}
                className="flex-1"
              >
                View Details
              </Button>
            )}
            {onEdit && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onEdit(job.id)}
                className="flex-1"
              >
                Edit
              </Button>
            )}
          </Group>
        )}
      </div>
    </Card>
  );
}
