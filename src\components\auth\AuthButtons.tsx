import { URLS } from "@/utils/urls";
import { But<PERSON> } from "@mantine/core";
import { FaLock, FaUserPlus } from "react-icons/fa";
import { Link } from "react-router";

export default function AuthButtons() {
  return (
    <div className="flex items-center gap-3">
      {/* Desktop login button */}
      <Button
        component={Link}
        to={URLS.auth.login}
        variant="outline"
        radius="xl"
        leftSection={<FaLock size={14} />}
        className="hidden border-gray-300 bg-white px-4 text-gray-700 transition-all hover:border-gray-400 hover:bg-gray-50 sm:flex"
      >
        Log in
      </Button>

      {/* Mobile login button */}
      <Button
        component={Link}
        to={URLS.auth.login}
        variant="outline"
        radius="xl"
        className="border-gray-300 bg-white p-2 text-gray-700 transition-all hover:border-gray-400 hover:bg-gray-50 sm:hidden"
        aria-label="Log in"
      >
        <FaLock size={14} />
      </Button>

      {/* Desktop signup button */}
      <Button
        component={Link}
        to={URLS.auth.register}
        radius="xl"
        leftSection={<FaUserPlus size={14} />}
        className="bg-primary-color hover:bg-primary-color/90 hidden px-4 transition-all sm:flex"
      >
        Sign Up
      </Button>

      {/* Mobile signup button */}
      <Button
        component={Link}
        to={URLS.auth.register}
        radius="xl"
        className="bg-primary-color hover:bg-primary-color/90 p-2 transition-all sm:hidden"
        aria-label="Sign Up"
      >
        <FaUserPlus size={14} />
      </Button>
    </div>
  );
}
