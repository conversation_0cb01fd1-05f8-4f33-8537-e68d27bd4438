export interface Application {
  id: number;
  jobTitle: string;
  company: string;
  appliedDate: string;
  status: "pending" | "reviewed" | "rejected" | "accepted";
  jobType: string;
  location: string;
}

export const mockApplications: Application[] = [
  {
    id: 1,
    jobTitle: "Senior Full Stack Developer",
    company: "TechCorp Solutions",
    appliedDate: "2024-01-15",
    status: "pending",
    jobType: "Full-time",
    location: "San Francisco, CA",
  },
  {
    id: 2,
    jobTitle: "Frontend Developer",
    company: "Digital Innovations",
    appliedDate: "2024-01-10",
    status: "reviewed",
    jobType: "Remote",
    location: "New York, NY",
  },
  {
    id: 3,
    jobTitle: "React Developer",
    company: "WebTech Inc",
    appliedDate: "2024-01-05",
    status: "accepted",
    jobType: "Contract",
    location: "Austin, TX",
  },
  {
    id: 4,
    jobTitle: "Software Engineer",
    company: "Innovation Labs",
    appliedDate: "2024-01-01",
    status: "rejected",
    jobType: "Full-time",
    location: "Seattle, WA",
  },
];
