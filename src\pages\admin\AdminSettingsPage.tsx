import AdminNavbar from "@/components/admin/AdminNavbar";
import PageContainer from "@/design-system/components/layout/PageContainer";
import PageHeading from "@/design-system/components/typography/PageHeading";
import {
  Avatar,
  Badge,
  Button,
  Card,
  ColorInput,
  FileInput,
  Group,
  Paper,
  Select,
  SimpleGrid,
  Stack,
  Switch,
  Tabs,
  Text,
  TextInput,
  Textarea,
} from "@mantine/core";
import { useState } from "react";
import {
  FaBell,
  FaCog,
  FaDatabase,
  FaEnvelope,
  FaGlobe,
  FaPalette,
  FaSave,
  FaShieldAlt,
  FaUpload,
} from "react-icons/fa";

export default function AdminSettingsPage() {
  const [activeTab, setActiveTab] = useState<string | null>("general");

  return (
    <>
      <AdminNavbar />
      <PageContainer variant="admin">
        {/* Page Title */}
        <PageHeading
          title="Admin Settings"
          subtitle="Configure system settings and preferences"
          variant="admin"
        />

        <div className="mt-6">
          <Card shadow="sm" padding="md" radius="md" withBorder>
            <Tabs
              value={activeTab}
              onChange={setActiveTab}
              orientation="horizontal"
            >
              <Tabs.List>
                <Tabs.Tab value="general" leftSection={<FaCog size={14} />}>
                  General
                </Tabs.Tab>
                <Tabs.Tab
                  value="appearance"
                  leftSection={<FaPalette size={14} />}
                >
                  Appearance
                </Tabs.Tab>
                <Tabs.Tab
                  value="security"
                  leftSection={<FaShieldAlt size={14} />}
                >
                  Security
                </Tabs.Tab>
                <Tabs.Tab
                  value="notifications"
                  leftSection={<FaBell size={14} />}
                >
                  Notifications
                </Tabs.Tab>
                <Tabs.Tab
                  value="integrations"
                  leftSection={<FaGlobe size={14} />}
                >
                  Integrations
                </Tabs.Tab>
                <Tabs.Tab value="backup" leftSection={<FaDatabase size={14} />}>
                  Backup & Restore
                </Tabs.Tab>
              </Tabs.List>

              <Tabs.Panel value="general" pt="xl">
                <Stack gap="lg">
                  <Text fw={600} size="lg">
                    General Settings
                  </Text>

                  <SimpleGrid cols={{ base: 1, md: 2 }} spacing="lg">
                    <div>
                      <Text fw={500} mb="xs">
                        Site Information
                      </Text>
                      <Paper withBorder p="md" radius="md">
                        <Stack gap="md">
                          <TextInput
                            label="Site Name"
                            placeholder="JobNest"
                            defaultValue="JobNest"
                          />
                          <TextInput
                            label="Site URL"
                            placeholder="https://jobnest.com"
                            defaultValue="https://jobnest.com"
                          />
                          <Textarea
                            label="Site Description"
                            placeholder="A job board platform connecting employers and candidates"
                            defaultValue="A job board platform connecting employers and candidates"
                            minRows={3}
                          />
                        </Stack>
                      </Paper>
                    </div>

                    <div>
                      <Text fw={500} mb="xs">
                        Contact Information
                      </Text>
                      <Paper withBorder p="md" radius="md">
                        <Stack gap="md">
                          <TextInput
                            label="Admin Email"
                            placeholder="<EMAIL>"
                            defaultValue="<EMAIL>"
                            leftSection={<FaEnvelope size={14} />}
                          />
                          <TextInput
                            label="Support Email"
                            placeholder="<EMAIL>"
                            defaultValue="<EMAIL>"
                            leftSection={<FaEnvelope size={14} />}
                          />
                          <TextInput
                            label="Phone Number"
                            placeholder="+****************"
                            defaultValue="+****************"
                          />
                        </Stack>
                      </Paper>
                    </div>
                  </SimpleGrid>

                  <div>
                    <Text fw={500} mb="xs">
                      Regional Settings
                    </Text>
                    <Paper withBorder p="md" radius="md">
                      <SimpleGrid cols={{ base: 1, md: 3 }} spacing="lg">
                        <Select
                          label="Default Language"
                          placeholder="Select language"
                          defaultValue="en"
                          data={[
                            { value: "en", label: "English" },
                            { value: "es", label: "Spanish" },
                            { value: "fr", label: "French" },
                            { value: "de", label: "German" },
                          ]}
                        />
                        <Select
                          label="Default Timezone"
                          placeholder="Select timezone"
                          defaultValue="utc"
                          data={[
                            { value: "utc", label: "UTC" },
                            { value: "est", label: "Eastern Time (EST)" },
                            { value: "cst", label: "Central Time (CST)" },
                            { value: "pst", label: "Pacific Time (PST)" },
                          ]}
                        />
                        <Select
                          label="Date Format"
                          placeholder="Select date format"
                          defaultValue="mdy"
                          data={[
                            { value: "mdy", label: "MM/DD/YYYY" },
                            { value: "dmy", label: "DD/MM/YYYY" },
                            { value: "ymd", label: "YYYY/MM/DD" },
                          ]}
                        />
                      </SimpleGrid>
                    </Paper>
                  </div>

                  <Group justify="flex-end" mt="md">
                    <Button variant="default">Cancel</Button>
                    <Button
                      leftSection={<FaSave size={14} />}
                      className="bg-primary-color hover:bg-primary-color/90"
                    >
                      Save Changes
                    </Button>
                  </Group>
                </Stack>
              </Tabs.Panel>

              <Tabs.Panel value="appearance" pt="xl">
                <Stack gap="lg">
                  <Text fw={600} size="lg">
                    Appearance Settings
                  </Text>

                  <SimpleGrid cols={{ base: 1, md: 2 }} spacing="lg">
                    <div>
                      <Text fw={500} mb="xs">
                        Theme Colors
                      </Text>
                      <Paper withBorder p="md" radius="md">
                        <Stack gap="md">
                          <ColorInput
                            label="Primary Color"
                            placeholder="Pick a color"
                            defaultValue="#3b82f6"
                            format="hex"
                          />
                          <ColorInput
                            label="Secondary Color"
                            placeholder="Pick a color"
                            defaultValue="#10b981"
                            format="hex"
                          />
                          <ColorInput
                            label="Accent Color"
                            placeholder="Pick a color"
                            defaultValue="#8b5cf6"
                            format="hex"
                          />
                        </Stack>
                      </Paper>
                    </div>

                    <div>
                      <Text fw={500} mb="xs">
                        Logo & Favicon
                      </Text>
                      <Paper withBorder p="md" radius="md">
                        <Stack gap="md">
                          <FileInput
                            label="Site Logo"
                            placeholder="Upload logo"
                            accept="image/png,image/jpeg,image/svg+xml"
                            leftSection={<FaUpload size={14} />}
                          />
                          <FileInput
                            label="Site Favicon"
                            placeholder="Upload favicon"
                            accept="image/png,image/x-icon,image/svg+xml"
                            leftSection={<FaUpload size={14} />}
                          />
                          <Group>
                            <Avatar src="/logo.png" size="lg" />
                            <Text size="sm" c="dimmed">
                              Current logo
                            </Text>
                          </Group>
                        </Stack>
                      </Paper>
                    </div>
                  </SimpleGrid>

                  <div>
                    <Text fw={500} mb="xs">
                      Display Options
                    </Text>
                    <Paper withBorder p="md" radius="md">
                      <SimpleGrid cols={{ base: 1, md: 2 }} spacing="lg">
                        <Stack gap="md">
                          <Switch
                            label="Enable Dark Mode"
                            description="Allow users to switch to dark mode"
                            defaultChecked
                          />
                          <Switch
                            label="Show Job Count"
                            description="Display job count on category pages"
                            defaultChecked
                          />
                          <Switch
                            label="Show Company Logos"
                            description="Display company logos in job listings"
                            defaultChecked
                          />
                        </Stack>
                        <Stack gap="md">
                          <Switch
                            label="Enable Animations"
                            description="Use animations throughout the site"
                            defaultChecked
                          />
                          <Switch
                            label="Compact Layout"
                            description="Use a more compact layout for tables"
                          />
                          <Switch
                            label="Show Social Share Buttons"
                            description="Display social sharing options"
                            defaultChecked
                          />
                        </Stack>
                      </SimpleGrid>
                    </Paper>
                  </div>

                  <Group justify="flex-end" mt="md">
                    <Button variant="default">Reset to Default</Button>
                    <Button
                      leftSection={<FaSave size={14} />}
                      className="bg-primary-color hover:bg-primary-color/90"
                    >
                      Save Changes
                    </Button>
                  </Group>
                </Stack>
              </Tabs.Panel>

              <Tabs.Panel value="security" pt="xl">
                <Text fw={600} size="lg" mb="lg">
                  Security Settings
                </Text>
                <SimpleGrid cols={{ base: 1, md: 2 }} spacing="lg">
                  <Paper withBorder p="md" radius="md">
                    <Text fw={500} mb="md">
                      Authentication
                    </Text>
                    <Stack gap="md">
                      <Switch
                        label="Two-Factor Authentication"
                        description="Require 2FA for admin accounts"
                        defaultChecked
                      />
                      <Switch
                        label="CAPTCHA on Forms"
                        description="Enable CAPTCHA on public forms"
                        defaultChecked
                      />
                      <Switch
                        label="Login Throttling"
                        description="Limit login attempts"
                        defaultChecked
                      />
                      <Select
                        label="Session Timeout"
                        description="Automatically log out after inactivity"
                        defaultValue="30"
                        data={[
                          { value: "15", label: "15 minutes" },
                          { value: "30", label: "30 minutes" },
                          { value: "60", label: "1 hour" },
                          { value: "120", label: "2 hours" },
                        ]}
                      />
                    </Stack>
                  </Paper>

                  <Paper withBorder p="md" radius="md">
                    <Text fw={500} mb="md">
                      Password Policy
                    </Text>
                    <Stack gap="md">
                      <Switch
                        label="Require Strong Passwords"
                        description="Enforce password complexity rules"
                        defaultChecked
                      />
                      <Select
                        label="Minimum Password Length"
                        defaultValue="8"
                        data={[
                          { value: "6", label: "6 characters" },
                          { value: "8", label: "8 characters" },
                          { value: "10", label: "10 characters" },
                          { value: "12", label: "12 characters" },
                        ]}
                      />
                      <Select
                        label="Password Expiry"
                        defaultValue="90"
                        data={[
                          { value: "30", label: "30 days" },
                          { value: "60", label: "60 days" },
                          { value: "90", label: "90 days" },
                          { value: "never", label: "Never" },
                        ]}
                      />
                      <Switch
                        label="Prevent Password Reuse"
                        description="Disallow using previous passwords"
                        defaultChecked
                      />
                    </Stack>
                  </Paper>
                </SimpleGrid>

                <Group justify="flex-end" mt="xl">
                  <Button variant="default">Cancel</Button>
                  <Button
                    leftSection={<FaSave size={14} />}
                    className="bg-primary-color hover:bg-primary-color/90"
                  >
                    Save Changes
                  </Button>
                </Group>
              </Tabs.Panel>

              <Tabs.Panel value="notifications" pt="xl">
                <Text fw={600} size="lg" mb="lg">
                  Notification Settings
                </Text>
                <Paper withBorder p="md" radius="md" mb="lg">
                  <Text fw={500} mb="md">
                    Email Notifications
                  </Text>
                  <SimpleGrid cols={{ base: 1, md: 2 }} spacing="lg">
                    <Stack gap="md">
                      <Switch
                        label="New User Registration"
                        description="Send email when a new user registers"
                        defaultChecked
                      />
                      <Switch
                        label="New Job Posting"
                        description="Send email when a new job is posted"
                        defaultChecked
                      />
                      <Switch
                        label="New Company Registration"
                        description="Send email when a new company registers"
                        defaultChecked
                      />
                    </Stack>
                    <Stack gap="md">
                      <Switch
                        label="Job Application"
                        description="Send email when a candidate applies for a job"
                        defaultChecked
                      />
                      <Switch
                        label="Contact Form Submission"
                        description="Send email when contact form is submitted"
                        defaultChecked
                      />
                      <Switch
                        label="System Alerts"
                        description="Send email for system alerts and errors"
                        defaultChecked
                      />
                    </Stack>
                  </SimpleGrid>
                </Paper>

                <Group justify="flex-end" mt="md">
                  <Button variant="default">Reset to Default</Button>
                  <Button
                    leftSection={<FaSave size={14} />}
                    className="bg-primary-color hover:bg-primary-color/90"
                  >
                    Save Changes
                  </Button>
                </Group>
              </Tabs.Panel>

              <Tabs.Panel value="integrations" pt="xl">
                <Text fw={600} size="lg" mb="lg">
                  Integration Settings
                </Text>
                <SimpleGrid cols={{ base: 1, md: 2 }} spacing="lg">
                  <Paper withBorder p="md" radius="md">
                    <Group justify="space-between" mb="md">
                      <div>
                        <Text fw={500}>Google Analytics</Text>
                        <Text size="sm" c="dimmed">
                          Track website traffic and user behavior
                        </Text>
                      </div>
                      <Badge color="green">Connected</Badge>
                    </Group>
                    <TextInput
                      label="Tracking ID"
                      placeholder="UA-XXXXXXXXX-X"
                      defaultValue="UA-123456789-1"
                    />
                    <Group mt="md">
                      <Button variant="light" size="xs" color="red">
                        Disconnect
                      </Button>
                      <Button variant="light" size="xs">
                        Configure
                      </Button>
                    </Group>
                  </Paper>

                  <Paper withBorder p="md" radius="md">
                    <Group justify="space-between" mb="md">
                      <div>
                        <Text fw={500}>Mailchimp</Text>
                        <Text size="sm" c="dimmed">
                          Email marketing automation
                        </Text>
                      </div>
                      <Badge color="red">Not Connected</Badge>
                    </Group>
                    <TextInput label="API Key" placeholder="Enter API key" />
                    <Group mt="md">
                      <Button
                        variant="light"
                        size="xs"
                        className="bg-primary-color hover:bg-primary-color/90 text-white"
                      >
                        Connect
                      </Button>
                    </Group>
                  </Paper>
                </SimpleGrid>

                <Group justify="flex-end" mt="xl">
                  <Button variant="default">Cancel</Button>
                  <Button
                    leftSection={<FaSave size={14} />}
                    className="bg-primary-color hover:bg-primary-color/90"
                  >
                    Save Changes
                  </Button>
                </Group>
              </Tabs.Panel>

              <Tabs.Panel value="backup" pt="xl">
                <Text fw={600} size="lg" mb="lg">
                  Backup & Restore
                </Text>
                <SimpleGrid cols={{ base: 1, md: 2 }} spacing="lg">
                  <Paper withBorder p="md" radius="md">
                    <Text fw={500} mb="md">
                      Database Backup
                    </Text>
                    <Stack gap="md">
                      <Select
                        label="Backup Frequency"
                        defaultValue="daily"
                        data={[
                          { value: "hourly", label: "Hourly" },
                          { value: "daily", label: "Daily" },
                          { value: "weekly", label: "Weekly" },
                          { value: "monthly", label: "Monthly" },
                        ]}
                      />
                      <Select
                        label="Backup Retention"
                        defaultValue="7"
                        data={[
                          { value: "3", label: "3 days" },
                          { value: "7", label: "7 days" },
                          { value: "14", label: "14 days" },
                          { value: "30", label: "30 days" },
                        ]}
                      />
                      <Group>
                        <Button leftSection={<FaDatabase size={14} />}>
                          Create Backup Now
                        </Button>
                      </Group>
                    </Stack>
                  </Paper>

                  <Paper withBorder p="md" radius="md">
                    <Text fw={500} mb="md">
                      Restore Database
                    </Text>
                    <Stack gap="md">
                      <FileInput
                        label="Upload Backup File"
                        placeholder="Choose backup file"
                        accept=".sql,.zip"
                        leftSection={<FaUpload size={14} />}
                      />
                      <Text size="xs" c="dimmed">
                        Warning: Restoring a backup will overwrite all current
                        data. This action cannot be undone.
                      </Text>
                      <Group>
                        <Button
                          color="red"
                          leftSection={<FaDatabase size={14} />}
                        >
                          Restore from Backup
                        </Button>
                      </Group>
                    </Stack>
                  </Paper>
                </SimpleGrid>
              </Tabs.Panel>
            </Tabs>
          </Card>
        </div>
      </PageContainer>
    </>
  );
}
