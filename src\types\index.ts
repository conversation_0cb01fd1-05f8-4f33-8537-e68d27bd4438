export interface Candidate {
  id: number;
  name: string;
  email: string;
  status: string;
  jobId: number;
  appliedDate: string;
  skills?: string[];
}

export interface Job {
  id: number;
  title: string;
  company: string;
  location: string;
  type: string;
  workType: string;
  salary: string;
  experience: string;
  skills: string[];
  datePosted: string;
  careerLevel: string;
  category: string;
  description: string;
  status?: "Active" | "Pending" | "Closed";
}
