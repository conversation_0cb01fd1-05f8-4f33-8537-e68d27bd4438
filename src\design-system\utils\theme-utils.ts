import { useMantineColorScheme } from "@mantine/core";

/**
 * A utility hook that returns classes based on the current color scheme
 * @param lightClasses - Classes to apply in light mode
 * @param darkClasses - Classes to apply in dark mode
 * @returns The appropriate classes based on the current color scheme
 */
export function useThemeClasses(
  lightClasses: string,
  darkClasses: string,
): string {
  const { colorScheme } = useMantineColorScheme();
  return colorScheme === "dark" ? darkClasses : lightClasses;
}

/**
 * A utility function that returns a class string based on the color scheme
 * @param baseClasses - Classes that apply to both light and dark modes
 * @param lightClasses - Classes to apply only in light mode
 * @param darkClasses - Classes to apply only in dark mode
 * @param colorScheme - The current color scheme ("light" or "dark")
 * @returns A combined class string
 */
export function getThemeClasses(
  baseClasses: string,
  lightClasses: string,
  darkClasses: string,
  colorScheme: "light" | "dark",
): string {
  const themeSpecificClasses =
    colorScheme === "dark" ? darkClasses : lightClasses;
  return `${baseClasses} ${themeSpecificClasses}`.trim();
}
