import { useUserActionsStore } from "@/stores/admin-store";
import { useDeleteUserModal } from "@/stores/modal-store";
import { Button, Group, Modal, Text } from "@mantine/core";

interface DeleteUserModalProps {
  onConfirm: () => void;
  userId?: number | null;
}

export default function DeleteUserModal({
  onConfirm,
  // userId,
}: DeleteUserModalProps) {
  const { close, isOpen } = useDeleteUserModal();

  const closeModal = () => {
    close();
  };

  // Get the delete callback from the store
  const deleteUserCallback = useUserActionsStore(
    (state) => state.deleteUserCallback,
  );

  return (
    <Modal
      opened={isOpen}
      onClose={closeModal}
      title="Confirm Deletion"
      centered
    >
      <Text>
        Are you sure you want to delete this user? This action cannot be undone.
      </Text>
      <Group mt="md" justify="flex-end">
        <Button variant="outline" onClick={closeModal}>
          Cancel
        </Button>
        <Button
          color="red"
          onClick={() => {
            // Call the provided onConfirm prop first
            onConfirm();

            // Then call the callback from the atom if it exists
            if (deleteUserCallback) {
              deleteUserCallback();
            }

            closeModal();
          }}
        >
          Delete
        </Button>
      </Group>
    </Modal>
  );
}
