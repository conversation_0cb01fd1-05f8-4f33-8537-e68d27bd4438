import { useThemeClasses } from "@/design-system/utils/theme-utils";
import { Card, Group, Loader, Skeleton, Stack, Text } from "@mantine/core";

interface LoadingStateProps {
  variant?: "page" | "card" | "inline" | "skeleton";
  message?: string;
  size?: "sm" | "md" | "lg";
  count?: number; // For skeleton variant
}

export default function LoadingState({
  variant = "card",
  message = "Loading...",
  size = "md",
  count = 3,
}: LoadingStateProps) {
  // Always call hooks at the top level
  const cardClasses = useThemeClasses(
    "bg-white shadow-sm",
    "bg-dark-6 shadow-dark-lg",
  );

  const sizeMap = {
    sm: { loader: "sm", text: "sm" },
    md: { loader: "md", text: "md" },
    lg: { loader: "lg", text: "lg" },
  };

  if (variant === "skeleton") {
    return (
      <Stack gap="md">
        {Array.from({ length: count }).map((_, index) => (
          <Card key={index} withBorder radius="md" p="md">
            <Group gap="md">
              <Skeleton height={40} width={40} radius="md" />
              <div className="flex-1">
                <Skeleton height={16} width="60%" mb="xs" />
                <Skeleton height={12} width="40%" />
              </div>
            </Group>
          </Card>
        ))}
      </Stack>
    );
  }

  if (variant === "inline") {
    return (
      <Group gap="sm" justify="center">
        <Loader size={sizeMap[size].loader} />
        <Text size={sizeMap[size].text} c="dimmed">
          {message}
        </Text>
      </Group>
    );
  }

  if (variant === "page") {
    return (
      <div className="flex min-h-96 items-center justify-center">
        <Stack align="center" gap="md">
          <Loader size={sizeMap[size].loader} />
          <Text size={sizeMap[size].text} c="dimmed">
            {message}
          </Text>
        </Stack>
      </div>
    );
  }

  // Default card variant
  return (
    <Card withBorder radius="md" className={cardClasses} p="xl">
      <Stack align="center" gap="md">
        <Loader size={sizeMap[size].loader} />
        <Text size={sizeMap[size].text} c="dimmed" ta="center">
          {message}
        </Text>
      </Stack>
    </Card>
  );
}
