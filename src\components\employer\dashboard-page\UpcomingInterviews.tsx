import { Avatar, Badge, Button, Group, Text } from "@mantine/core";
import { FaCalendarAlt, FaClock, FaVideo } from "react-icons/fa";

const upcomingInterviews = [
  {
    id: 1,
    candidateName: "<PERSON>",
    jobTitle: "Software Engineer",
    date: "2023-11-15",
    time: "10:00 AM",
    type: "Video Call",
    status: "Confirmed",
  },
  {
    id: 2,
    candidateName: "<PERSON>",
    jobTitle: "Product Manager",
    date: "2023-11-16",
    time: "2:30 PM",
    type: "Video Call",
    status: "Pending",
  },
  {
    id: 3,
    candidateName: "<PERSON>",
    jobTitle: "UX Designer",
    date: "2023-11-18",
    time: "11:15 AM",
    type: "In-person",
    status: "Confirmed",
  },
];

export default function UpcomingInterviews() {
  return (
    <div>
      {upcomingInterviews.map((interview) => (
        <div key={interview.id} className="mb-4 border-b pb-4">
          <Group justify="space-between" wrap="nowrap" className="mb-2">
            <Group gap="sm" wrap="nowrap">
              <Avatar color="blue" radius="xl">
                {interview.candidateName
                  .split(" ")
                  .map((n) => n[0])
                  .join("")}
              </Avatar>
              <div>
                <Text fw={600}>{interview.candidateName}</Text>
                <Text size="sm" c="dimmed">
                  {interview.jobTitle}
                </Text>
              </div>
            </Group>
            <Badge
              color={interview.status === "Confirmed" ? "green" : "yellow"}
            >
              {interview.status}
            </Badge>
          </Group>

          <Group className="mt-2 ml-12">
            <Group gap="xs">
              <FaCalendarAlt size={14} className="text-blue-500" />
              <Text size="sm">{interview.date}</Text>
            </Group>
            <Group gap="xs">
              <FaClock size={14} className="text-blue-500" />
              <Text size="sm">{interview.time}</Text>
            </Group>
            <Group gap="xs">
              <FaVideo size={14} className="text-blue-500" />
              <Text size="sm">{interview.type}</Text>
            </Group>
          </Group>

          <Group className="mt-3 ml-12">
            <Button variant="light" size="xs">
              Reschedule
            </Button>
            <Button variant="outline" size="xs">
              View Details
            </Button>
            {interview.type === "Video Call" && (
              <Button variant="filled" size="xs">
                Join Call
              </Button>
            )}
          </Group>
        </div>
      ))}
    </div>
  );
}
