import SectionHeading from "@/design-system/components/typography/SectionHeading";
import {
  Badge,
  Button,
  Checkbox,
  Divider,
  Group,
  Select,
  Stack,
  Text,
  TextInput,
} from "@mantine/core";
import { DatePickerInput } from "@mantine/dates";
import { type UseFormReturnType } from "@mantine/form";
import {
  FaCalendarAlt,
  FaFilter,
  FaSearch,
  FaSort,
  FaTrash,
} from "react-icons/fa";

interface UserFiltersProps {
  form: UseFormReturnType<any>;
  onSubmit: () => void;
  onReset: () => void;
}

export default function UserFilters({
  form,
  onSubmit,
  onReset,
}: UserFiltersProps) {
  return (
    <>
      <SectionHeading variant="admin" className="mb-4">
        <Group gap="xs">
          <FaFilter size={16} />
          <span>Filters</span>
        </Group>
      </SectionHeading>

      <form onSubmit={form.onSubmit(onSubmit)}>
        <Stack gap="md">
          <TextInput
            label="Search Users"
            placeholder="Name, email or keyword"
            leftSection={<FaSearch size={14} />}
            {...form.getInputProps("search")}
          />

          <Divider label="User Type" labelPosition="center" />

          <div>
            <Text size="sm" fw={500} mb="xs">
              Role
            </Text>
            <Group gap="xs" mb="xs">
              <Checkbox
                label={
                  <Group gap="xs">
                    <span>Candidates</span>
                    <Badge size="xs" variant="light" color="blue">
                      180
                    </Badge>
                  </Group>
                }
                checked={form.values.role === "candidate"}
                onChange={() =>
                  form.setFieldValue(
                    "role",
                    form.values.role === "candidate" ? "all" : "candidate",
                  )
                }
              />
            </Group>
            <Group gap="xs" mb="xs">
              <Checkbox
                label={
                  <Group gap="xs">
                    <span>Employers</span>
                    <Badge size="xs" variant="light" color="green">
                      45
                    </Badge>
                  </Group>
                }
                checked={form.values.role === "employer"}
                onChange={() =>
                  form.setFieldValue(
                    "role",
                    form.values.role === "employer" ? "all" : "employer",
                  )
                }
              />
            </Group>
            <Group gap="xs">
              <Checkbox
                label={
                  <Group gap="xs">
                    <span>Admins</span>
                    <Badge size="xs" variant="light" color="purple">
                      25
                    </Badge>
                  </Group>
                }
                checked={form.values.role === "admin"}
                onChange={() =>
                  form.setFieldValue(
                    "role",
                    form.values.role === "admin" ? "all" : "admin",
                  )
                }
              />
            </Group>
          </div>

          <Divider label="Status" labelPosition="center" />

          <Select
            label="User Status"
            placeholder="Select user status"
            data={[
              { label: "All Users", value: "all" },
              { label: "Active Users", value: "active" },
              { label: "Inactive Users", value: "inactive" },
              { label: "Blocked Users", value: "blocked" },
            ]}
            clearable={false}
            searchable
            value={form.values.status}
            onChange={(value) => form.setFieldValue("status", value || "all")}
          />

          <Divider label="Registration Date" labelPosition="center" />

          <DatePickerInput
            type="range"
            label="Select date range"
            placeholder="Pick dates range"
            valueFormat="YYYY-MM-DD"
            clearable
            leftSection={<FaCalendarAlt size={14} />}
          />

          <Divider label="Sorting" labelPosition="center" />

          <Select
            label="Sort By"
            placeholder="Select field"
            leftSection={<FaSort size={14} />}
            data={[
              { value: "name", label: "Name" },
              { value: "email", label: "Email" },
              { value: "role", label: "Role" },
              { value: "createdAt", label: "Registration Date" },
            ]}
            {...form.getInputProps("sortBy")}
          />

          <Select
            label="Sort Order"
            placeholder="Select sort direction"
            data={[
              { label: "Ascending (A-Z, Oldest first)", value: "asc" },
              { label: "Descending (Z-A, Newest first)", value: "desc" },
            ]}
            clearable={false}
            value={form.values.sortOrder}
            onChange={(value) =>
              form.setFieldValue("sortOrder", value || "asc")
            }
          />

          <Divider />

          <Group justify="space-between">
            <Button
              variant="subtle"
              color="red"
              onClick={onReset}
              leftSection={<FaTrash size={14} />}
            >
              Clear All
            </Button>
            <Button
              type="submit"
              leftSection={<FaFilter size={14} />}
              className="bg-primary-color hover:bg-primary-color/90"
            >
              Apply Filters
            </Button>
          </Group>
        </Stack>
      </form>
    </>
  );
}
