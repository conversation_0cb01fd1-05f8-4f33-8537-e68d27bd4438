import { type Application } from "@/data/applications";
import { useThemeClasses } from "@/design-system/utils/theme-utils";
import {
  Badge,
  Button,
  Card,
  Divider,
  Group,
  Modal,
  Progress,
  Stack,
  Text,
  Timeline,
  Title,
  useMantineColorScheme,
} from "@mantine/core";
import {
  BiArrowBack,
  BiBriefcase,
  BiBuildings,
  BiCalendar,
  BiCheckCircle,
  BiEnvelope,
  BiFile,
  BiMap,
  BiTime,
  BiUserCheck,
  BiX,
} from "react-icons/bi";

interface TrackingModalProps {
  application: Application;
  opened: boolean;
  onClose: () => void;
}

export function TrackingModal({
  application,
  opened,
  onClose,
}: TrackingModalProps) {
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  // Get color based on status
  const getStatusColor = (status: string) => {
    const colors = {
      pending: "yellow",
      reviewed: "blue",
      rejected: "red",
      accepted: "green",
    };
    return colors[status as keyof typeof colors];
  };

  // Get icon based on status
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending":
        return <BiTime size={20} />;
      case "reviewed":
        return <BiFile size={20} />;
      case "accepted":
        return <BiCheckCircle size={20} />;
      case "rejected":
        return <BiX size={20} />;
      default:
        return null;
    }
  };

  // Calculate application progress percentage
  const getProgressPercentage = () => {
    switch (application.status) {
      case "pending":
        return 25;
      case "reviewed":
        return 50;
      case "accepted":
      case "rejected":
        return 100;
      default:
        return 0;
    }
  };

  const getTimelineItems = () => {
    const items = [
      {
        icon: <BiEnvelope />,
        title: "Application Submitted",
        date: application.appliedDate,
        active: true,
        color: "blue",
        description:
          "Your application has been successfully submitted to the employer.",
      },
      {
        icon: <BiFile />,
        title: "Resume Review",
        date: "2024-01-16",
        active: ["reviewed", "accepted", "rejected"].includes(
          application.status,
        ),
        color: "yellow",
        description:
          "The employer is reviewing your resume and application materials.",
      },
      {
        icon: <BiUserCheck />,
        title: "Interview Process",
        date: "2024-01-20",
        active: application.status === "accepted",
        color: "green",
        description:
          application.status === "rejected"
            ? "Your application did not proceed to the interview stage."
            : "You've been selected for an interview with the hiring team.",
      },
      {
        icon: <BiCheckCircle />,
        title: "Final Decision",
        date: "2024-01-25",
        active: ["accepted", "rejected"].includes(application.status),
        color: application.status === "accepted" ? "green" : "red",
        description:
          application.status === "accepted"
            ? "Congratulations! Your application has been accepted."
            : application.status === "rejected"
              ? "Unfortunately, your application was not selected for this position."
              : "The employer will make a final decision after the interview process.",
      },
    ];

    return items;
  };

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      size="xl"
      title={
        <Group>
          <Button
            variant="subtle"
            leftSection={<BiArrowBack />}
            onClick={onClose}
            px={0}
          >
            Back
          </Button>
          <Title
            order={3}
            className={useThemeClasses("text-gray-900", "text-gray-100")}
          >
            Application Details
          </Title>
        </Group>
      }
      centered
    >
      <Stack gap="xl">
        {/* Header with job info */}
        <Card
          withBorder
          radius="md"
          className={useThemeClasses("bg-gray-50", "bg-dark-6")}
        >
          <Group justify="space-between" className="mb-3">
            <div className="flex items-center gap-3">
              <div
                className={`flex h-12 w-12 items-center justify-center rounded-full ${
                  isDark
                    ? `bg-${getStatusColor(application.status)}-900/30 text-${getStatusColor(application.status)}-400`
                    : `bg-${getStatusColor(application.status)}-100 text-${getStatusColor(application.status)}-500`
                }`}
              >
                {getStatusIcon(application.status)}
              </div>
              <div>
                <Title
                  order={3}
                  className={useThemeClasses("text-gray-900", "text-gray-100")}
                >
                  {application.jobTitle}
                </Title>
                <Group gap="xs">
                  <BiBuildings
                    className={useThemeClasses(
                      "text-gray-500",
                      "text-gray-400",
                    )}
                  />
                  <Text
                    className={useThemeClasses(
                      "text-gray-700",
                      "text-gray-300",
                    )}
                  >
                    {application.company}
                  </Text>
                </Group>
              </div>
            </div>
            <Badge
              size="xl"
              radius="md"
              color={getStatusColor(application.status)}
              className="px-4 py-2"
            >
              {application.status.toUpperCase()}
            </Badge>
          </Group>

          <Divider className="my-4" />

          <Group
            grow
            className={useThemeClasses("text-gray-600", "text-gray-400")}
          >
            <Group gap="xs">
              <BiCalendar size={18} />
              <Text>
                Applied:{" "}
                {new Date(application.appliedDate).toLocaleDateString("en-US", {
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                })}
              </Text>
            </Group>
            <Group gap="xs">
              <BiMap size={18} />
              <Text>{application.location}</Text>
            </Group>
            <Group gap="xs">
              <BiBriefcase size={18} />
              <Text>{application.jobType}</Text>
            </Group>
          </Group>
        </Card>

        {/* Progress bar */}
        <div>
          <Group justify="space-between" className="mb-2">
            <Text
              fw={500}
              className={useThemeClasses("text-gray-900", "text-gray-100")}
            >
              Application Progress
            </Text>
            <Text size="sm" c="dimmed">
              {getProgressPercentage()}%
            </Text>
          </Group>
          <Progress
            value={getProgressPercentage()}
            color={getStatusColor(application.status)}
            size="xl"
            radius="xl"
            striped
            animated={application.status !== "rejected"}
          />
        </div>

        {/* Timeline */}
        <Card
          withBorder
          radius="md"
          p="lg"
          className={useThemeClasses("", "bg-dark-6")}
        >
          <Title
            order={4}
            className={useThemeClasses(
              "!mb-4 text-gray-900",
              "!mb-4 text-gray-100",
            )}
          >
            Application Timeline
          </Title>
          <Timeline
            active={getTimelineItems().filter((item) => item.active).length - 1}
            bulletSize={32}
            lineWidth={2}
          >
            {getTimelineItems().map((item, index) => (
              <Timeline.Item
                key={index}
                title={
                  <Text
                    fw={600}
                    size="lg"
                    className={useThemeClasses(
                      "text-gray-900",
                      "text-gray-100",
                    )}
                  >
                    {item.title}
                  </Text>
                }
                bullet={item.icon}
                color={item.active ? item.color : "gray"}
              >
                <Text size="sm" c="dimmed" className="mb-1">
                  {item.date}
                </Text>
                <Text
                  size="sm"
                  className={useThemeClasses("text-gray-700", "text-gray-300")}
                >
                  {item.description}
                </Text>
              </Timeline.Item>
            ))}
          </Timeline>
        </Card>

        {/* Action buttons */}
        <Group justify="center" mt="md">
          <Button variant="outline" color="gray" onClick={onClose}>
            Close
          </Button>
          <Button
            variant="filled"
            color="blue"
            disabled={application.status === "rejected"}
          >
            Contact Employer
          </Button>
        </Group>
      </Stack>
    </Modal>
  );
}
