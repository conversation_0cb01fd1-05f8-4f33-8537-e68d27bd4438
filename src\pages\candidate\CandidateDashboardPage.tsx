import { <PERSON><PERSON><PERSON><PERSON>, PageHeading } from "@/design-system/components";
import {
  <PERSON>ge,
  Button,
  Card,
  Divider,
  Group,
  Progress,
  Text,
  useMantineColorScheme,
} from "@mantine/core";
import {
  FaBriefcase,
  FaBuilding,
  FaCalendarAlt,
  FaCheckCircle,
  FaEye,
  FaMapMarkerAlt,
  FaStar,
} from "react-icons/fa";
import { Link } from "react-router";

// Dummy data for recent applications
const recentApplications = [
  {
    id: 1,
    position: "Senior Frontend Developer",
    company: "Tech Innovations Inc.",
    location: "San Francisco, CA",
    date: "2023-11-15",
    status: "Applied",
  },
  {
    id: 2,
    position: "UX/UI Designer",
    company: "Creative Solutions",
    location: "New York, NY",
    date: "2023-11-10",
    status: "Interview",
  },
  {
    id: 3,
    position: "Full Stack Developer",
    company: "Global Systems",
    location: "Remote",
    date: "2023-11-05",
    status: "Rejected",
  },
  {
    id: 4,
    position: "Product Manager",
    company: "Innovative Products",
    location: "Austin, TX",
    date: "2023-10-28",
    status: "Offer",
  },
];

// Dummy data for recommended jobs
const recommendedJobs = [
  {
    id: 1,
    position: "Senior React Developer",
    company: "Tech Giants",
    location: "Remote",
    salary: "$120K - $150K",
    posted: "2 days ago",
    match: "95%",
  },
  {
    id: 2,
    position: "Frontend Team Lead",
    company: "Startup Innovators",
    location: "Boston, MA",
    salary: "$130K - $160K",
    posted: "1 week ago",
    match: "90%",
  },
  {
    id: 3,
    position: "JavaScript Developer",
    company: "Software Solutions",
    location: "Chicago, IL",
    salary: "$100K - $130K",
    posted: "3 days ago",
    match: "85%",
  },
];

export default function CandidateDashboardPage() {
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";
  return (
    <PageContainer
      breadcrumbItems={[{ title: "Home", href: "/" }, { title: "Dashboard" }]}
      variant="candidate"
    >
      <PageHeading
        title="Candidate Dashboard"
        subtitle="Welcome back! Here's an overview of your job search"
      />

      {/* Key Metrics */}
      <div className="mb-8">
        <Text
          size="lg"
          fw={700}
          className={`mb-4 ${isDark ? "text-gray-200" : "text-gray-800"}`}
        >
          Your Job Search Overview
        </Text>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          {/* Jobs Applied Metric */}
          <Card
            withBorder
            p="lg"
            radius="md"
            className={`transition-all duration-200 hover:shadow-md ${
              isDark
                ? "border-dark-4 bg-dark-7 hover:border-primary-500/30"
                : "border-gray-200 bg-white hover:border-primary-500/30"
            }`}
            style={{
              borderColor: isDark ? "var(--mantine-color-dark-4)" : undefined,
              backgroundColor: isDark
                ? "var(--mantine-color-dark-7)"
                : undefined,
            }}
          >
            <div className="flex flex-col h-full">
              <div className="flex justify-between items-start mb-4">
                <div
                  className="rounded-full p-3"
                  style={{
                    backgroundColor: isDark
                      ? "rgba(24, 144, 255, 0.2)"
                      : "rgba(24, 144, 255, 0.1)",
                  }}
                >
                  <FaBriefcase className="text-primary-color" size={22} />
                </div>
                <Badge
                  color="teal"
                  variant="light"
                  size="sm"
                  className="animate-pulse"
                >
                  +3 new
                </Badge>
              </div>

              <Text size="sm" c="dimmed" className="mb-1">
                Jobs Applied
              </Text>

              <Text size="xl" fw={700} className="mb-4">
                12
              </Text>

              <div className="mt-auto">
                <Group justify="space-between" className="mb-1">
                  <Text size="xs" c="dimmed">
                    Monthly Target
                  </Text>
                  <Text
                    size="xs"
                    fw={500}
                    className={isDark ? "text-primary-300" : "text-primary-600"}
                  >
                    12/20
                  </Text>
                </Group>
                <Progress
                  value={60}
                  size="sm"
                  radius="xl"
                  color="primary"
                  className="transition-all duration-300"
                />
              </div>
            </div>
          </Card>

          {/* Interviews Metric */}
          <Card
            withBorder
            p="lg"
            radius="md"
            className={`transition-all duration-200 hover:shadow-md ${
              isDark
                ? "border-dark-4 bg-dark-7 hover:border-secondary-500/30"
                : "border-gray-200 bg-white hover:border-secondary-500/30"
            }`}
            style={{
              borderColor: isDark ? "var(--mantine-color-dark-4)" : undefined,
              backgroundColor: isDark
                ? "var(--mantine-color-dark-7)"
                : undefined,
            }}
          >
            <div className="flex flex-col h-full">
              <div className="flex justify-between items-start mb-4">
                <div
                  className="rounded-full p-3"
                  style={{
                    backgroundColor: isDark
                      ? "rgba(19, 194, 194, 0.2)"
                      : "rgba(19, 194, 194, 0.1)",
                  }}
                >
                  <FaCheckCircle className="text-secondary-color" size={22} />
                </div>
                <Badge color="teal" variant="light" size="sm">
                  +1 new
                </Badge>
              </div>

              <Text size="sm" c="dimmed" className="mb-1">
                Interviews
              </Text>

              <Text size="xl" fw={700} className="mb-4">
                3
              </Text>

              <div className="mt-auto">
                <Group justify="space-between" className="mb-1">
                  <Text size="xs" c="dimmed">
                    Success Rate
                  </Text>
                  <Text
                    size="xs"
                    fw={500}
                    className={
                      isDark ? "text-secondary-300" : "text-secondary-600"
                    }
                  >
                    75%
                  </Text>
                </Group>
                <Progress
                  value={75}
                  size="sm"
                  radius="xl"
                  color="secondary"
                  className="transition-all duration-300"
                />
              </div>
            </div>
          </Card>

          {/* Saved Jobs Metric */}
          <Card
            withBorder
            p="lg"
            radius="md"
            className={`transition-all duration-200 hover:shadow-md ${
              isDark
                ? "border-dark-4 bg-dark-7 hover:border-accent-500/30"
                : "border-gray-200 bg-white hover:border-accent-500/30"
            }`}
            style={{
              borderColor: isDark ? "var(--mantine-color-dark-4)" : undefined,
              backgroundColor: isDark
                ? "var(--mantine-color-dark-7)"
                : undefined,
            }}
          >
            <div className="flex flex-col h-full">
              <div className="flex justify-between items-start mb-4">
                <div
                  className="rounded-full p-3"
                  style={{
                    backgroundColor: isDark
                      ? "rgba(114, 46, 209, 0.2)"
                      : "rgba(114, 46, 209, 0.1)",
                  }}
                >
                  <FaStar className="text-accent-color" size={22} />
                </div>
                <Badge color="teal" variant="light" size="sm">
                  +2 new
                </Badge>
              </div>

              <Text size="sm" c="dimmed" className="mb-1">
                Saved Jobs
              </Text>

              <Text size="xl" fw={700} className="mb-4">
                8
              </Text>

              <div className="mt-auto">
                <Group justify="space-between" className="mb-1">
                  <Text size="xs" c="dimmed">
                    Applied
                  </Text>
                  <Text
                    size="xs"
                    fw={500}
                    className={isDark ? "text-accent-300" : "text-accent-600"}
                  >
                    4/8
                  </Text>
                </Group>
                <Progress
                  value={50}
                  size="sm"
                  radius="xl"
                  color="accent"
                  className="transition-all duration-300"
                />
              </div>
            </div>
          </Card>

          {/* Profile Views Metric */}
          <Card
            withBorder
            p="lg"
            radius="md"
            className={`transition-all duration-200 hover:shadow-md ${
              isDark
                ? "border-dark-4 bg-dark-7 hover:border-primary-500/30"
                : "border-gray-200 bg-white hover:border-primary-500/30"
            }`}
            style={{
              borderColor: isDark ? "var(--mantine-color-dark-4)" : undefined,
              backgroundColor: isDark
                ? "var(--mantine-color-dark-7)"
                : undefined,
            }}
          >
            <div className="flex flex-col h-full">
              <div className="flex justify-between items-start mb-4">
                <div
                  className="rounded-full p-3"
                  style={{
                    backgroundColor: isDark
                      ? "rgba(24, 144, 255, 0.2)"
                      : "rgba(24, 144, 255, 0.1)",
                  }}
                >
                  <FaEye className="text-primary-color" size={22} />
                </div>
                <Badge
                  color="teal"
                  variant="light"
                  size="sm"
                  className="animate-pulse"
                >
                  +5 new
                </Badge>
              </div>

              <Text size="sm" c="dimmed" className="mb-1">
                Profile Views
              </Text>

              <Text size="xl" fw={700} className="mb-4">
                24
              </Text>

              <div className="mt-auto">
                <Group justify="space-between" className="mb-1">
                  <Text size="xs" c="dimmed">
                    Last Week
                  </Text>
                  <Text
                    size="xs"
                    fw={500}
                    className={isDark ? "text-primary-300" : "text-primary-600"}
                  >
                    +26%
                  </Text>
                </Group>
                <Progress
                  value={26}
                  size="sm"
                  radius="xl"
                  color="primary"
                  className="transition-all duration-300"
                />
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* Recent Applications */}
      <div className="mb-8">
        <Text
          size="lg"
          fw={700}
          className={`mb-4 ${isDark ? "text-gray-200" : "text-gray-800"}`}
        >
          Recent Applications
        </Text>

        <Card
          withBorder
          p="lg"
          radius="md"
          className={`transition-all duration-200 ${
            isDark ? "border-dark-4 bg-dark-7" : "border-gray-200 bg-white"
          }`}
          style={{
            borderColor: isDark ? "var(--mantine-color-dark-4)" : undefined,
            backgroundColor: isDark ? "var(--mantine-color-dark-7)" : undefined,
          }}
        >
          <Group justify="space-between" mb="md">
            <Text
              size="md"
              fw={600}
              className={isDark ? "text-gray-300" : "text-gray-700"}
            >
              Your application history
            </Text>
            <Button
              variant="subtle"
              component={Link}
              to="/candidate/applications"
              color={isDark ? "primary.4" : "primary"}
              className="transition-all duration-200"
              size="sm"
            >
              View All
            </Button>
          </Group>

          <div className="overflow-x-auto">
            <table
              className={`min-w-full divide-y ${isDark ? "divide-dark-4" : "divide-gray-200"}`}
            >
              <thead className={isDark ? "bg-dark-6" : "bg-gray-50"}>
                <tr>
                  <th
                    className={`px-6 py-3 text-left text-xs font-medium tracking-wider uppercase ${isDark ? "text-gray-400" : "text-gray-500"}`}
                  >
                    Position
                  </th>
                  <th
                    className={`px-6 py-3 text-left text-xs font-medium tracking-wider uppercase ${isDark ? "text-gray-400" : "text-gray-500"}`}
                  >
                    Company
                  </th>
                  <th
                    className={`px-6 py-3 text-left text-xs font-medium tracking-wider uppercase ${isDark ? "text-gray-400" : "text-gray-500"}`}
                  >
                    Location
                  </th>
                  <th
                    className={`px-6 py-3 text-left text-xs font-medium tracking-wider uppercase ${isDark ? "text-gray-400" : "text-gray-500"}`}
                  >
                    Date
                  </th>
                  <th
                    className={`px-6 py-3 text-left text-xs font-medium tracking-wider uppercase ${isDark ? "text-gray-400" : "text-gray-500"}`}
                  >
                    Status
                  </th>
                </tr>
              </thead>
              <tbody
                className={`divide-y ${isDark ? "divide-dark-4 bg-dark-7" : "divide-gray-200 bg-white"}`}
              >
                {recentApplications.map((application) => (
                  <tr
                    key={application.id}
                    className={isDark ? "hover:bg-dark-6" : "hover:bg-gray-50"}
                  >
                    <td
                      className={`px-6 py-4 text-sm font-medium whitespace-nowrap ${isDark ? "text-gray-200" : "text-gray-900"}`}
                    >
                      {application.position}
                    </td>
                    <td
                      className={`px-6 py-4 text-sm whitespace-nowrap ${isDark ? "text-gray-400" : "text-gray-500"}`}
                    >
                      {application.company}
                    </td>
                    <td
                      className={`px-6 py-4 text-sm whitespace-nowrap ${isDark ? "text-gray-400" : "text-gray-500"}`}
                    >
                      {application.location}
                    </td>
                    <td
                      className={`px-6 py-4 text-sm whitespace-nowrap ${isDark ? "text-gray-400" : "text-gray-500"}`}
                    >
                      {new Date(application.date).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-flex rounded-full px-2 text-xs leading-5 font-semibold ${
                          application.status === "Applied"
                            ? isDark
                              ? "bg-primary-700/30 text-primary-200"
                              : "bg-primary-100 text-primary-800"
                            : application.status === "Interview"
                              ? isDark
                                ? "bg-secondary-700/30 text-secondary-200"
                                : "bg-secondary-100 text-secondary-800"
                              : application.status === "Rejected"
                                ? isDark
                                  ? "bg-red-900/30 text-red-200"
                                  : "bg-red-100 text-red-800"
                                : isDark
                                  ? "bg-green-900/30 text-green-200"
                                  : "bg-green-100 text-green-800"
                        }`}
                      >
                        {application.status}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </Card>
      </div>

      {/* Recommended Jobs */}
      <div className="mb-8">
        <Text
          size="lg"
          fw={700}
          className={`mb-4 ${isDark ? "text-gray-200" : "text-gray-800"}`}
        >
          Recommended Jobs
        </Text>

        <Card
          withBorder
          p="lg"
          radius="md"
          className={`transition-all duration-200 ${
            isDark ? "border-dark-4 bg-dark-7" : "border-gray-200 bg-white"
          }`}
          style={{
            borderColor: isDark ? "var(--mantine-color-dark-4)" : undefined,
            backgroundColor: isDark ? "var(--mantine-color-dark-7)" : undefined,
          }}
        >
          <Group justify="space-between" mb="md">
            <Text
              size="md"
              fw={600}
              className={isDark ? "text-gray-300" : "text-gray-700"}
            >
              Jobs that match your profile
            </Text>
            <Button
              variant="subtle"
              component={Link}
              to="/candidate/jobs"
              color={isDark ? "primary.4" : "primary"}
              className="transition-all duration-200"
              size="sm"
            >
              Browse All Jobs
            </Button>
          </Group>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            {recommendedJobs.map((job) => (
              <Card
                key={job.id}
                withBorder
                shadow="sm"
                radius="md"
                p="md"
                className={`transition-all duration-200 ${
                  isDark
                    ? "border-dark-4 bg-dark-6 hover:border-primary-color/30"
                    : "border-gray-200 bg-white hover:border-primary-color/20"
                }`}
                style={{
                  borderColor: isDark
                    ? "var(--mantine-color-dark-4)"
                    : undefined,
                  backgroundColor: isDark
                    ? "var(--mantine-color-dark-6)"
                    : undefined,
                }}
              >
                <div className="flex flex-col space-y-2">
                  <Text
                    fw={700}
                    size="lg"
                    className={`${isDark ? "text-primary-color/90" : "text-primary-color"}`}
                  >
                    {job.position}
                  </Text>
                  <div className="flex items-center space-x-2">
                    <FaBuilding
                      size={14}
                      className={isDark ? "text-gray-400" : "text-gray-500"}
                    />
                    <Text size="sm" className={isDark ? "text-gray-300" : ""}>
                      {job.company}
                    </Text>
                  </div>
                  <div className="flex items-center space-x-2">
                    <FaMapMarkerAlt
                      size={14}
                      className={isDark ? "text-gray-400" : "text-gray-500"}
                    />
                    <Text size="sm" className={isDark ? "text-gray-300" : ""}>
                      {job.location}
                    </Text>
                  </div>
                  <div className="flex items-center space-x-2">
                    <FaBriefcase
                      size={14}
                      className={isDark ? "text-gray-400" : "text-gray-500"}
                    />
                    <Text size="sm" className={isDark ? "text-gray-300" : ""}>
                      {job.salary}
                    </Text>
                  </div>
                  <div className="flex items-center space-x-2">
                    <FaCalendarAlt
                      size={14}
                      className={isDark ? "text-gray-400" : "text-gray-500"}
                    />
                    <Text size="sm" className={isDark ? "text-gray-300" : ""}>
                      Posted {job.posted}
                    </Text>
                  </div>
                  <Divider my="xs" color={isDark ? "dark.4" : undefined} />
                  <div className="flex items-center justify-between">
                    <span
                      className={`rounded-full px-2 py-1 text-xs font-semibold ${
                        isDark
                          ? "bg-secondary-700/30 text-secondary-200"
                          : "bg-secondary-100 text-secondary-800"
                      }`}
                    >
                      {job.match} Match
                    </span>
                    <Button
                      variant="light"
                      size="xs"
                      component={Link}
                      to={`/candidate/jobs/${job.id}`}
                      color={isDark ? "primary.4" : "primary"}
                      className="transition-all duration-200"
                    >
                      View Job
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </Card>
      </div>
    </PageContainer>
  );
}
