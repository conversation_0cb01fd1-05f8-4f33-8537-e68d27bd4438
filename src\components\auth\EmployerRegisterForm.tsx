import { employerSimpleSchema } from "@/schemas/auth/employer-register-schema";
import {
  Button,
  Group,
  PasswordInput,
  Select,
  Textarea,
  TextInput,
} from "@mantine/core";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import {
  FaBriefcase,
  FaBuilding,
  FaEnvelope,
  FaGlobe,
  FaLock,
  FaPhone,
} from "react-icons/fa";
import { Link } from "react-router";

export default function EmployerRegisterForm() {
  // const [isLoading, setIsLoading] = useState(false);

  // Simplified form for employer registration
  const form = useForm({
    initialValues: {
      companyName: "",
      email: "",
      password: "",
      passwordConfirmation: "",
      phone: "",
      website: "",
      description: "",
      industry: "",
      companySize: "",
      role: "employer" as const,
    },
    validate: zodResolver(employerSimpleSchema),
  });

  // Handle form submission
  const handleSubmit = async (values: typeof form.values) => {
    console.log(values);
  };

  return (
    <div className="relative">
      {/* <LoadingOverlay visible={isLoading} overlayProps={{ blur: 2 }} /> */}

      <form
        noValidate
        onSubmit={form.onSubmit(handleSubmit)}
        className="flex flex-col gap-4"
      >
        <TextInput
          label="Company Name"
          placeholder="Enter your company name"
          required
          leftSection={<FaBuilding />}
          {...form.getInputProps("companyName")}
        />

        <TextInput
          label="Email"
          placeholder="<EMAIL>"
          required
          leftSection={<FaEnvelope />}
          {...form.getInputProps("email")}
        />

        <PasswordInput
          label="Password"
          placeholder="Your password"
          required
          leftSection={<FaLock />}
          {...form.getInputProps("password")}
        />

        <PasswordInput
          label="Confirm Password"
          placeholder="Confirm your password"
          required
          leftSection={<FaLock />}
          {...form.getInputProps("passwordConfirmation")}
        />

        <TextInput
          label="Phone Number"
          placeholder="Enter your phone number"
          required
          leftSection={<FaPhone />}
          {...form.getInputProps("phone")}
        />

        <TextInput
          label="Website"
          placeholder="https://yourcompany.com"
          leftSection={<FaGlobe />}
          {...form.getInputProps("website")}
        />

        <Textarea
          label="Company Description"
          placeholder="Describe your company (min 10 characters)"
          required
          minRows={3}
          {...form.getInputProps("description")}
        />

        <Select
          label="Industry"
          placeholder="Select your industry"
          required
          data={[
            { value: "technology", label: "Technology" },
            { value: "healthcare", label: "Healthcare" },
            { value: "finance", label: "Finance" },
            { value: "education", label: "Education" },
            { value: "retail", label: "Retail" },
            { value: "manufacturing", label: "Manufacturing" },
            { value: "design", label: "Design" },
            { value: "marketing", label: "Marketing" },
            { value: "consulting", label: "Consulting" },
            { value: "other", label: "Other" },
          ]}
          leftSection={<FaBriefcase />}
          {...form.getInputProps("industry")}
        />

        <Select
          label="Company Size"
          placeholder="Select company size"
          required
          data={[
            { value: "1-10", label: "1-10 employees" },
            { value: "11-50", label: "11-50 employees" },
            { value: "51-200", label: "51-200 employees" },
            { value: "201-500", label: "201-500 employees" },
            { value: "501+", label: "501+ employees" },
          ]}
          leftSection={<FaBriefcase />}
          {...form.getInputProps("companySize")}
        />

        <Button
          type="submit"
          mt="md"
          // loading={isLoading}
        >
          Complete Registration
        </Button>

        <Group mt="md" justify="center">
          <Link
            to="/auth/login"
            className="text-sm text-blue-600 hover:underline"
          >
            Already have an account? Login
          </Link>
        </Group>
      </form>
    </div>
  );
}
