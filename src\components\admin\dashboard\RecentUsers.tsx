import Card from "@/design-system/components/card/Card";
import { Badge, Group, Text } from "@mantine/core";
import AdminSectionHeading from "../AdminSectionHeading";

const recentUsers = [
  {
    id: 1,
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "candidate",
    registeredDate: "2023-10-10",
  },
  {
    id: 2,
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "employer",
    registeredDate: "2023-10-09",
  },
  {
    id: 3,
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "candidate",
    registeredDate: "2023-10-08",
  },
];

export default function RecentUsers() {
  return (
    <Card>
      <AdminSectionHeading>Recently Registered Users</AdminSectionHeading>
      {recentUsers.map((user) => (
        <div key={user.id} className="mb-4 border-b pb-4">
          <Text size="lg" fw={600}>
            {user.name}
          </Text>
          <Group>
            <Text size="sm" c="dimmed">
              {user.email}
            </Text>
            <Badge
              color={user.role === "candidate" ? "blue" : "green"}
              variant="light"
            >
              {user.role}
            </Badge>
            <Text size="sm" c="dimmed">
              Registered on: {user.registeredDate}
            </Text>
          </Group>
        </div>
      ))}
    </Card>
  );
}
