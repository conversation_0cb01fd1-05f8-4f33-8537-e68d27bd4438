import { candidateData } from "@/data/candidate-data";
import { <PERSON><PERSON>, Card, Group, Title } from "@mantine/core";
import { FaFilePdf } from "react-icons/fa";
import { Link } from "react-router";

export default function CandidateDocuments() {
  return (
    <Card withBorder radius="md" className="mb-6 p-6 shadow-sm">
      <Title order={2} className="mb-4 text-lg font-semibold">
        Documents
      </Title>
      <Group>
        <Button
          component={Link}
          to={candidateData.resume}
          target="_blank"
          leftSection={<FaFilePdf size={16} />}
          size="sm"
        >
          View Resume
        </Button>
        <Button
          component={Link}
          to={candidateData.coverLetter}
          target="_blank"
          leftSection={<FaFilePdf size={16} />}
          size="sm"
          variant="outline"
        >
          View Cover Letter
        </Button>
      </Group>
    </Card>
  );
}
