import { Avatar, Badge, Button, Group, Text } from "@mantine/core";
import {
  FaBriefcase,
  FaCalendarAlt,
  FaEye,
  FaThumbsDown,
  FaThumbsUp,
} from "react-icons/fa";
import { useNavigate } from "react-router";

const recentCandidates = [
  {
    id: 1,
    name: "<PERSON>",
    jobTitle: "Senior Software Engineer",
    appliedDate: "2023-10-10",
    status: "New",
    experience: "5 years",
    skills: ["React", "TypeScript", "Node.js"],
  },
  {
    id: 2,
    name: "<PERSON>",
    jobTitle: "Product Manager",
    appliedDate: "2023-10-09",
    status: "Shortlisted",
    experience: "7 years",
    skills: ["Product Strategy", "Agile", "User Research"],
  },
  {
    id: 3,
    name: "<PERSON>",
    jobTitle: "UX Designer",
    appliedDate: "2023-10-08",
    status: "Interviewing",
    experience: "4 years",
    skills: ["Figma", "UI Design", "User Testing"],
  },
  {
    id: 4,
    name: "<PERSON>",
    jobTitle: "Frontend Developer",
    appliedDate: "2023-10-07",
    status: "New",
    experience: "3 years",
    skills: ["JavaScript", "CSS", "HTML"],
  },
];

export default function RecentlyAppliedCandidates() {
  const navigate = useNavigate();

  const handleViewProfile = (candidateId: number) => {
    navigate(`/employer/candidates/${candidateId}`);
  };

  const handleViewAllCandidates = () => {
    navigate("/employer/candidates");
  };

  return (
    <div>
      {recentCandidates.map((candidate) => (
        <div key={candidate.id} className="mb-4 border-b pb-4">
          <Group justify="space-between" wrap="nowrap" className="mb-2">
            <Group gap="sm" wrap="nowrap">
              <Avatar color="blue" radius="xl">
                {candidate.name
                  .split(" ")
                  .map((n) => n[0])
                  .join("")}
              </Avatar>
              <div>
                <Text fw={600}>{candidate.name}</Text>
                <Text size="sm" c="dimmed">
                  {candidate.experience} experience
                </Text>
              </div>
            </Group>
            <Badge
              size="lg"
              color={
                candidate.status === "New"
                  ? "blue"
                  : candidate.status === "Shortlisted"
                    ? "green"
                    : "yellow"
              }
            >
              {candidate.status}
            </Badge>
          </Group>

          <Group className="mt-2 ml-12">
            <Group gap="xs">
              <FaBriefcase size={14} className="text-blue-500" />
              <Text size="sm">Applied for: {candidate.jobTitle}</Text>
            </Group>
            <Group gap="xs">
              <FaCalendarAlt size={14} className="text-blue-500" />
              <Text size="sm">Applied on: {candidate.appliedDate}</Text>
            </Group>
          </Group>

          <Group className="mt-2 ml-12">
            {candidate.skills.map((skill, index) => (
              <Badge key={index} variant="light" color="gray">
                {skill}
              </Badge>
            ))}
          </Group>

          <Group className="mt-3 ml-12">
            <Button
              variant="light"
              size="xs"
              leftSection={<FaEye size={12} />}
              onClick={() => handleViewProfile(candidate.id)}
            >
              View Profile
            </Button>
            <Button
              variant="outline"
              size="xs"
              color="green"
              leftSection={<FaThumbsUp size={12} />}
            >
              Shortlist
            </Button>
            <Button
              variant="outline"
              size="xs"
              color="red"
              leftSection={<FaThumbsDown size={12} />}
            >
              Reject
            </Button>
          </Group>
        </div>
      ))}

      <Group justify="center" className="mt-4">
        <Button variant="subtle" onClick={handleViewAllCandidates}>
          View All Candidates
        </Button>
      </Group>
    </div>
  );
}
