import { dummyJobs } from "@/data/jobs-data";
import { Grid, Text } from "@mantine/core";
import { useState } from "react";
import JobCard from "./JobCard";

export default function JobsList() {
  // Set default view mode to grid
  const [viewMode] = useState<"grid" | "list">("grid");

  // Use the dummy jobs data from our data file
  const jobs = dummyJobs.map((job) => ({
    ...job,
    status:
      job.id % 3 === 0 ? "Closed" : job.id % 2 === 0 ? "Pending" : "Active",
  }));

  if (jobs.length === 0) {
    return <Text className="text-center text-lg">No jobs available.</Text>;
  }

  return (
    <Grid gutter="lg">
      {jobs.map((job: any) => (
        <JobCard key={job.id} job={job} viewMode={viewMode} />
      ))}
    </Grid>
  );
}
