import { create } from "zustand";

// Define the selected candidate store type
interface SelectedCandidateStore {
  selectedCandidateId: number;
  setSelectedCandidateId: (id: number) => void;
}

// Create the selected candidate store
export const useSelectedCandidateStore = create<SelectedCandidateStore>(
  (set) => ({
    selectedCandidateId: 1, // Default to the first candidate
    setSelectedCandidateId: (id) => set({ selectedCandidateId: id }),
  }),
);
