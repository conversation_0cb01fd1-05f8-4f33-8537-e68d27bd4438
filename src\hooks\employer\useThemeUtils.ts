import { useThemeClasses } from "@/design-system/utils/theme-utils";

// Custom hook to simplify common theme patterns
export function useThemeUtils() {
  // Common card styles
  const cardStyles = useThemeClasses(
    "bg-white border border-gray-200 shadow-sm",
    "bg-dark-7 border border-dark-4 shadow-dark-sm"
  );

  const cardHoverStyles = useThemeClasses(
    "transition-all duration-200 hover:shadow-md",
    "transition-all duration-200 hover:shadow-dark-md"
  );

  // Common text styles
  const primaryTextStyles = useThemeClasses(
    "text-gray-800",
    "text-gray-100"
  );

  const secondaryTextStyles = useThemeClasses(
    "text-gray-600",
    "text-gray-400"
  );

  const mutedTextStyles = useThemeClasses(
    "text-gray-500",
    "text-gray-500"
  );

  // Common background styles
  const primaryBgStyles = useThemeClasses(
    "bg-white",
    "bg-dark-7"
  );

  const secondaryBgStyles = useThemeClasses(
    "bg-gray-50",
    "bg-dark-6"
  );

  const accentBgStyles = useThemeClasses(
    "bg-blue-50",
    "bg-blue-900/20"
  );

  // Common border styles
  const borderStyles = useThemeClasses(
    "border-gray-200",
    "border-dark-4"
  );

  const dividerStyles = useThemeClasses(
    "border-b border-gray-200",
    "border-b border-dark-4"
  );

  // Common gradient styles
  const gradientStyles = useThemeClasses(
    "bg-gradient-to-r from-blue-50 to-indigo-50",
    "bg-gradient-to-r from-dark-6 to-dark-5"
  );

  // Common input styles
  const inputStyles = useThemeClasses(
    "border-gray-300 focus:border-blue-500",
    "border-dark-4 focus:border-blue-400"
  );

  // Common button styles
  const primaryButtonStyles = useThemeClasses(
    "bg-blue-600 hover:bg-blue-700 text-white",
    "bg-blue-600 hover:bg-blue-700 text-white"
  );

  const secondaryButtonStyles = useThemeClasses(
    "bg-gray-100 hover:bg-gray-200 text-gray-700",
    "bg-dark-6 hover:bg-dark-5 text-gray-300"
  );

  return {
    // Card styles
    cardStyles,
    cardHoverStyles,
    
    // Text styles
    primaryTextStyles,
    secondaryTextStyles,
    mutedTextStyles,
    
    // Background styles
    primaryBgStyles,
    secondaryBgStyles,
    accentBgStyles,
    
    // Border styles
    borderStyles,
    dividerStyles,
    
    // Other styles
    gradientStyles,
    inputStyles,
    primaryButtonStyles,
    secondaryButtonStyles,
    
    // Utility function for custom theme classes
    getThemeClasses: useThemeClasses,
  };
}
