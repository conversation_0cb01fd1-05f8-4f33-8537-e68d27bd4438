import { useThemeClasses } from "@/design-system/utils/theme-utils";
import { URLS } from "@/utils/urls";
import { Button, Tabs, useMantineColorScheme } from "@mantine/core";
import { useEffect, useState } from "react";
import {
  FaBriefcase,
  FaBuilding,
  FaCog,
  FaTachometerAlt,
  FaUsers,
} from "react-icons/fa";
import { Link, useLocation } from "react-router";

export default function AdminNavbar() {
  const { pathname } = useLocation();
  const [activeTab, setActiveTab] = useState<string | null>("dashboard");
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  // Map paths to tab values
  useEffect(() => {
    if (pathname.includes("/admin/dashboard")) {
      setActiveTab("dashboard");
    } else if (pathname.includes("/admin/users")) {
      setActiveTab("users");
    } else if (pathname.includes("/admin/jobs")) {
      setActiveTab("jobs");
    } else if (pathname.includes("/admin/companies")) {
      setActiveTab("companies");
    } else if (pathname.includes("/admin/settings")) {
      setActiveTab("settings");
    } else {
      setActiveTab("dashboard");
    }
  }, [pathname]);

  return (
    <div
      className={useThemeClasses(
        "border-b border-gray-200 bg-white px-6 py-4",
        "border-b border-dark-4 bg-dark-7 px-6 py-4",
      )}
    >
      <div className="flex flex-col items-center justify-between gap-4 sm:flex-row">
        <div className="flex items-center gap-4">
          <h1 className="text-primary-color text-2xl font-bold">Admin Panel</h1>
        </div>

        {/* Desktop Navigation Tabs */}
        <Tabs
          value={activeTab}
          onChange={(value) => setActiveTab(value)}
          variant="pills"
          radius="xl"
          className="hidden md:block"
        >
          <Tabs.List>
            <Link to={URLS.admin.dashboard}>
              <Tabs.Tab
                value="dashboard"
                leftSection={<FaTachometerAlt size={16} />}
              >
                Dashboard
              </Tabs.Tab>
            </Link>
            <Link to={URLS.admin.users}>
              <Tabs.Tab value="users" leftSection={<FaUsers size={16} />}>
                Users
              </Tabs.Tab>
            </Link>
            <Link to={URLS.admin.jobs}>
              <Tabs.Tab value="jobs" leftSection={<FaBriefcase size={16} />}>
                Jobs
              </Tabs.Tab>
            </Link>
            <Link to={URLS.admin.companies}>
              <Tabs.Tab
                value="companies"
                leftSection={<FaBuilding size={16} />}
              >
                Companies
              </Tabs.Tab>
            </Link>
            <Link to={URLS.admin.settings}>
              <Tabs.Tab value="settings" leftSection={<FaCog size={16} />}>
                Settings
              </Tabs.Tab>
            </Link>
          </Tabs.List>
        </Tabs>

        {/* Mobile Navigation Dropdown */}
        <div className="w-full sm:w-auto md:hidden">
          <Button
            variant="light"
            radius="xl"
            className={`w-full sm:w-auto ${isDark ? "bg-dark-6 text-gray-200 hover:bg-dark-5" : ""}`}
            component={Link}
            to={
              activeTab === "dashboard"
                ? URLS.admin.dashboard
                : activeTab === "users"
                  ? URLS.admin.users
                  : activeTab === "jobs"
                    ? URLS.admin.jobs
                    : activeTab === "companies"
                      ? URLS.admin.companies
                      : URLS.admin.settings
            }
            rightSection={
              activeTab === "dashboard" ? (
                <FaTachometerAlt size={16} />
              ) : activeTab === "users" ? (
                <FaUsers size={16} />
              ) : activeTab === "jobs" ? (
                <FaBriefcase size={16} />
              ) : activeTab === "companies" ? (
                <FaBuilding size={16} />
              ) : (
                <FaCog size={16} />
              )
            }
          >
            {activeTab === "dashboard"
              ? "Dashboard"
              : activeTab === "users"
                ? "Users"
                : activeTab === "jobs"
                  ? "Jobs"
                  : activeTab === "companies"
                    ? "Companies"
                    : "Settings"}
          </Button>
        </div>
      </div>
    </div>
  );
}
