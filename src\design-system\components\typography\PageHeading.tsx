import { cn } from "@/design-system/utils";
import { useThemeClasses } from "@/design-system/utils/theme-utils";

export interface PageHeadingProps {
  title: string;
  subtitle?: string;
  className?: string;
  variant?: "default" | "admin" | "employer" | "candidate";
}

/**
 * Reusable page heading component with optional subtitle
 *
 * @param variant - Different styling variants based on user role
 */
export default function PageHeading({
  title,
  subtitle,
  className = "",
  variant = "default",
}: PageHeadingProps) {
  // Apply variant-specific styling
  const variantClasses = {
    default: "mb-6",
    admin: "mb-4 md:mb-6",
    employer: "mb-6",
    candidate: "mb-6",
  };

  const titleClasses = {
    default: useThemeClasses(
      "text-primary-color text-2xl font-bold",
      "text-primary-color/90 text-2xl font-bold",
    ),
    admin: useThemeClasses(
      "text-primary-color text-xl font-bold md:text-2xl",
      "text-primary-color/90 text-xl font-bold md:text-2xl",
    ),
    employer: useThemeClasses(
      "text-primary-color text-2xl font-bold",
      "text-primary-color/90 text-2xl font-bold",
    ),
    candidate: useThemeClasses(
      "text-primary-color text-2xl font-bold",
      "text-primary-color/90 text-2xl font-bold",
    ),
  };

  const subtitleClasses = {
    default: useThemeClasses("mt-1 text-gray-600", "mt-1 text-gray-400"),
    admin: useThemeClasses(
      "mt-1 text-sm text-gray-500 md:text-base",
      "mt-1 text-sm text-gray-400 md:text-base",
    ),
    employer: useThemeClasses("mt-1 text-gray-600", "mt-1 text-gray-400"),
    candidate: useThemeClasses("mt-1 text-gray-600", "mt-1 text-gray-400"),
  };

  return (
    <div className={cn(variantClasses[variant], className)}>
      <h1 className={titleClasses[variant]}>{title}</h1>
      {subtitle && <p className={subtitleClasses[variant]}>{subtitle}</p>}
    </div>
  );
}
