// React Router imports
import { Navigate, Route, Routes } from "react-router";

// Custom hooks
import useScrollToTop from "./hooks/useScrollToTop";

// Layout components
import AdminLayout from "./layouts/AdminLayout";
import CandidateLayout from "./layouts/CandidateLayout";
import EmployerLayout from "./layouts/EmployerLayout";
import MainLayout from "./layouts/MainLayout";

// Website pages
import AboutPage from "./pages/website/AboutPage";
import ContactUsPage from "./pages/website/ContactUsPage";
import FaqPage from "./pages/website/FaqPage";
import HomePage from "./pages/website/HomePage";
import NotFoundPage from "./pages/website/NotFoundPage";
import ResourcesPage from "./pages/website/ResourcesPage";

// Authentication pages
import ForgotPasswordPage from "./pages/auth/ForgotPasswordPage";
import LoginPage from "./pages/auth/LoginPage";
import RegisterPage from "./pages/auth/RegisterPage";
import ResetPasswordPage from "./pages/auth/ResetPasswordPage";

// Admin pages
import AdminCompaniesPage from "./pages/admin/AdminCompaniesPage";
import AdminDashboardPage from "./pages/admin/AdminDashboardPage";
import AdminJobsPage from "./pages/admin/AdminJobsPage";
import AdminSettingsPage from "./pages/admin/AdminSettingsPage";
import AdminUsersPage from "./pages/admin/AdminUsersPage";

// Candidate pages
import CandidateApplicationsPage from "./pages/candidate/CandidateApplicationsPage";
import CandidateDashboardPage from "./pages/candidate/CandidateDashboardPage";
import CandidateJobApplicationPage from "./pages/candidate/CandidateJobApplicationPage";
import CandidateJobDetailsPage from "./pages/candidate/CandidateJobDetailsPage";
import CandidateJobsPage from "./pages/candidate/CandidateJobsPage";
import CandidateProfilePage from "./pages/candidate/CandidateProfilePage";

// Employer pages
import EmployerApplicationDetailsPage from "./pages/employer/EmployerApplicationDetailsPage";
import EmployerAppliedCandidateDetailsPage from "./pages/employer/EmployerAppliedCandidateDetailsPage";
import EmployerAppliedCandidatesPage from "./pages/employer/EmployerAppliedCandidatesPage";
import EmployerCreateJobPage from "./pages/employer/EmployerCreateJobPage";
import EmployerDashboardPage from "./pages/employer/EmployerDashboardPage";
import EmployerEditJobPage from "./pages/employer/EmployerEditJobPage";
import EmployerJobApplicationsPage from "./pages/employer/EmployerJobApplicationsPage";
import EmployerManageJobsPage from "./pages/employer/EmployerManageJobsPage";
import EmployerPricingPage from "./pages/employer/EmployerPricingPage";
import EmployerProfilePage from "./pages/employer/EmployerProfilePage";
import CheckoutPage from "./pages/employer/checkout/CheckoutPage";
import ConfirmationPage from "./pages/employer/checkout/ConfirmationPage";
import PaymentPage from "./pages/employer/checkout/PaymentPage";

/**
 * Main App component that defines the routing structure for the entire application
 * Uses React Router v6 for declarative routing
 */
export default function App() {
  // Custom hook to scroll to top on route changes
  useScrollToTop();

  return (
    <Routes>
      {/* Main Layout - Parent route that wraps all other routes */}
      {/* This provides consistent layout (header, footer, etc.) across all pages */}
      <Route path="/" element={<MainLayout />}>
        {/* Public website routes */}
        {/* index route renders at the root URL path (/) */}
        <Route index element={<HomePage />} />
        <Route path="about-us" element={<AboutPage />} />
        <Route path="contact-us" element={<ContactUsPage />} />
        <Route path="faq" element={<FaqPage />} />
        <Route path="resources" element={<ResourcesPage />} />

        {/* Authentication routes */}
        {/* These routes handle user authentication flows */}
        <Route path="/auth">
          <Route path="login" element={<LoginPage />} />
          <Route path="register" element={<RegisterPage />} />
          <Route path="forgot-password" element={<ForgotPasswordPage />} />
          <Route path="reset-password" element={<ResetPasswordPage />} />
        </Route>

        {/* Admin section routes */}
        {/* These routes are for admin users to manage the platform */}
        {/* AdminLayout provides consistent admin UI components */}
        <Route path="/admin" element={<AdminLayout />}>
          <Route index element={<Navigate to="dashboard" replace />} />
          <Route path="dashboard" element={<AdminDashboardPage />} />
          <Route path="users" element={<AdminUsersPage />} />
          <Route path="jobs" element={<AdminJobsPage />} />
          <Route path="companies" element={<AdminCompaniesPage />} />
          <Route path="settings" element={<AdminSettingsPage />} />
        </Route>

        {/* Candidate section routes */}
        {/* These routes are for job seekers to find and apply for jobs */}
        {/* CandidateLayout provides consistent candidate UI components */}
        <Route path="/candidate" element={<CandidateLayout />}>
          <Route index element={<Navigate to="dashboard" replace />} />
          <Route path="dashboard" element={<CandidateDashboardPage />} />
          <Route path="jobs" element={<CandidateJobsPage />} />
          {/* Dynamic route with :id parameter to view specific job details */}
          <Route path="jobs/:id" element={<CandidateJobDetailsPage />} />
          {/* Dynamic route for applying to a specific job */}
          <Route
            path="jobs/:id/apply"
            element={<CandidateJobApplicationPage />}
          />
          <Route path="applications" element={<CandidateApplicationsPage />} />
          <Route path="profile" element={<CandidateProfilePage />} />
        </Route>

        {/* Employer section routes */}
        {/* These routes are for employers to post jobs and manage applications */}
        {/* EmployerLayout provides consistent employer UI components */}
        <Route path="/employer" element={<EmployerLayout />}>
          <Route index element={<Navigate to="dashboard" replace />} />
          <Route path="dashboard" element={<EmployerDashboardPage />} />
          {/* View all candidates who have applied to jobs */}
          <Route
            path="candidates"
            element={<EmployerAppliedCandidatesPage />}
          />
          {/* Dynamic route to view specific candidate details */}
          <Route
            path="candidates/:candidate_id"
            element={<EmployerAppliedCandidateDetailsPage />}
          />
          <Route path="create-job" element={<EmployerCreateJobPage />} />
          <Route path="manage-jobs" element={<EmployerManageJobsPage />} />
          {/* Dynamic route to edit a specific job */}
          <Route
            path="manage-jobs/:job_id/edit"
            element={<EmployerEditJobPage />}
          />
          {/* Dynamic route to view applications for a specific job */}
          <Route
            path="manage-jobs/:job_id/applications"
            element={<EmployerJobApplicationsPage />}
          />
          {/* Dynamic route to view individual application details */}
          <Route
            path="applications/:application_id"
            element={<EmployerApplicationDetailsPage />}
          />
          <Route path="profile" element={<EmployerProfilePage />} />
          <Route path="pricing" element={<EmployerPricingPage />} />
          <Route path="checkout" element={<CheckoutPage />} />
          <Route path="checkout/payment" element={<PaymentPage />} />
          <Route path="checkout/confirmation" element={<ConfirmationPage />} />
        </Route>

        {/* Catch-all route for handling 404 Not Found pages */}
        {/* This will match any URL that doesn't match the above routes */}
        <Route path="*" element={<NotFoundPage />} />
      </Route>
    </Routes>
  );
}
