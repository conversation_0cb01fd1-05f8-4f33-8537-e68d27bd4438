import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import { z } from "zod";
import { createJobSchema } from "../../schemas/employer/create-job-schema";

// Type for form values
type FormValues = z.infer<typeof createJobSchema>;

export default function useCreateJobForm() {
  const form = useForm<FormValues>({
    initialValues: {
      jobTitle: "",
      minSalary: undefined,
      maxSalary: undefined,
      currency: "",
      location: "",
      jobType: "",
      jobDescription: "",
      jobRequirements: "",
      applicationDeadline: undefined,
      requiredExperience: "",
      jobAttachment: undefined,
      // companyName: "",
      // companyWebsite: "",
      // companyLogo: undefined,
      // aboutCompany: "",
      jobCategory: "",
      jobTags: [],
      benefits: [],
      showSalary: false,
      // enableExpiryNotification: false,
      // notificationEmail: "",
      // jobStatus: "",
    },
    validate: zodResolver(createJobSchema),
  });

  const handleSubmit = (values: FormValues) => {
    console.log(values);
  };

  return {
    form,
    handleSubmit,
  };
}
