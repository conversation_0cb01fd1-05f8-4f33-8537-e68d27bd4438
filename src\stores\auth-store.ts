import {
  type AuthState,
  type LoginCredentials,
  type RegisterData,
} from "@/types/auth";
import { notifications } from "@mantine/notifications";
import { create } from "zustand";

// Define the auth store type
interface AuthStore extends AuthState {
  // Actions
  setUser: (user: AuthState["user"]) => void;
  setLoading: (isLoading: boolean) => void;
  setError: (error: string | null) => void;
  setLoggedIn: (isLoggedIn: boolean) => void;
  setAccessToken: (token: string | null) => void;

  // Auth methods
  login: (credentials: LoginCredentials) => Promise<boolean>;
  register: (data: RegisterData) => Promise<boolean>;
  logout: () => Promise<boolean>;
  init: () => Promise<void>;
}

// Create the auth store
export const useAuthStore = create<AuthStore>((set) => ({
  // Initial state
  user: null,
  accessToken: null,
  isLoggedIn: false,
  isLoading: true,
  error: null,

  // Actions
  setUser: (user) => set({ user }),
  setLoading: (isLoading) => set({ isLoading }),
  setError: (error) => set({ error }),
  setLoggedIn: (isLoggedIn) => set({ isLoggedIn }),
  setAccessToken: (accessToken) => set({ accessToken }),

  // Auth methods
  init: async () => {
    try {
      const response = await fetch("/api/auth/me", {
        credentials: "include", // Include cookies in the request
      });
      const data = await response.json();

      if (data.success && data.user) {
        set({
          user: data.user,
          isLoggedIn: true,
          isLoading: false,
          error: null,
        });
      } else {
        set({
          isLoading: false,
        });
      }
    } catch (error) {
      console.error("Failed to load user:", error);
      set({
        isLoading: false,
        error: "Failed to load user",
      });
    }
  },

  login: async (credentials) => {
    try {
      // Set loading state
      set({
        isLoading: true,
        error: null,
      });

      // Make API request
      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(credentials),
        credentials: "include", // Include cookies in the request
      });

      const data = await response.json();

      if (!response.ok) {
        // Handle error
        set({
          isLoading: false,
          error: data.message || "Login failed",
        });

        // Show error notification
        notifications.show({
          title: "Login Failed",
          message: data.message || "Invalid email or password",
          color: "red",
        });

        return false;
      }

      if (!data.user) {
        // Handle missing user data
        set({
          isLoading: false,
          error: "Invalid response from server",
        });

        // Show error notification
        notifications.show({
          title: "Login Failed",
          message: "Invalid response from server",
          color: "red",
        });

        return false;
      }

      // Update state with user data
      set({
        user: data.user,
        isLoggedIn: true,
        isLoading: false,
        error: null,
      });

      // Show success notification
      notifications.show({
        title: "Login Successful",
        message: "Welcome back!",
        color: "green",
      });

      return true;
    } catch (error) {
      console.error("Login error:", error);

      // Update state with error
      set({
        isLoading: false,
        error: "An unexpected error occurred",
      });

      // Show error notification
      notifications.show({
        title: "Login Failed",
        message: "An unexpected error occurred. Please try again.",
        color: "red",
      });

      return false;
    }
  },

  register: async (data) => {
    try {
      // Set loading state
      set({
        isLoading: true,
        error: null,
      });

      // Make API request
      const response = await fetch("/api/auth/register", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
        credentials: "include", // Include cookies in the request
      });

      const responseData = await response.json();

      if (!response.ok) {
        // Handle error
        set({
          isLoading: false,
          error: responseData.message || "Registration failed",
        });

        // Show error notification
        notifications.show({
          title: "Registration Failed",
          message: responseData.message || "Could not create account",
          color: "red",
        });

        return false;
      }

      // Update state with user data
      set({
        user: responseData.user,
        isLoggedIn: true,
        isLoading: false,
        error: null,
      });

      // Show success notification
      notifications.show({
        title: "Registration Successful",
        message: "Your account has been created successfully!",
        color: "green",
      });

      return true;
    } catch (error) {
      console.error("Registration error:", error);

      // Update state with error
      set({
        isLoading: false,
        error: "An unexpected error occurred",
      });

      // Show error notification
      notifications.show({
        title: "Registration Failed",
        message: "An unexpected error occurred. Please try again.",
        color: "red",
      });

      return false;
    }
  },

  logout: async () => {
    try {
      // Set loading state
      set({
        isLoading: true,
      });

      // Make API request
      await fetch("/api/auth/logout", {
        method: "POST",
        credentials: "include", // Include cookies in the request
      });

      // Update state
      set({
        user: null,
        accessToken: null,
        isLoggedIn: false,
        isLoading: false,
        error: null,
      });

      // Show success notification
      notifications.show({
        title: "Logged Out",
        message: "You have been logged out successfully",
        color: "blue",
      });

      return true;
    } catch (error) {
      console.error("Logout error:", error);

      // Update state
      set({
        isLoading: false,
      });

      // Show error notification
      notifications.show({
        title: "Logout Failed",
        message: "An error occurred during logout",
        color: "red",
      });

      return false;
    }
  },
}));
