import EditJobForm from "@/components/employer/forms/EditJobForm";
import { PageContainer, PageHeading } from "@/design-system/components";
import { Box, useMantineColorScheme } from "@mantine/core";
import { useParams } from "react-router";

export default function EmployerEditJobPage() {
  const { jobId } = useParams();
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  return (
    <PageContainer
      breadcrumbItems={[
        { title: "Home", href: "/" },
        { title: "Manage Jobs", href: "/employer/manage-jobs" },
        { title: "Edit Job" },
      ]}
      variant="employer"
      className="pb-12"
    >
      {/* Hero Section */}
      <Box
        className="mb-8 rounded-lg px-4 py-8 md:px-6 md:py-10"
        mx={{ base: -4, sm: -6 }}
        style={{
          background: isDark
            ? "linear-gradient(to right, #1a1b2e, #141b2d, #1a1b2e)"
            : "linear-gradient(to right, #f0f4ff, #e6f0ff, #f0f4ff)",
        }}
      >
        <PageHeading
          title="Edit Job Posting"
          subtitle="Update your job posting to attract the best candidates"
          className="mb-0 text-center"
          variant="employer"
        />
      </Box>

      <EditJobForm jobId={jobId as string} />
    </PageContainer>
  );
}
