import { z } from "zod";

export const createJobSchema = z
  .object({
    jobTitle: z.string().min(2, "Job title must be at least 2 characters"),
    minSalary: z
      .number()
      .min(0, "Minimum salary must be a positive number")
      .optional(),
    maxSalary: z
      .number()
      .min(0, "Maximum salary must be a positive number")
      .optional(),
    currency: z.string().optional(),
    location: z.string().min(1, "Location is required"),
    jobType: z.string().min(1, "Job type is required"),
    jobDescription: z
      .string()
      .min(10, "Description must be at least 10 characters"),
    jobRequirements: z
      .string()
      .min(10, "Requirements must be at least 10 characters"),
    applicationDeadline: z
      .union([
        z.date(),
        z.string().transform((str, ctx) => {
          if (!str || str.trim() === "") {
            return undefined;
          }
          const date = new Date(str);
          if (isNaN(date.getTime())) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: "Invalid date format",
            });
            return z.NEVER;
          }
          return date;
        }),
        z.null().transform(() => undefined),
        z.undefined(),
      ])
      .optional(),
    requiredExperience: z.string().min(1, "Required experience is required"),
    jobAttachment: z.instanceof(File).optional(),
    // companyName: z
    //   .string()
    //   .min(2, "Company name must be at least 2 characters"),
    // companyWebsite: z.string().url("Invalid URL").optional(),
    // companyLogo: z.instanceof(File).optional(),
    // aboutCompany: z
    //   .string()
    //   .min(10, "Description must be at least 10 characters")
    //   .optional(),
    jobCategory: z.string().min(1, "Job category is required"),
    jobTags: z.array(z.string()).optional(),
    benefits: z.array(z.string()).optional(),
    showSalary: z.boolean().optional(),
    // enableExpiryNotification: z.boolean().optional(),
    // notificationEmail: z.string().email("Invalid email").optional(),
    // jobStatus: z.string().min(1, "Job status is required"),
  })
  .refine(
    (data) => {
      // If minSalary or maxSalary is provided, currency is required
      if (data.minSalary !== undefined || data.maxSalary !== undefined) {
        return !!data.currency; // Currency must be provided
      }
      return true; // Otherwise, currency is optional
    },
    {
      message: "Currency is required when salary is provided",
      path: ["currency"], // Attach the error to the currency field
    },
  )
  .refine(
    (data) => {
      // If both minSalary and maxSalary are provided, maxSalary must be >= minSalary
      if (data.minSalary !== undefined && data.maxSalary !== undefined) {
        return data.maxSalary >= data.minSalary;
      }
      return true; // Otherwise, no validation is needed
    },
    {
      message: "Maximum salary must be greater than or equal to minimum salary",
      path: ["maxSalary"], // Attach the error to the maxSalary field
    },
  );
