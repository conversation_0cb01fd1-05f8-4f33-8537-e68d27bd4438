import { mockJobDetails } from "@/data/job-details";
import { PageContainer } from "@/design-system/components";
import { useThemeClasses } from "@/design-system/utils/theme-utils";
import {
  Avatar,
  Badge,
  Button,
  Card,
  Grid,
  Group,
  List,
  Progress,
  Stack,
  Text,
  ThemeIcon,
  Title,
  Tooltip,
} from "@mantine/core";
import {
  BiBriefcase,
  BiBuildings,
  BiCalendar,
  BiCheckCircle,
  BiDollar,
  BiGlobe,
  BiGroup,
  BiInfoCircle,
  BiMapPin,
  BiSolidGraduation,
  BiTime,
  BiTrophy,
  BiUser,
} from "react-icons/bi";
import { FaCheck, FaRegBookmark, FaRegShareSquare } from "react-icons/fa";
import { useNavigate } from "react-router";

export default function CandidateJobDetailsPage() {
  const navigate = useNavigate();
  const jobDetails = mockJobDetails;

  const handleApply = () => {
    navigate(`/candidate/jobs/${jobDetails.id}/apply`);
  };

  // Calculate match score based on skills (for demo purposes)
  const matchScore = 92;

  return (
    <PageContainer
      variant="candidate"
      breadcrumbItems={[
        { title: "Home", href: "/" },
        { title: "Jobs", href: "/candidate/jobs" },
        { title: "Job Details" },
      ]}
    >
      {/* Hero Section */}
      <Card shadow="sm" radius="md" withBorder className="mb-8 overflow-hidden">
        <div className="relative">
          {/* Background gradient */}
          <div className="from-primary-color/10 to-primary-color/5 dark:from-primary-color/20 dark:to-primary-color/10 absolute inset-0 bg-gradient-to-r rounded-lg"></div>

          <div className="p-6 md:p-8">
            <div className="flex flex-col gap-6 md:flex-row md:items-center md:justify-between">
              <div className="flex items-center gap-4">
                <Avatar
                  size={80}
                  radius="md"
                  src={null}
                  color="blue"
                  className="border border-gray-200 dark:border-gray-700 bg-white dark:bg-dark-6"
                  styles={{
                    root: {
                      backgroundColor: "var(--mantine-color-dark-6) !important",
                      borderColor: "var(--mantine-color-dark-4) !important",
                    },
                  }}
                >
                  {jobDetails.company.substring(0, 2).toUpperCase()}
                </Avatar>

                <div className="space-y-2">
                  <Title
                    order={1}
                    className="text-primary-color text-2xl font-bold md:text-3xl"
                  >
                    {jobDetails.title}
                  </Title>
                  <Group gap="lg">
                    <Group gap="xs">
                      <BiBuildings className="text-primary-color h-5 w-5" />
                      <Text className="font-medium text-gray-800 dark:text-gray-200">
                        {jobDetails.company}
                      </Text>
                    </Group>
                    <Group gap="xs">
                      <BiMapPin className="text-primary-color h-5 w-5" />
                      <Text className="text-gray-800 dark:text-gray-200">
                        {jobDetails.location}
                      </Text>
                    </Group>
                  </Group>
                </div>
              </div>

              <div className="flex flex-wrap items-center gap-3">
                <Tooltip label="Save Job">
                  <Button variant="outline" color="gray" radius="xl" size="md">
                    <FaRegBookmark className="h-5 w-5" />
                  </Button>
                </Tooltip>
                <Tooltip label="Share Job">
                  <Button variant="outline" color="gray" radius="xl" size="md">
                    <FaRegShareSquare className="h-5 w-5" />
                  </Button>
                </Tooltip>
                <Button
                  onClick={handleApply}
                  leftSection={<BiBriefcase className="h-5 w-5" />}
                  radius="xl"
                  size="md"
                  className="bg-primary-color hover:bg-primary-color/90"
                >
                  Apply Now
                </Button>
              </div>
            </div>

            <div className="mt-6 flex flex-wrap items-center gap-4 border-t border-gray-200 dark:border-gray-700 pt-4">
              <Badge color="blue" size="lg" radius="sm" className="px-3 py-1.5">
                {jobDetails.type}
              </Badge>
              <Text
                size="sm"
                className="text-gray-700 dark:text-gray-300 flex items-center"
              >
                <BiCalendar className="mr-1.5 inline h-4 w-4 text-primary-color" />
                Posted: {new Date(jobDetails.postedDate).toLocaleDateString()}
              </Text>
              <Text
                size="sm"
                className="text-gray-700 dark:text-gray-300 flex items-center"
              >
                <BiCalendar className="mr-1.5 inline h-4 w-4 text-primary-color" />
                Deadline: {new Date(jobDetails.deadline).toLocaleDateString()}
              </Text>
              <Badge
                color="indigo"
                size="lg"
                radius="sm"
                className="px-3 py-1.5"
              >
                {jobDetails.experience} Experience
              </Badge>
            </div>
          </div>
        </div>
      </Card>

      <Grid gutter="lg">
        <Grid.Col span={{ base: 12, md: 8 }}>
          <Stack gap="lg">
            {/* Job Description */}
            <Card
              shadow="sm"
              radius="md"
              withBorder
              className="overflow-hidden"
            >
              <div className="border-b border-gray-100 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30 p-4">
                <Group gap="xs">
                  <ThemeIcon size="lg" radius="md" color="blue" variant="light">
                    <BiInfoCircle className="h-5 w-5" />
                  </ThemeIcon>
                  <Title
                    order={2}
                    className="text-xl font-bold text-gray-800 dark:text-gray-100"
                  >
                    Job Description
                  </Title>
                </Group>
              </div>
              <div className="p-5">
                <Text className="leading-relaxed whitespace-pre-line text-gray-900 dark:text-gray-300">
                  {jobDetails.description}
                </Text>
              </div>
            </Card>

            {/* Requirements */}
            <Card
              shadow="sm"
              radius="md"
              withBorder
              className="overflow-hidden"
            >
              <div className="border-b border-gray-100 dark:border-gray-700 bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900/30 dark:to-indigo-900/30 p-4">
                <Group gap="xs">
                  <ThemeIcon
                    size="lg"
                    radius="md"
                    color="indigo"
                    variant="light"
                  >
                    <BiSolidGraduation className="h-5 w-5" />
                  </ThemeIcon>
                  <Title
                    order={2}
                    className="text-xl font-bold text-gray-800 dark:text-gray-100"
                  >
                    Requirements
                  </Title>
                </Group>
              </div>
              <div className="p-5">
                <List
                  spacing="sm"
                  icon={
                    <ThemeIcon color="indigo" size="sm" radius="xl">
                      <FaCheck size={10} />
                    </ThemeIcon>
                  }
                >
                  {jobDetails.requirements.map((req, index) => (
                    <List.Item
                      key={index}
                      className={useThemeClasses(
                        "text-gray-900 font-medium",
                        "text-gray-300 font-medium",
                      )}
                    >
                      {req}
                    </List.Item>
                  ))}
                </List>
              </div>
            </Card>

            {/* Skills */}
            <Card
              shadow="sm"
              radius="md"
              withBorder
              className="overflow-hidden"
            >
              <div className="border-b border-gray-100 dark:border-gray-700 bg-gradient-to-r from-cyan-50 to-blue-50 dark:from-cyan-900/30 dark:to-blue-900/30 p-4">
                <Group gap="xs">
                  <ThemeIcon size="lg" radius="md" color="cyan" variant="light">
                    <BiGlobe className="h-5 w-5" />
                  </ThemeIcon>
                  <Title
                    order={2}
                    className="text-xl font-bold text-gray-800 dark:text-gray-100"
                  >
                    Required Skills
                  </Title>
                </Group>
              </div>
              <div className="p-5">
                <div className="flex flex-wrap gap-2">
                  {jobDetails.skills.map((skill, index) => (
                    <Badge
                      key={index}
                      size="lg"
                      radius="sm"
                      variant="light"
                      color="cyan"
                      className="px-3 py-1.5"
                    >
                      {skill}
                    </Badge>
                  ))}
                </div>
              </div>
            </Card>

            {/* Benefits */}
            <Card
              shadow="sm"
              radius="md"
              withBorder
              className="overflow-hidden"
            >
              <div className="border-b border-gray-100 dark:border-gray-700 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/30 dark:to-emerald-900/30 p-4">
                <Group gap="xs">
                  <ThemeIcon size="lg" radius="md" color="teal" variant="light">
                    <BiGroup className="h-5 w-5" />
                  </ThemeIcon>
                  <Title
                    order={2}
                    className="text-xl font-bold text-gray-800 dark:text-gray-100"
                  >
                    Benefits
                  </Title>
                </Group>
              </div>
              <div className="p-5">
                <List
                  spacing="sm"
                  icon={
                    <ThemeIcon color="teal" size="sm" radius="xl">
                      <FaCheck size={10} />
                    </ThemeIcon>
                  }
                >
                  {jobDetails.benefits.map((benefit, index) => (
                    <List.Item
                      key={index}
                      className={useThemeClasses(
                        "text-gray-900 font-medium",
                        "text-gray-300 font-medium",
                      )}
                    >
                      {benefit}
                    </List.Item>
                  ))}
                </List>
              </div>
            </Card>
          </Stack>
        </Grid.Col>

        <Grid.Col span={{ base: 12, md: 4 }}>
          <Stack gap="lg">
            {/* Match Score Card */}
            <Card
              shadow="sm"
              radius="md"
              withBorder
              className="overflow-hidden"
            >
              <div className="border-b border-gray-100 dark:border-gray-700 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/30 dark:to-emerald-900/30 p-4">
                <Group gap="xs">
                  <ThemeIcon
                    size="lg"
                    radius="md"
                    color="green"
                    variant="light"
                  >
                    <BiTrophy className="h-5 w-5" />
                  </ThemeIcon>
                  <Title
                    order={2}
                    className="text-xl font-bold text-gray-800 dark:text-gray-100"
                  >
                    Match Score
                  </Title>
                </Group>
              </div>
              <div className="p-5">
                <div className="mb-2 text-center">
                  <Text size="xl" fw={700} className="text-green-600">
                    {matchScore}%
                  </Text>
                  <Text size="sm" c="dimmed">
                    Match with your profile
                  </Text>
                </div>
                <Progress
                  value={matchScore}
                  color={
                    matchScore > 80
                      ? "green"
                      : matchScore > 60
                        ? "yellow"
                        : "red"
                  }
                  size="xl"
                  radius="xl"
                  className="mb-3"
                />
                <Text
                  size="sm"
                  className="text-center text-gray-700 dark:text-gray-400"
                >
                  Your skills and experience are a great match for this
                  position!
                </Text>
              </div>
            </Card>

            {/* Job Details Card */}
            <Card
              shadow="sm"
              radius="md"
              withBorder
              className="overflow-hidden"
            >
              <div className="border-b border-gray-100 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30 p-4">
                <Group gap="xs">
                  <ThemeIcon size="lg" radius="md" color="blue" variant="light">
                    <BiInfoCircle className="h-5 w-5" />
                  </ThemeIcon>
                  <Title
                    order={2}
                    className="text-xl font-bold text-gray-800 dark:text-gray-100"
                  >
                    Job Details
                  </Title>
                </Group>
              </div>
              <div className="p-5">
                <div className="space-y-4">
                  {jobDetails.salary && (
                    <Group gap="xs">
                      <ThemeIcon
                        size="md"
                        radius="xl"
                        color="green"
                        variant="light"
                      >
                        <BiDollar className="h-4 w-4" />
                      </ThemeIcon>
                      <Text className="text-gray-900 font-medium dark:text-gray-300">
                        <span className="font-medium">Salary:</span>{" "}
                        {jobDetails.salary}
                      </Text>
                    </Group>
                  )}
                  <Group gap="xs">
                    <ThemeIcon
                      size="md"
                      radius="xl"
                      color="blue"
                      variant="light"
                    >
                      <BiTime className="h-4 w-4" />
                    </ThemeIcon>
                    <Text className="text-gray-900 font-medium dark:text-gray-300">
                      <span className="font-medium">Job Type:</span>{" "}
                      {jobDetails.type}
                    </Text>
                  </Group>
                  <Group gap="xs">
                    <ThemeIcon
                      size="md"
                      radius="xl"
                      color="indigo"
                      variant="light"
                    >
                      <BiCheckCircle className="h-4 w-4" />
                    </ThemeIcon>
                    <Text className="text-gray-900 font-medium dark:text-gray-300">
                      <span className="font-medium">Status:</span>{" "}
                      {jobDetails.status}
                    </Text>
                  </Group>
                  <Group gap="xs">
                    <ThemeIcon
                      size="md"
                      radius="xl"
                      color="violet"
                      variant="light"
                    >
                      <BiUser className="h-4 w-4" />
                    </ThemeIcon>
                    <Text className="text-gray-900 font-medium dark:text-gray-300">
                      <span className="font-medium">Experience:</span>{" "}
                      {jobDetails.experience}
                    </Text>
                  </Group>
                </div>
              </div>
            </Card>

            {/* Company Information */}
            <Card
              shadow="sm"
              radius="md"
              withBorder
              className="overflow-hidden"
            >
              <div className="border-b border-gray-100 dark:border-gray-700 bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/30 dark:to-orange-900/30 p-4">
                <Group gap="xs">
                  <ThemeIcon
                    size="lg"
                    radius="md"
                    color="orange"
                    variant="light"
                  >
                    <BiBuildings className="h-5 w-5" />
                  </ThemeIcon>
                  <Title
                    order={2}
                    className="text-xl font-bold text-gray-800 dark:text-gray-100"
                  >
                    About {jobDetails.companyInfo.name}
                  </Title>
                </Group>
              </div>
              <div className="p-5">
                <Text className="!mb-4 text-gray-900 dark:text-gray-300">
                  {jobDetails.companyInfo.description}
                </Text>
                <div className="space-y-2">
                  <Group gap="xs">
                    <ThemeIcon
                      size="sm"
                      radius="xl"
                      color="orange"
                      variant="light"
                    >
                      <FaCheck size={8} />
                    </ThemeIcon>
                    <Text
                      size="sm"
                      className="text-gray-900 dark:text-gray-300"
                    >
                      <span className="font-medium">Industry:</span>{" "}
                      {jobDetails.companyInfo.industry}
                    </Text>
                  </Group>
                  <Group gap="xs">
                    <ThemeIcon
                      size="sm"
                      radius="xl"
                      color="orange"
                      variant="light"
                    >
                      <FaCheck size={8} />
                    </ThemeIcon>
                    <Text
                      size="sm"
                      className="text-gray-900 dark:text-gray-300"
                    >
                      <span className="font-medium">Company Size:</span>{" "}
                      {jobDetails.companyInfo.size}
                    </Text>
                  </Group>
                  <Group gap="xs">
                    <ThemeIcon
                      size="sm"
                      radius="xl"
                      color="orange"
                      variant="light"
                    >
                      <FaCheck size={8} />
                    </ThemeIcon>
                    <Text
                      size="sm"
                      className="text-gray-900 dark:text-gray-300"
                    >
                      <span className="font-medium">Founded:</span>{" "}
                      {jobDetails.companyInfo.founded}
                    </Text>
                  </Group>
                  <Group gap="xs">
                    <ThemeIcon
                      size="sm"
                      radius="xl"
                      color="orange"
                      variant="light"
                    >
                      <FaCheck size={8} />
                    </ThemeIcon>
                    <Text
                      size="sm"
                      className="text-gray-900 dark:text-gray-300"
                    >
                      <span className="font-medium">Website:</span>{" "}
                      <a
                        href={`https://${jobDetails.companyInfo.website}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary-color hover:underline"
                      >
                        {jobDetails.companyInfo.website}
                      </a>
                    </Text>
                  </Group>
                </div>
              </div>
            </Card>
          </Stack>
        </Grid.Col>
      </Grid>
    </PageContainer>
  );
}
