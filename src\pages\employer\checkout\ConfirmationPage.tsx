import { <PERSON><PERSON><PERSON><PERSON>, PageHeading } from "@/design-system/components";
import { URLS } from "@/utils/urls";
import {
  Box,
  Button,
  Card,
  Divider,
  Grid,
  Group,
  List,
  Stepper,
  Text,
  ThemeIcon,
  Title,
} from "@mantine/core";
import { notifications } from "@mantine/notifications";
import { useEffect, useState } from "react";
import {
  FaCalendarAlt,
  FaCheck,
  FaCheckCircle,
  FaCreditCard,
  FaDownload,
  FaEnvelope,
  FaInfoCircle,
  FaUser,
} from "react-icons/fa";
import { useNavigate, useSearchParams } from "react-router";

// Plan data - in a real app, this would come from an API or context
const plans = {
  basic: {
    name: "Basic",
    price: 29,
    annualPrice: 295,
    features: [
      "2 active job postings",
      "30-day job visibility",
      "50 CV views per month",
      "Basic candidate filtering",
      "Email notifications",
    ],
  },
  professional: {
    name: "Professional",
    price: 59,
    annualPrice: 565,
    features: [
      "5 active job postings",
      "45-day job visibility",
      "200 CV views per month",
      "Advanced candidate filtering",
      "Priority placement",
    ],
  },
  enterprise: {
    name: "Enterprise",
    price: 149,
    annualPrice: 1341,
    features: [
      "Unlimited active job postings",
      "60-day job visibility",
      "Unlimited CV views",
      "Advanced analytics and reporting",
      "Dedicated account manager",
    ],
  },
};

export default function ConfirmationPage() {
  const [searchParams] = useSearchParams();
  const planId = searchParams.get("plan") || "professional";
  const billingPeriod = searchParams.get("billing") || "month";
  const plan = plans[planId as keyof typeof plans];
  const navigate = useNavigate();

  // Order details
  const [orderNumber, setOrderNumber] = useState<string>("");
  const [orderDate, setOrderDate] = useState<string>("");
  const [isProcessing, setIsProcessing] = useState<boolean>(true);

  // Format price with currency
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      maximumFractionDigits: 0,
    }).format(price);
  };

  // Get price based on billing period
  const getPrice = () => {
    if (billingPeriod === "month") {
      return plan.price;
    } else {
      return plan.annualPrice;
    }
  };

  // Calculate tax (example: 10%)
  const calculateTax = () => {
    return getPrice() * 0.1;
  };

  // Calculate total
  const calculateTotal = () => {
    return getPrice() + calculateTax();
  };

  // Generate order details
  useEffect(() => {
    // Simulate order processing
    const timer = setTimeout(() => {
      // Generate random order number
      const randomOrderNumber = `ORD-${Math.floor(100000 + Math.random() * 900000)}`;
      setOrderNumber(randomOrderNumber);

      // Set current date
      const now = new Date();
      setOrderDate(
        now.toLocaleDateString("en-US", {
          year: "numeric",
          month: "long",
          day: "numeric",
        }),
      );

      setIsProcessing(false);

      // Show success notification
      notifications.show({
        title: "Payment Successful",
        message: "Your subscription has been activated",
        color: "green",
        icon: <FaCheckCircle />,
      });
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  // Handle continue to dashboard
  const handleContinue = () => {
    navigate(URLS.employer.dashboard);
  };

  return (
    <PageContainer
      breadcrumbItems={[
        { title: "Home", href: "/" },
        { title: "Pricing", href: URLS.employer.pricing },
        {
          title: "Checkout",
          href: `/employer/checkout?plan=${planId}&billing=${billingPeriod}`,
        },
        { title: "Confirmation" },
      ]}
      variant="employer"
      className="pb-12"
    >
      <PageHeading
        title="Order Confirmation"
        subtitle="Thank you for your purchase"
        className="mb-8"
        variant="employer"
      />

      {/* Checkout Process Stepper */}
      <Stepper
        active={2}
        className="mb-8"
        color="primary"
        onStepClick={(step) => {
          // Only allow clicking on previous steps
          if (step < 2) {
            if (step === 0) {
              // Navigate back to billing information
              navigate(
                `/employer/checkout?plan=${planId}&billing=${billingPeriod}`,
              );
            } else if (step === 1) {
              // Navigate back to payment method
              navigate(
                `/employer/checkout/payment?plan=${planId}&billing=${billingPeriod}`,
              );
            }
          }
        }}
        allowNextStepsSelect={false}
      >
        <Stepper.Step
          label="Billing Information"
          description="Enter your details"
          icon={<FaUser size={18} />}
          state="stepCompleted"
        />
        <Stepper.Step
          label="Payment Method"
          description="Choose how to pay"
          icon={<FaCreditCard size={18} />}
          state="stepCompleted"
        />
        <Stepper.Step
          label="Confirmation"
          description="Review and confirm"
          icon={<FaCheck size={18} />}
        />
      </Stepper>

      {isProcessing ? (
        <Card withBorder radius="md" className="text-center p-8">
          <Title order={3} className="mb-4">
            Processing Your Order
          </Title>
          <Text className="mb-8">
            Please wait while we process your payment and activate your
            subscription...
          </Text>
          <div className="flex justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
          </div>
        </Card>
      ) : (
        <Grid gutter="xl">
          {/* Left Column - Confirmation Details */}
          <Grid.Col span={{ base: 12, md: 8 }}>
            <Card withBorder radius="md" className="mb-4">
              <Box className="bg-green-50 p-4 rounded-md mb-6 text-center">
                <ThemeIcon
                  color="green"
                  size="xl"
                  radius="xl"
                  className="mb-2 mx-auto"
                >
                  <FaCheckCircle size={20} />
                </ThemeIcon>
                <Title order={3} className="mb-2">
                  Payment Successful
                </Title>
                <Text>
                  Your {plan.name} plan has been activated successfully.
                </Text>
              </Box>

              <Grid>
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <Text fw={500} className="mb-1">
                    Order Number
                  </Text>
                  <Text className="mb-4">{orderNumber}</Text>
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <Text fw={500} className="mb-1">
                    Order Date
                  </Text>
                  <Text className="mb-4">{orderDate}</Text>
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <Text fw={500} className="mb-1">
                    Plan
                  </Text>
                  <Text className="mb-4">
                    {plan.name} (
                    {billingPeriod === "month" ? "Monthly" : "Annual"})
                  </Text>
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <Text fw={500} className="mb-1">
                    Amount Paid
                  </Text>
                  <Text className="mb-4">{formatPrice(calculateTotal())}</Text>
                </Grid.Col>
                <Grid.Col span={12}>
                  <Text fw={500} className="mb-1">
                    Payment Method
                  </Text>
                  <Text className="mb-4">
                    {searchParams.get("method") === "vodafone"
                      ? "Vodafone Cash"
                      : "Bank Transfer"}
                  </Text>
                </Grid.Col>
              </Grid>

              <Divider my="md" />

              <Title order={4} className="mb-4">
                What&apos;s Next?
              </Title>
              <List
                spacing="sm"
                size="sm"
                icon={
                  <ThemeIcon color="primary" size={24} radius="xl">
                    <FaCheck size={12} />
                  </ThemeIcon>
                }
                className="mb-6"
              >
                <List.Item>
                  <Text fw={500}>Access Your Dashboard</Text>
                  <Text size="sm">
                    Start using your new features by visiting your employer
                    dashboard.
                  </Text>
                </List.Item>
                <List.Item>
                  <Text fw={500}>Post Your Jobs</Text>
                  <Text size="sm">
                    Create and publish job listings to start attracting
                    candidates.
                  </Text>
                </List.Item>
                <List.Item>
                  <Text fw={500}>Check Your Email</Text>
                  <Text size="sm">
                    We&apos;ve sent a receipt and subscription details to your
                    email.
                  </Text>
                </List.Item>
              </List>

              <Group justify="center" gap="md">
                <Button
                  size="md"
                  leftSection={<FaDownload size={14} />}
                  variant="outline"
                >
                  Download Receipt
                </Button>
                <Button
                  size="md"
                  leftSection={<FaEnvelope size={14} />}
                  variant="outline"
                >
                  Email Receipt
                </Button>
                <Button size="md" onClick={handleContinue}>
                  Go to Dashboard
                </Button>
              </Group>
            </Card>
          </Grid.Col>

          {/* Right Column - Subscription Details */}
          <Grid.Col span={{ base: 12, md: 4 }}>
            <Card withBorder radius="md" className="bg-gray-50">
              <Title order={3} className="mb-4">
                Subscription Details
              </Title>
              <Box className="mb-4">
                <Group justify="space-between" className="mb-2">
                  <Text fw={500}>{plan.name} Plan</Text>
                  <Text fw={500}>{formatPrice(getPrice())}</Text>
                </Group>
                <Text size="sm" c="dimmed">
                  {billingPeriod === "month" ? "Monthly" : "Annual"}{" "}
                  subscription
                </Text>
              </Box>

              <List
                spacing="xs"
                size="sm"
                center
                icon={
                  <ThemeIcon color="green" size={20} radius="xl">
                    <FaCheck size={10} />
                  </ThemeIcon>
                }
                className="mb-4"
              >
                {plan.features.map((feature, index) => (
                  <List.Item key={index}>{feature}</List.Item>
                ))}
              </List>

              <Divider my="md" />

              <Box className="mb-4">
                <Group gap="xs" align="center" className="mb-2">
                  <FaCalendarAlt size={14} className="text-gray-500" />
                  <Text fw={500}>Billing Cycle</Text>
                </Group>
                <Text size="sm">
                  {billingPeriod === "month"
                    ? "You will be billed monthly. Next billing date: "
                    : "You will be billed annually. Next billing date: "}
                  {new Date(
                    new Date().setMonth(
                      new Date().getMonth() +
                        (billingPeriod === "month" ? 1 : 12),
                    ),
                  ).toLocaleDateString("en-US", {
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                  })}
                </Text>
              </Box>

              <Box className="mt-6 bg-primary-50 p-3 rounded-md">
                <Group gap="xs">
                  <ThemeIcon
                    color="primary"
                    variant="light"
                    size="md"
                    radius="xl"
                  >
                    <FaInfoCircle size={14} />
                  </ThemeIcon>
                  <Text size="sm" fw={500}>
                    Need to make changes?
                  </Text>
                </Group>
                <Text size="xs" c="dimmed" className="mt-2">
                  You can manage your subscription, update payment methods, or
                  cancel at any time from your account settings.
                </Text>
              </Box>
            </Card>
          </Grid.Col>
        </Grid>
      )}
    </PageContainer>
  );
}
