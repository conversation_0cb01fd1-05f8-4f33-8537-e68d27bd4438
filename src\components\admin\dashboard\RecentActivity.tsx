import SectionHeading from "@/design-system/components/typography/SectionHeading";
import { Badge, Group, Tabs, Text, Timeline } from "@mantine/core";
import {
  FaBriefcase,
  FaBuilding,
  FaCheckCircle,
  FaExclamationCircle,
  FaUserPlus,
} from "react-icons/fa";

const recentActivities = [
  {
    id: 1,
    type: "user",
    title: "New User Registration",
    description: "<PERSON> registered as a candidate",
    timestamp: "2 hours ago",
    user: {
      name: "<PERSON>",
      avatar: "/images/avatar-placeholder.png",
      role: "candidate",
    },
  },
  {
    id: 2,
    type: "job",
    title: "New Job Posted",
    description: "Senior Developer position at Tech Solutions",
    timestamp: "4 hours ago",
    company: "Tech Solutions",
  },
  {
    id: 3,
    type: "company",
    title: "Company Verification",
    description: "Global Innovations verified their account",
    timestamp: "6 hours ago",
    company: "Global Innovations",
  },
  {
    id: 4,
    type: "job",
    title: "Job Approved",
    description: "Marketing Specialist at Creative Agency",
    timestamp: "8 hours ago",
    company: "Creative Agency",
  },
  {
    id: 5,
    type: "alert",
    title: "System Alert",
    description: "Unusual login activity detected",
    timestamp: "12 hours ago",
    severity: "warning",
  },
];

export default function RecentActivity() {
  const getTimelineIcon = (type: string) => {
    switch (type) {
      case "user":
        return <FaUserPlus size={14} />;
      case "job":
        return <FaBriefcase size={14} />;
      case "company":
        return <FaBuilding size={14} />;
      case "alert":
        return <FaExclamationCircle size={14} />;
      default:
        return <FaCheckCircle size={14} />;
    }
  };

  const getTimelineColor = (type: string) => {
    switch (type) {
      case "user":
        return "blue";
      case "job":
        return "green";
      case "company":
        return "purple";
      case "alert":
        return "orange";
      default:
        return "gray";
    }
  };

  return (
    <>
      <SectionHeading variant="admin">Recent Activity</SectionHeading>
      <Tabs defaultValue="all" mt="md">
        <Tabs.List>
          <Tabs.Tab value="all">All</Tabs.Tab>
          <Tabs.Tab value="users">Users</Tabs.Tab>
          <Tabs.Tab value="jobs">Jobs</Tabs.Tab>
          <Tabs.Tab value="companies">Companies</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="all" pt="md">
          <Timeline
            active={recentActivities.length - 1}
            bulletSize={24}
            lineWidth={2}
          >
            {recentActivities.map((activity) => (
              <Timeline.Item
                key={activity.id}
                bullet={getTimelineIcon(activity.type)}
                title={
                  <Text fw={600} size="sm">
                    {activity.title}
                  </Text>
                }
                color={getTimelineColor(activity.type)}
              >
                <Text size="sm">{activity.description}</Text>
                <Group gap="xs" mt={4}>
                  <Text size="xs" c="dimmed">
                    {activity.timestamp}
                  </Text>
                  {activity.user && (
                    <Badge size="xs" color="blue" variant="light">
                      {activity.user.role}
                    </Badge>
                  )}
                </Group>
              </Timeline.Item>
            ))}
          </Timeline>
        </Tabs.Panel>

        <Tabs.Panel value="users" pt="md">
          <Timeline active={1} bulletSize={24} lineWidth={2}>
            {recentActivities
              .filter((activity) => activity.type === "user")
              .map((activity) => (
                <Timeline.Item
                  key={activity.id}
                  bullet={getTimelineIcon(activity.type)}
                  title={
                    <Text fw={600} size="sm">
                      {activity.title}
                    </Text>
                  }
                  color={getTimelineColor(activity.type)}
                >
                  <Text size="sm">{activity.description}</Text>
                  <Group gap="xs" mt={4}>
                    <Text size="xs" c="dimmed">
                      {activity.timestamp}
                    </Text>
                    {activity.user && (
                      <Badge size="xs" color="blue" variant="light">
                        {activity.user.role}
                      </Badge>
                    )}
                  </Group>
                </Timeline.Item>
              ))}
          </Timeline>
        </Tabs.Panel>

        <Tabs.Panel value="jobs" pt="md">
          <Timeline active={1} bulletSize={24} lineWidth={2}>
            {recentActivities
              .filter((activity) => activity.type === "job")
              .map((activity) => (
                <Timeline.Item
                  key={activity.id}
                  bullet={getTimelineIcon(activity.type)}
                  title={
                    <Text fw={600} size="sm">
                      {activity.title}
                    </Text>
                  }
                  color={getTimelineColor(activity.type)}
                >
                  <Text size="sm">{activity.description}</Text>
                  <Text size="xs" c="dimmed" mt={4}>
                    {activity.timestamp}
                  </Text>
                </Timeline.Item>
              ))}
          </Timeline>
        </Tabs.Panel>

        <Tabs.Panel value="companies" pt="md">
          <Timeline active={0} bulletSize={24} lineWidth={2}>
            {recentActivities
              .filter((activity) => activity.type === "company")
              .map((activity) => (
                <Timeline.Item
                  key={activity.id}
                  bullet={getTimelineIcon(activity.type)}
                  title={
                    <Text fw={600} size="sm">
                      {activity.title}
                    </Text>
                  }
                  color={getTimelineColor(activity.type)}
                >
                  <Text size="sm">{activity.description}</Text>
                  <Text size="xs" c="dimmed" mt={4}>
                    {activity.timestamp}
                  </Text>
                </Timeline.Item>
              ))}
          </Timeline>
        </Tabs.Panel>
      </Tabs>
    </>
  );
}
