import { useConfirmDeleteJobModal } from "@/stores/modal-store";
import { Button, Group, Modal, Text } from "@mantine/core";

export default function DeleteJobModal() {
  const { close, isOpen } = useConfirmDeleteJobModal();

  const closeModal = () => {
    close();
  };

  return (
    <Modal
      opened={isOpen}
      onClose={closeModal}
      title="Confirm Deletion"
      centered
    >
      <Text>Are you sure you want to delete this job?</Text>
      <Group mt="md" justify="end">
        <Button variant="outline" onClick={closeModal}>
          Cancel
        </Button>
        <Button
          color="red"
          onClick={() => {
            closeModal();
          }}
        >
          Delete
        </Button>
      </Group>
    </Modal>
  );
}
