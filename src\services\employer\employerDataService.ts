// Mock data and service functions for employer module
// In a real application, these would be API calls

export interface Job {
  id: string;
  title: string;
  company: string;
  location: string;
  jobType: string;
  salary?: {
    min: number;
    max: number;
    currency: string;
  };
  postedDate: string;
  applicants: number;
  status: "active" | "paused" | "closed" | "draft";
  description: string;
  requirements: string;
  benefits: string[];
  tags: string[];
  category: string;
  experience: string;
  applicationDeadline?: string;
}

export interface Application {
  id: string;
  candidateName: string;
  candidateEmail: string;
  candidatePhone?: string;
  candidateLocation?: string;
  candidateAvatar?: string;
  jobTitle: string;
  jobId: string;
  appliedDate: string;
  status: "pending" | "approved" | "rejected";
  experience?: string;
  education?: string;
  resumeUrl?: string;
  coverLetter?: string;
}

// Mock data
const mockJobs: Job[] = [
  {
    id: "1",
    title: "Senior Frontend Developer",
    company: "TechCorp Inc.",
    location: "Remote",
    jobType: "Full Time",
    salary: {
      min: 80000,
      max: 120000,
      currency: "USD",
    },
    postedDate: "2 days ago",
    applicants: 15,
    status: "active",
    description:
      "We are looking for a skilled Senior Frontend Developer to join our growing team.",
    requirements: "3+ years of experience with React and modern JavaScript",
    benefits: ["Health Insurance", "Remote Work", "Flexible Hours"],
    tags: ["React", "TypeScript", "Next.js"],
    category: "Web Development",
    experience: "3-5 years",
    applicationDeadline: "2024-02-15",
  },
  {
    id: "2",
    title: "Product Manager",
    company: "TechCorp Inc.",
    location: "New York, NY",
    jobType: "Full Time",
    salary: {
      min: 90000,
      max: 140000,
      currency: "USD",
    },
    postedDate: "1 week ago",
    applicants: 8,
    status: "active",
    description: "Lead product strategy and development for our core platform.",
    requirements: "5+ years of product management experience",
    benefits: ["Health Insurance", "Stock Options", "Paid Time Off"],
    tags: ["Product Management", "Strategy", "Analytics"],
    category: "Product Management",
    experience: "5-10 years",
  },
  {
    id: "3",
    title: "UX Designer",
    company: "TechCorp Inc.",
    location: "Hybrid",
    jobType: "Full Time",
    salary: {
      min: 70000,
      max: 100000,
      currency: "USD",
    },
    postedDate: "3 days ago",
    applicants: 12,
    status: "paused",
    description:
      "Create intuitive and beautiful user experiences for our products.",
    requirements: "3+ years of UX design experience",
    benefits: [
      "Health Insurance",
      "Professional Development",
      "Flexible Hours",
    ],
    tags: ["UX Design", "Figma", "User Research"],
    category: "UI/UX Design",
    experience: "3-5 years",
  },
];

const mockApplications: Application[] = [
  {
    id: "1",
    candidateName: "John Smith",
    candidateEmail: "<EMAIL>",
    candidatePhone: "+****************",
    candidateLocation: "San Francisco, CA",
    jobTitle: "Senior Frontend Developer",
    jobId: "1",
    appliedDate: "2 days ago",
    status: "pending",
    experience: "5 years",
    education: "BS Computer Science",
    resumeUrl: "/resumes/john-smith.pdf",
    coverLetter:
      "I am excited to apply for the Senior Frontend Developer position. With 5 years of experience in React and modern web technologies, I believe I would be a great fit for your team.",
  },
  {
    id: "2",
    candidateName: "Sarah Johnson",
    candidateEmail: "<EMAIL>",
    candidatePhone: "+****************",
    candidateLocation: "Austin, TX",
    jobTitle: "Senior Frontend Developer",
    jobId: "1",
    appliedDate: "1 day ago",
    status: "approved",
    experience: "4 years",
    education: "MS Software Engineering",
    resumeUrl: "/resumes/sarah-johnson.pdf",
    coverLetter:
      "I have been following your company's work and am impressed by your commitment to innovation. I would love to contribute to your frontend team.",
  },
  {
    id: "3",
    candidateName: "Mike Chen",
    candidateEmail: "<EMAIL>",
    candidateLocation: "Seattle, WA",
    jobTitle: "Product Manager",
    jobId: "2",
    appliedDate: "3 days ago",
    status: "rejected",
    experience: "6 years",
    education: "MBA",
    resumeUrl: "/resumes/mike-chen.pdf",
    coverLetter:
      "With my background in product management and passion for technology, I am excited about the opportunity to lead product strategy at your company.",
  },
];

// Service functions
export const employerDataService = {
  // Jobs
  async getJobs(): Promise<Job[]> {
    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 500));
    return mockJobs;
  },

  async getJobById(id: string): Promise<Job | null> {
    await new Promise((resolve) => setTimeout(resolve, 300));
    return mockJobs.find((job) => job.id === id) || null;
  },

  async createJob(jobData: Partial<Job>): Promise<Job> {
    await new Promise((resolve) => setTimeout(resolve, 1000));
    const newJob: Job = {
      id: Date.now().toString(),
      title: jobData.title || "",
      company: jobData.company || "TechCorp Inc.",
      location: jobData.location || "",
      jobType: jobData.jobType || "",
      postedDate: "Just now",
      applicants: 0,
      status: "draft",
      description: jobData.description || "",
      requirements: jobData.requirements || "",
      benefits: jobData.benefits || [],
      tags: jobData.tags || [],
      category: jobData.category || "",
      experience: jobData.experience || "",
      ...jobData,
    };
    mockJobs.unshift(newJob);
    return newJob;
  },

  async updateJob(id: string, updates: Partial<Job>): Promise<Job | null> {
    await new Promise((resolve) => setTimeout(resolve, 800));
    const jobIndex = mockJobs.findIndex((job) => job.id === id);
    if (jobIndex === -1) return null;

    mockJobs[jobIndex] = { ...mockJobs[jobIndex], ...updates };
    return mockJobs[jobIndex];
  },

  async deleteJob(id: string): Promise<boolean> {
    await new Promise((resolve) => setTimeout(resolve, 500));
    const jobIndex = mockJobs.findIndex((job) => job.id === id);
    if (jobIndex === -1) return false;

    mockJobs.splice(jobIndex, 1);
    return true;
  },

  // Applications
  async getApplications(jobId?: string): Promise<Application[]> {
    await new Promise((resolve) => setTimeout(resolve, 500));
    if (jobId) {
      return mockApplications.filter((app) => app.jobId === jobId);
    }
    return mockApplications;
  },

  async getApplicationById(id: string): Promise<Application | null> {
    await new Promise((resolve) => setTimeout(resolve, 300));
    return mockApplications.find((app) => app.id === id) || null;
  },

  async updateApplicationStatus(
    id: string,
    status: "pending" | "approved" | "rejected",
  ): Promise<Application | null> {
    await new Promise((resolve) => setTimeout(resolve, 500));
    const appIndex = mockApplications.findIndex((app) => app.id === id);
    if (appIndex === -1) return null;

    mockApplications[appIndex].status = status;
    return mockApplications[appIndex];
  },

  // Analytics
  async getJobStats(): Promise<{
    totalJobs: number;
    activeJobs: number;
    totalApplications: number;
    pendingApplications: number;
  }> {
    await new Promise((resolve) => setTimeout(resolve, 300));
    return {
      totalJobs: mockJobs.length,
      activeJobs: mockJobs.filter((job) => job.status === "active").length,
      totalApplications: mockApplications.length,
      pendingApplications: mockApplications.filter(
        (app) => app.status === "pending",
      ).length,
    };
  },
};
