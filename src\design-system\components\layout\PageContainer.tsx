import Breadcrumbs from "@/components/common/Breadcrumbs";
import { cn } from "@/design-system/utils";
import { type PropsWithChildren } from "react";

export interface PageContainerProps extends PropsWithChildren {
  breadcrumbItems?: Array<{
    title: string;
    href?: string;
  }>;
  showBreadcrumbs?: boolean;
  className?: string;
  variant?: "default" | "admin" | "employer" | "candidate";
}

/**
 * Reusable page container component with optional breadcrumbs
 *
 * @param variant - Different styling variants based on user role
 */
export default function PageContainer({
  children,
  breadcrumbItems,
  showBreadcrumbs = true,
  className = "",
  variant = "default",
}: PageContainerProps) {
  // Apply variant-specific styling
  const variantClasses = {
    default: "p-3 sm:p-4 md:p-6",
    admin: "p-3 sm:p-4 md:p-6",
    employer: "container flex flex-col gap-8 py-8",
    candidate: "space-y-6",
  };

  return (
    <div className={cn(variantClasses[variant], className)}>
      {showBreadcrumbs && <Breadcrumbs items={breadcrumbItems} />}
      {children}
    </div>
  );
}
