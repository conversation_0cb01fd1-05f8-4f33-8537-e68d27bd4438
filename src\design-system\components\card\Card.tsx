import { cn } from "@/design-system/utils";
import { useMantineColorScheme } from "@mantine/core";
import { type PropsWithChildren } from "react";

export interface CardProps extends PropsWithChildren {
  className?: string;
  withBorder?: boolean;
  withShadow?: boolean;
  padding?: "sm" | "md" | "lg" | "xl" | "none";
  radius?: "sm" | "md" | "lg" | "xl" | "none";
  variant?: "default" | "admin" | "employer" | "candidate";
  fullHeight?: boolean;
  fullWidth?: boolean;
}

/**
 * Reusable card component with standardized styling
 *
 * @param variant - Different styling variants based on user role
 * @param fullHeight - Whether the card should take full height
 * @param fullWidth - Whether the card should take full width
 */
export default function Card({
  children,
  className = "",
  withBorder = true,
  withShadow = false,
  padding = "lg",
  radius = "md",
  variant = "default",
  fullHeight = false,
  fullWidth = false,
}: CardProps) {
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";
  const paddingClass = {
    none: "p-0",
    sm: "p-2",
    md: "p-4",
    lg: "p-6",
    xl: "p-8",
  };

  const radiusClass = {
    none: "rounded-none",
    sm: "rounded-sm",
    md: "rounded-md",
    lg: "rounded-lg",
    xl: "rounded-xl",
  };

  // Apply variant-specific styling
  const variantClasses = {
    default: "",
    admin: withShadow === undefined ? "shadow-sm" : "",
    employer: "",
    candidate: "",
  };

  return (
    <div
      className={cn(
        isDark ? "bg-dark-7 border-dark-4" : "bg-white",
        withBorder && "border",
        withShadow && (isDark ? "shadow-dark-lg" : "shadow-sm"),
        paddingClass[padding],
        radiusClass[radius],
        fullHeight && "h-full",
        fullWidth && "w-full",
        variantClasses[variant],
        className,
      )}
    >
      {children}
    </div>
  );
}
