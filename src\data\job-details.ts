export const mockJobDetails = {
  id: 1,
  title: "Senior Full Stack Developer",
  company: "TechCorp Solutions",
  location: "San Francisco, CA (Hybrid)",
  type: "Full-time",
  salary: "$120,000 - $150,000/year",
  status: "Active",
  experience: "5-7 years",
  postedDate: "2024-01-15",
  deadline: "2024-02-15",
  description: `We are seeking an experienced Full Stack Developer to join our dynamic team. You will be responsible for developing and maintaining web applications using modern technologies.

Key Responsibilities:
• Lead development of complex web applications
• Collaborate with cross-functional teams
• Mentor junior developers
• Implement best practices and coding standards
• Participate in code reviews and technical discussions`,
  requirements: [
    "Bachelor's degree in Computer Science or related field",
    "5+ years of experience with React and Node.js",
    "Strong understanding of front-end technologies",
    "Experience with cloud platforms (AWS/Azure)",
    "Excellent problem-solving skills",
    "Strong communication abilities",
  ],
  benefits: [
    "Competitive salary package",
    "Health, dental, and vision insurance",
    "401(k) matching",
    "Flexible working hours",
    "Remote work options",
    "Professional development budget",
  ],
  skills: [
    "React",
    "Node.js",
    "TypeScript",
    "AWS",
    "Docker",
    "MongoDB",
    "GraphQL",
    "REST APIs",
  ],
  companyInfo: {
    name: "TechCorp Solutions",
    description:
      "TechCorp Solutions is a leading software development company specializing in enterprise solutions. With over 10 years of experience, we've helped numerous Fortune 500 companies transform their digital presence.",
    industry: "Information Technology",
    size: "500-1000 employees",
    founded: "2010",
    website: "www.techcorp-solutions.com",
    location: "San Francisco, CA",
  },
  questions: [
    {
      id: 1,
      question: "How many years of experience do you have with React?",
      type: "text",
      required: true,
    },
    {
      id: 2,
      question: "Are you willing to relocate?",
      type: "radio",
      options: ["Yes", "No"],
      required: true,
    },
    {
      id: 3,
      question: "What programming languages are you proficient in?",
      type: "checkbox",
      options: ["JavaScript", "TypeScript", "Python", "Java", "C++"],
      required: false,
    },
    {
      id: 4,
      question:
        "Describe your most challenging project and how you handled it.",
      type: "textarea",
      required: true,
    },
  ],
};
