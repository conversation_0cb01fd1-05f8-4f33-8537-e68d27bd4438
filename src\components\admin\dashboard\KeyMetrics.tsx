import MetricCard from "@/design-system/components/card/MetricCard";
import { useThemeClasses } from "@/design-system/utils/theme-utils";
import { FaBriefcase, FaBuilding, FaUsers } from "react-icons/fa";

export default function KeyMetrics() {
  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
      <MetricCard
        icon={FaUsers}
        title="Total Users"
        value={250}
        trend={{ value: 8, isPositive: true }}
        variant="admin"
        className={useThemeClasses(
          "border-blue-100 bg-gradient-to-br from-blue-50 to-white",
          "border-blue-900/30 bg-dark-6",
        )}
      />
      <MetricCard
        icon={FaBriefcase}
        title="Total Jobs"
        value={120}
        trend={{ value: 15, isPositive: true }}
        variant="admin"
        className={useThemeClasses(
          "border-green-100 bg-gradient-to-br from-green-50 to-white",
          "border-green-900/30 bg-dark-6",
        )}
      />
      <MetricCard
        icon={FaBuilding}
        title="Total Companies"
        value={45}
        trend={{ value: 5, isPositive: true }}
        variant="admin"
        className={useThemeClasses(
          "border-purple-100 bg-gradient-to-br from-purple-50 to-white",
          "border-purple-900/30 bg-dark-6",
        )}
      />
      <MetricCard
        icon={FaUsers}
        title="Active Candidates"
        value={180}
        trend={{ value: 12, isPositive: true }}
        variant="admin"
        className={useThemeClasses(
          "border-amber-100 bg-gradient-to-br from-amber-50 to-white",
          "border-amber-900/30 bg-dark-6",
        )}
      />
    </div>
  );
}
