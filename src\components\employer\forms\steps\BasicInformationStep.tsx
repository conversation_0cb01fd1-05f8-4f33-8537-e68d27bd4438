import { useThemeClasses } from "@/design-system/utils/theme-utils";
import {
  Box,
  Divider,
  NumberInput,
  Select,
  Switch,
  Text,
  TextInput,
  Tooltip,
} from "@mantine/core";
import {
  FaBriefcase,
  FaDollarSign,
  FaInfoCircle,
  FaMapMarkerAlt,
  FaTags,
} from "react-icons/fa";

const jobCategories = [
  { value: "software", label: "Software Development" },
  { value: "web-development", label: "Web Development" },
  { value: "mobile-development", label: "Mobile Development" },
  { value: "data-science", label: "Data Science" },
  { value: "ai-ml", label: "Artificial Intelligence & Machine Learning" },
  { value: "devops", label: "DevOps" },
  { value: "cloud-computing", label: "Cloud Computing" },
  { value: "cybersecurity", label: "Cybersecurity" },
  { value: "it-support", label: "IT Support" },
  { value: "networking", label: "Networking" },
  { value: "ui-ux-design", label: "UI/UX Design" },
  { value: "graphic-design", label: "Graphic Design" },
  { value: "marketing", label: "Marketing" },
  { value: "digital-marketing", label: "Digital Marketing" },
  { value: "content-writing", label: "Content Writing" },
  { value: "social-media", label: "Social Media Management" },
  { value: "sales", label: "Sales" },
  { value: "customer-support", label: "Customer Support" },
  { value: "finance", label: "Finance" },
  { value: "accounting", label: "Accounting" },
  { value: "human-resources", label: "Human Resources" },
  { value: "project-management", label: "Project Management" },
  { value: "product-management", label: "Product Management" },
  { value: "business-development", label: "Business Development" },
  { value: "consulting", label: "Consulting" },
  { value: "other", label: "Other" },
];

interface BasicInformationStepProps {
  form: any;
}

export default function BasicInformationStep({
  form,
}: BasicInformationStepProps) {
  return (
    <div className="space-y-8">
      <div
        className={useThemeClasses(
          "border-b border-gray-200 pb-4",
          "border-b border-dark-4 pb-4",
        )}
      >
        <div className="flex flex-wrap items-center justify-between gap-2">
          <div className="flex items-center gap-2">
            <div
              className={useThemeClasses(
                "flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-blue-600",
                "flex h-8 w-8 items-center justify-center rounded-full bg-blue-900/30 text-blue-300",
              )}
            >
              <FaBriefcase size={16} />
            </div>
            <Text
              className={useThemeClasses(
                "text-xl font-semibold text-gray-800",
                "text-xl font-semibold text-gray-100",
              )}
            >
              Basic Job Information
            </Text>
          </div>
          <Text
            size="sm"
            className={useThemeClasses(
              "rounded-md bg-gray-100 px-2 py-1 text-gray-600",
              "rounded-md bg-dark-5 px-2 py-1 text-gray-300",
            )}
          >
            * Required fields
          </Text>
        </div>
        <Text size="sm" c="dimmed" mt="xs">
          Enter the core details about the position you&apos;re offering
        </Text>
      </div>

      <div className="grid grid-cols-1 gap-x-8 gap-y-6 md:grid-cols-2">
        <div>
          <TextInput
            label={
              <div className="flex items-center gap-1">
                <Text className="font-medium">Job Title</Text>
                <Text c="red" span>
                  *
                </Text>
                <Tooltip label="Be specific with the job title to attract the right candidates">
                  <FaInfoCircle
                    size={14}
                    className="cursor-help text-gray-400"
                  />
                </Tooltip>
              </div>
            }
            placeholder="e.g. Senior React Developer"
            leftSection={
              <FaBriefcase size={18} className="text-primary-color" />
            }
            {...form.getInputProps("jobTitle")}
            error={form.errors.jobTitle}
            aria-label="Job Title"
            size="md"
            className="mb-4"
            radius="md"
          />

          <Select
            label={
              <div className="flex items-center gap-1">
                <Text>Required Experience</Text>
                <Text c="red" span>
                  *
                </Text>
              </div>
            }
            placeholder="Select required experience"
            data={[
              { value: "internship", label: "Internship" },
              { value: "1-2 years", label: "1-2 years" },
              { value: "3-5 years", label: "3-5 years" },
              { value: "5-10 years", label: "5-10 years" },
              { value: "10+ years", label: "10+ years" },
            ]}
            leftSection={
              <FaBriefcase size={18} className="text-primary-color" />
            }
            {...form.getInputProps("requiredExperience")}
            error={form.errors.requiredExperience}
            aria-label="Required Experience"
            size="md"
            className="mb-4"
          />

          <Select
            label={
              <div className="flex items-center gap-1">
                <Text>Job Category</Text>
                <Text c="red" span>
                  *
                </Text>
              </div>
            }
            placeholder="Select job category"
            data={jobCategories}
            leftSection={<FaTags size={18} className="text-primary-color" />}
            {...form.getInputProps("jobCategory")}
            error={form.errors.jobCategory}
            aria-label="Job Category"
            size="md"
            searchable
          />
        </div>

        <div
          className={useThemeClasses(
            "rounded-lg bg-blue-50 p-6",
            "rounded-lg bg-dark-6 p-6",
          )}
        >
          <Text className="text-primary-color mb-3 text-lg font-medium">
            Salary Information
          </Text>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <NumberInput
              label="Minimum Salary"
              placeholder="Enter minimum"
              min={0}
              step={100}
              leftSection={
                <FaDollarSign size={18} className="text-primary-color" />
              }
              {...form.getInputProps("minSalary")}
              error={form.errors.minSalary}
              aria-label="Minimum Salary"
              size="md"
            />
            <NumberInput
              label="Maximum Salary"
              placeholder="Enter maximum"
              min={0}
              step={100}
              leftSection={
                <FaDollarSign size={18} className="text-primary-color" />
              }
              {...form.getInputProps("maxSalary")}
              error={form.errors.maxSalary}
              aria-label="Maximum Salary"
              size="md"
            />
          </div>

          <Select
            label="Currency"
            placeholder="Select currency"
            data={[
              { value: "EGP", label: "EGP (Egyptian Pound)" },
              { value: "SAR", label: "SAR (Saudi Riyal)" },
              { value: "USD", label: "USD (US Dollar)" },
              { value: "EUR", label: "EUR (Euro)" },
              { value: "GBP", label: "GBP (British Pound)" },
            ]}
            leftSection={
              <FaDollarSign size={18} className="text-primary-color" />
            }
            {...form.getInputProps("currency")}
            error={form.errors.currency}
            aria-label="Currency"
            size="md"
            className="mt-4"
          />

          <Switch
            label={
              <div className="flex items-center gap-1">
                <Text>Show Salary Range to Applicants</Text>
                <Tooltip label="Showing salary range can increase application rates by up to 30%">
                  <FaInfoCircle
                    size={14}
                    className={useThemeClasses(
                      "cursor-help text-gray-400",
                      "cursor-help text-gray-500",
                    )}
                  />
                </Tooltip>
              </div>
            }
            {...form.getInputProps("showSalary")}
            error={form.errors.showSalary}
            aria-label="Show Salary Range"
            className="mt-4"
            size="md"
          />
        </div>
      </div>

      <Divider my="lg" />

      <Box className="mt-6">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <Select
            label={
              <div className="flex items-center gap-1">
                <Text>Location</Text>
                <Text c="red" span>
                  *
                </Text>
              </div>
            }
            placeholder="Select location"
            data={[
              { value: "remote", label: "Remote" },
              { value: "onsite", label: "Onsite" },
              { value: "hybrid", label: "Hybrid" },
            ]}
            leftSection={
              <FaMapMarkerAlt size={18} className="text-primary-color" />
            }
            {...form.getInputProps("location")}
            error={form.errors.location}
            aria-label="Location"
            size="md"
          />
          <Select
            label={
              <div className="flex items-center gap-1">
                <Text>Job Type</Text>
                <Text c="red" span>
                  *
                </Text>
              </div>
            }
            placeholder="Select job type"
            data={[
              { value: "full-time", label: "Full Time" },
              { value: "part-time", label: "Part Time" },
              { value: "internship", label: "Internship" },
              { value: "contract", label: "Contract" },
              { value: "temporary", label: "Temporary" },
            ]}
            leftSection={
              <FaBriefcase size={18} className="text-primary-color" />
            }
            {...form.getInputProps("jobType")}
            error={form.errors.jobType}
            aria-label="Job Type"
            size="md"
          />
        </div>
      </Box>
    </div>
  );
}
