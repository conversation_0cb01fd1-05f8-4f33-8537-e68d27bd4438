import Card, { type CardProps } from "@/design-system/components/card/Card";
import { type PropsWithChildren } from "react";

/**
 * @deprecated Use Card from design-system/components instead
 */
export default function BaseCard({
  children,
  className = "",
  withBorder = true,
  withShadow = false,
  padding = "lg",
  radius = "md",
  fullHeight,
  fullWidth,
  variant,
  ...rest
}: PropsWithChildren<CardProps>) {
  return (
    <Card
      className={className}
      withBorder={withBorder}
      withShadow={withShadow}
      padding={padding}
      radius={radius}
      fullHeight={fullHeight}
      fullWidth={fullWidth}
      variant={variant}
      {...rest}
    >
      {children}
    </Card>
  );
}
