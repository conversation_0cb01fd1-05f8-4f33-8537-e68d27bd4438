import CandidateProfilePageContent from "@/components/employer/candidate-profile/CandidateProfilePageContent";
import { candidateData } from "@/data/candidate-data";
import { PageContainer } from "@/design-system/components";
import { useParams } from "react-router";

export default function EmployerAppliedCandidateDetailsPage() {
  const params = useParams();
  const candidateId = params.candidate_id;

  console.log(candidateId);

  // In a real app, you would fetch the candidate data based on the ID
  // For now, we'll use the mock data

  return (
    <PageContainer
      breadcrumbItems={[
        { title: "Home", href: "/" },
        { title: "Candidates", href: "/employer/candidates" },
        { title: candidateData.name },
      ]}
      variant="employer"
    >
      <CandidateProfilePageContent />
    </PageContainer>
  );
}
