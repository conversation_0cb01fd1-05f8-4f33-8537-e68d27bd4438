import { type Candidate } from "@/types";
import { <PERSON><PERSON>, Card, Center, Grid, <PERSON><PERSON>, <PERSON>ack, Text } from "@mantine/core";
import { FaUser } from "react-icons/fa";
import CandidateCard from "./CandidateCard";
import CandidateListItem from "./CandidateListItem";

// Sample jobs data
const jobs = [
  {
    id: 1,
    title: "Software Engineer",
    status: "Active",
  },
  {
    id: 2,
    title: "Product Manager",
    status: "Pending",
  },
  {
    id: 3,
    title: "UX Designer",
    status: "Closed",
  },
];

// Sample candidates data
const candidates: Candidate[] = [
  {
    id: 1,
    name: "<PERSON>",
    email: "<EMAIL>",
    status: "Applied",
    jobId: 1, // Linked to "Software Engineer"
    appliedDate: "2023-10-01",
    skills: ["JavaScript", "React", "Node.js"],
  },
  {
    id: 2,
    name: "<PERSON>",
    email: "<EMAIL>",
    status: "Shortlisted",
    jobId: 2, // Linked to "Product Manager"
    appliedDate: "2023-10-02",
    skills: ["Product Management", "Agile", "User Research"],
  },
  {
    id: 3,
    name: "<PERSON>",
    email: "<EMAIL>",
    status: "Interviewed",
    jobId: 1, // Linked to "Software Engineer"
    appliedDate: "2023-10-03",
    skills: ["JavaScript", "TypeScript", "React Native"],
  },
  {
    id: 4,
    name: "Bob Brown",
    email: "<EMAIL>",
    status: "Rejected",
    jobId: 3, // Linked to "UX Designer"
    appliedDate: "2023-10-04",
    skills: ["UI Design", "Figma", "User Testing"],
  },
  {
    id: 5,
    name: "Charlie Davis",
    email: "<EMAIL>",
    status: "Hired",
    jobId: 2, // Linked to "Product Manager"
    appliedDate: "2023-10-05",
    skills: ["Product Strategy", "Data Analysis", "Team Leadership"],
  },
];

interface CandidatesListProps {
  viewMode: "grid" | "list";
  isLoading?: boolean;
}

export default function CandidatesList({
  viewMode,
  isLoading = false,
}: CandidatesListProps) {
  // If loading, show a loader
  if (isLoading) {
    return (
      <Center py={50}>
        <Loader size="lg" />
      </Center>
    );
  }

  // If no candidates, show empty state
  if (candidates.length === 0) {
    return (
      <Card withBorder p="xl" radius="md" className="text-center">
        <Center className="flex-col gap-4 py-8">
          <div className="rounded-full bg-gray-100 p-6">
            <FaUser size={40} className="text-gray-400" />
          </div>
          <Text size="xl" fw={600}>
            No candidates found
          </Text>
          <Text c="dimmed" className="max-w-md">
            We couldn&apos;t find any candidates matching your search criteria.
            Try adjusting your filters or search for something else.
          </Text>
          <Button
            variant="outline"
            mt="md"
            onClick={() => {
              // Reset filters functionality would go here
            }}
          >
            Clear Filters
          </Button>
        </Center>
      </Card>
    );
  }

  // Grid view
  if (viewMode === "grid") {
    return (
      <Grid gutter="lg">
        {candidates.map((candidate) => {
          const job = jobs.find((job) => job.id === candidate.jobId);
          return (
            <Grid.Col key={candidate.id} span={{ base: 12, sm: 6, lg: 4 }}>
              <CandidateCard job={job} candidate={candidate} />
            </Grid.Col>
          );
        })}
      </Grid>
    );
  }

  // List view
  return (
    <Stack gap="md">
      {candidates.map((candidate) => {
        const job = jobs.find((job) => job.id === candidate.jobId);
        return (
          <CandidateListItem
            key={candidate.id}
            job={job}
            candidate={candidate}
          />
        );
      })}
    </Stack>
  );
}
