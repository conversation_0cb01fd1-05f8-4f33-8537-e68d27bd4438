import { SectionHeading } from "@/design-system/components";
import useProfileForm from "@/hooks/employer/use-profile-form";
import {
  Button,
  Card,
  FileInput,
  Select,
  TextInput,
  Textarea,
} from "@mantine/core";
import {
  FaBriefcase,
  FaBuilding,
  FaEnvelope,
  FaFacebook,
  FaGlobe,
  FaInstagram,
  FaLinkedin,
  FaMapMarkerAlt,
  FaPhone,
  FaTwitter,
  FaUpload,
  FaUser,
} from "react-icons/fa";

export default function ProfileForm() {
  const { form, handleSubmit } = useProfileForm();

  return (
    <form
      noValidate
      className="flex flex-col gap-8"
      onSubmit={form.onSubmit(handleSubmit)}
    >
      {/* Company Information Section */}
      <Card variant="employer" withBorder shadow="sm" padding="lg" radius="md">
        <SectionHeading variant="employer" icon={FaBuilding}>
          Company Information
        </SectionHeading>
        <div className="flex flex-col gap-4">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <TextInput
              label="Company Name"
              placeholder="Enter your company name"
              required
              leftSection={
                <FaBuilding size={18} className="text-primary-color" />
              }
              {...form.getInputProps("companyName")}
              error={form.errors.companyName}
            />
            <TextInput
              label="Contact Person"
              placeholder="Enter contact person's name"
              required
              leftSection={<FaUser size={18} className="text-primary-color" />}
              {...form.getInputProps("contactPerson")}
              error={form.errors.contactPerson}
            />
            <TextInput
              label="Email"
              placeholder="Enter company email"
              required
              leftSection={
                <FaEnvelope size={18} className="text-primary-color" />
              }
              {...form.getInputProps("email")}
              error={form.errors.email}
            />
            <TextInput
              label="Phone"
              placeholder="Enter company phone number"
              required
              leftSection={<FaPhone size={18} className="text-primary-color" />}
              {...form.getInputProps("phone")}
              error={form.errors.phone}
            />
          </div>
          <TextInput
            label="Website"
            placeholder="Enter company website URL"
            leftSection={<FaGlobe size={18} className="text-primary-color" />}
            {...form.getInputProps("website")}
            error={form.errors.website}
          />
          <Textarea
            label="Company Description"
            placeholder="Describe your company"
            required
            rows={6}
            {...form.getInputProps("companyDescription")}
            error={form.errors.companyDescription}
          />
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <TextInput
              label="Industry"
              placeholder="Enter your industry"
              leftSection={
                <FaBriefcase size={18} className="text-primary-color" />
              }
              {...form.getInputProps("industry")}
              error={form.errors.industry}
            />
            <TextInput
              label="Location"
              placeholder="Enter company location"
              leftSection={
                <FaMapMarkerAlt size={18} className="text-primary-color" />
              }
              {...form.getInputProps("location")}
              error={form.errors.location}
            />
          </div>
          <FileInput
            label="Company Logo"
            placeholder="Upload your company logo"
            accept="image/png,image/jpeg"
            leftSection={<FaUpload size={18} className="text-primary-color" />}
            {...form.getInputProps("companyLogo")}
            error={form.errors.companyLogo}
          />
        </div>
      </Card>

      {/* Social Media Links Section */}
      <Card variant="employer" withBorder shadow="sm" padding="lg" radius="md">
        <SectionHeading variant="employer" icon={FaGlobe}>
          Social Media Links
        </SectionHeading>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <TextInput
            label="LinkedIn"
            placeholder="Enter LinkedIn profile URL"
            leftSection={
              <FaLinkedin size={18} className="text-primary-color" />
            }
            {...form.getInputProps("linkedin")}
            error={form.errors.linkedin}
          />
          <TextInput
            label="Twitter"
            placeholder="Enter Twitter profile URL"
            leftSection={<FaTwitter size={18} className="text-primary-color" />}
            {...form.getInputProps("twitter")}
            error={form.errors.twitter}
          />
          <TextInput
            label="Facebook"
            placeholder="Enter Facebook profile URL"
            leftSection={
              <FaFacebook size={18} className="text-primary-color" />
            }
            {...form.getInputProps("facebook")}
            error={form.errors.facebook}
          />
          <TextInput
            label="Instagram"
            placeholder="Enter Instagram profile URL"
            leftSection={
              <FaInstagram size={18} className="text-primary-color" />
            }
            {...form.getInputProps("instagram")}
            error={form.errors.instagram}
          />
        </div>
      </Card>

      {/* Company Size and Type Section */}
      <Card variant="employer" withBorder shadow="sm" padding="lg" radius="md">
        <SectionHeading variant="employer" icon={FaBriefcase}>
          Company Details
        </SectionHeading>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <Select
            label="Company Size"
            placeholder="Select company size"
            data={[
              { value: "1-10", label: "1-10 employees" },
              { value: "11-50", label: "11-50 employees" },
              { value: "51-200", label: "51-200 employees" },
              { value: "201-500", label: "201-500 employees" },
              { value: "501+", label: "501+ employees" },
            ]}
            leftSection={
              <FaBriefcase size={18} className="text-primary-color" />
            }
            {...form.getInputProps("companySize")}
            error={form.errors.companySize}
          />
          <Select
            label="Company Type"
            placeholder="Select company type"
            data={[
              { value: "startup", label: "Startup" },
              { value: "sme", label: "Small & Medium Enterprise (SME)" },
              { value: "corporate", label: "Corporate" },
              { value: "non-profit", label: "Non-Profit" },
              { value: "government", label: "Government" },
            ]}
            leftSection={
              <FaBriefcase size={18} className="text-primary-color" />
            }
            {...form.getInputProps("companyType")}
            error={form.errors.companyType}
          />
        </div>
      </Card>

      {/* Submit Button */}
      <div className="text-right">
        <Button type="submit">Update Profile</Button>
      </div>
    </form>
  );
}
