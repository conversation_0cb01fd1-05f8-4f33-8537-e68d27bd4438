import { getApplicationsCountByJobId } from "@/data/job-applications";
import { useConfirmDeleteJobModal } from "@/stores/modal-store";
import { type Job } from "@/types";
import {
  ActionIcon,
  Avatar,
  Badge,
  Button,
  Card,
  Flex,
  Grid,
  Group,
  Menu,
  Text,
  Tooltip,
} from "@mantine/core";
import {
  FaBuilding,
  FaCalendarAlt,
  FaEdit,
  FaEllipsisV,
  FaMapMarkerAlt,
  FaTrash,
  FaUsers,
} from "react-icons/fa";
import { useNavigate } from "react-router";

type JobCardProps = {
  job: Job;
  viewMode: "grid" | "list";
};

export default function JobCard({ job, viewMode }: JobCardProps) {
  const navigate = useNavigate();
  const confirmDeleteJobModal = useConfirmDeleteJobModal();

  // Get the applications count for this job from our mock data
  const applicationsCount = getApplicationsCountByJobId(job.id);

  const getStatusColor = (status: "Active" | "Pending" | "Closed") => {
    switch (status) {
      case "Active":
        return "blue";
      case "Pending":
        return "yellow";
      case "Closed":
        return "red";
      default:
        return "gray";
    }
  };

  // Function to get company initials for the logo
  const getCompanyInitials = (companyName: string) => {
    return companyName
      .split(" ")
      .map((word) => word[0])
      .join("")
      .toUpperCase()
      .substring(0, 2);
  };

  // Format date posted
  const formatDate = (date: string | undefined) => {
    if (!date) return "Recently";
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Handle edit job
  const handleEditJob = () => {
    navigate(`/employer/manage-jobs/${job.id}/edit`);
  };

  // Handle view applications
  const handleViewApplications = () => {
    navigate(`/employer/manage-jobs/${job.id}/applications`);
  };

  // Handle delete job
  const handleDeleteJob = () => {
    confirmDeleteJobModal.open();
  };

  // Grid view card
  if (viewMode === "grid") {
    return (
      <Grid.Col span={{ base: 12, sm: 6, md: 4 }}>
        <Card withBorder radius="md" className="h-full shadow-sm" p="lg">
          <Flex direction="column" gap="md" className="h-full">
            {/* Header with title and status */}
            <Group justify="space-between" align="flex-start">
              <div>
                <Text size="xl" fw={600} className="line-clamp-1">
                  {job.title}
                </Text>
                <Group gap="xs" mt={4}>
                  <FaBuilding size={14} className="text-gray-500" />
                  <Text size="sm" c="dimmed" className="line-clamp-1">
                    {job.company}
                  </Text>
                </Group>
              </div>
              <Badge
                color={getStatusColor(job.status || "Active")}
                size="lg"
                variant="light"
              >
                {job.status}
              </Badge>
            </Group>

            {/* Job details */}
            <div className="flex-grow">
              <Group gap="xs" mt={4}>
                <FaMapMarkerAlt size={14} className="text-gray-500" />
                <Text size="sm" c="dimmed">
                  {job.location}
                </Text>
              </Group>
              <Group gap="xs" mt={4}>
                <FaCalendarAlt size={14} className="text-gray-500" />
                <Text size="sm" c="dimmed">
                  Posted: {formatDate(job.datePosted)}
                </Text>
              </Group>
              <Group gap="xs" mt={4}>
                <FaUsers size={14} className="text-blue-500" />
                <Text size="sm" fw={500} c="blue">
                  {applicationsCount} Application
                  {applicationsCount !== 1 ? "s" : ""}
                </Text>
              </Group>
            </div>

            {/* Action buttons */}
            <Group mt="auto" grow>
              <Button
                variant="light"
                leftSection={<FaEdit size={14} />}
                onClick={handleEditJob}
              >
                Edit
              </Button>
              <Button
                variant="light"
                color="red"
                leftSection={<FaTrash size={14} />}
                onClick={handleDeleteJob}
              >
                Delete
              </Button>
            </Group>
            <Button
              variant="filled"
              color="blue"
              leftSection={<FaUsers size={14} />}
              onClick={handleViewApplications}
              fullWidth
            >
              View Applications
            </Button>
          </Flex>
        </Card>
      </Grid.Col>
    );
  }

  // List view card
  return (
    <Grid.Col span={12}>
      <Card withBorder radius="md" className="shadow-sm" p="md">
        <Group justify="space-between" wrap="nowrap">
          <Group wrap="nowrap" align="flex-start">
            <Avatar
              radius="md"
              size="lg"
              color="blue"
              className="hidden sm:flex"
            >
              {getCompanyInitials(job.company)}
            </Avatar>
            <div>
              <Text size="lg" fw={600} className="line-clamp-1">
                {job.title}
              </Text>
              <Group gap="xs" mt={4}>
                <FaBuilding size={14} className="text-gray-500" />
                <Text size="sm" c="dimmed">
                  {job.company}
                </Text>
                <Text size="sm" c="dimmed" className="hidden md:block">
                  •
                </Text>
                <Group gap="xs" className="hidden md:flex">
                  <FaMapMarkerAlt size={14} className="text-gray-500" />
                  <Text size="sm" c="dimmed">
                    {job.location}
                  </Text>
                </Group>
              </Group>
              <Group gap="xs" mt={4} className="md:hidden">
                <FaMapMarkerAlt size={14} className="text-gray-500" />
                <Text size="sm" c="dimmed">
                  {job.location}
                </Text>
              </Group>
              <Group gap="xs" mt={4}>
                <FaCalendarAlt size={14} className="text-gray-500" />
                <Text size="sm" c="dimmed">
                  Posted: {formatDate(job.datePosted)}
                </Text>
                <Text size="sm" c="dimmed">
                  •
                </Text>
                <FaUsers size={14} className="text-blue-500" />
                <Text size="sm" fw={500} c="blue">
                  {applicationsCount} Application
                  {applicationsCount !== 1 ? "s" : ""}
                </Text>
              </Group>
            </div>
          </Group>

          <Group wrap="nowrap">
            <Badge
              color={getStatusColor(job.status || "Active")}
              size="lg"
              variant="light"
              className="hidden sm:flex"
            >
              {job.status}
            </Badge>
            <Group gap="xs" className="hidden md:flex">
              <Tooltip label="Edit Job">
                <ActionIcon variant="light" onClick={handleEditJob} size="lg">
                  <FaEdit size={16} />
                </ActionIcon>
              </Tooltip>
              <Tooltip label="Delete Job">
                <ActionIcon
                  variant="light"
                  color="red"
                  onClick={handleDeleteJob}
                  size="lg"
                >
                  <FaTrash size={16} />
                </ActionIcon>
              </Tooltip>
              <Button
                variant="light"
                color="blue"
                leftSection={<FaUsers size={14} />}
                onClick={handleViewApplications}
              >
                View Applications
              </Button>
            </Group>
            <Menu position="bottom-end" withinPortal>
              <Menu.Target>
                <div className="cursor-pointer">
                  <ActionIcon className="md:hidden">
                    <FaEllipsisV />
                  </ActionIcon>
                </div>
              </Menu.Target>
              <Menu.Dropdown>
                <Menu.Item
                  leftSection={<FaEdit size={14} />}
                  onClick={handleEditJob}
                >
                  Edit Job
                </Menu.Item>
                <Menu.Item
                  leftSection={<FaUsers size={14} />}
                  onClick={handleViewApplications}
                >
                  View Applications
                </Menu.Item>
                <Menu.Divider />
                <Menu.Item
                  leftSection={<FaTrash size={14} />}
                  c="red"
                  onClick={handleDeleteJob}
                >
                  Delete Job
                </Menu.Item>
              </Menu.Dropdown>
            </Menu>
          </Group>
        </Group>
      </Card>
    </Grid.Col>
  );
}
