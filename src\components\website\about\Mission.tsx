import {
  Badge,
  Card,
  Text,
  ThemeIcon,
  Title,
  useMantineColorScheme,
} from "@mantine/core";
import { FaHandshake, FaLightbulb, FaUsers } from "react-icons/fa";

// Using a new image URL for mission
const missionImageUrl =
  "https://images.unsplash.com/photo-1552664730-d307ca884978?q=80&w=2940&auto=format&fit=crop";

const missionPoints = [
  {
    icon: FaUsers,
    title: "Connect Talent",
    description: "Bring together qualified candidates and great employers",
    color: "blue",
    stats: "10,000+ connections made",
  },
  {
    icon: FaHandshake,
    title: "Simplify Hiring",
    description: "Make the recruitment process efficient and effective",
    color: "green",
    stats: "50% faster hiring process",
  },
  {
    icon: FaLightbulb,
    title: "Empower Careers",
    description:
      "Help professionals find opportunities that match their skills",
    color: "violet",
    stats: "85% job satisfaction rate",
  },
];

export default function Mission() {
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  return (
    <section
      className={`relative overflow-hidden py-20 ${isDark ? "bg-gray-900" : ""}`}
    >
      {/* Background decoration - enhanced with more elements */}
      <div
        className={`absolute top-0 right-0 -z-10 h-96 w-96 rounded-full ${isDark ? "bg-blue-900/20" : "bg-blue-50"} opacity-70 blur-3xl`}
      ></div>
      <div
        className={`absolute bottom-0 left-0 -z-10 h-96 w-96 rounded-full ${isDark ? "bg-green-900/20" : "bg-green-50"} opacity-70 blur-3xl`}
      ></div>
      <div
        className={`absolute top-1/3 left-1/4 -z-10 h-64 w-64 rounded-full ${isDark ? "bg-purple-900/20" : "bg-purple-50"} opacity-50 blur-3xl`}
      ></div>

      <div className="container">
        <div className="mb-16 text-center">
          <Badge color="primary_color" size="lg" radius="sm" className="mb-4">
            Our Purpose
          </Badge>
          <Title
            order={2}
            className="mb-6 text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary-color to-blue-700"
          >
            Our Mission
          </Title>
          <div className="bg-primary-color mx-auto h-1 w-24 rounded-full"></div>
        </div>

        <div className="grid grid-cols-1 items-center gap-16 lg:grid-cols-2">
          <div className="relative order-2 lg:order-1">
            {/* Enhanced image styling with animated elements */}
            <div className="bg-primary-color/20 absolute -top-6 -left-6 -z-10 h-32 w-32 rounded-lg transform rotate-6 animate-pulse"></div>
            <div
              className="bg-primary-color/20 absolute -right-6 -bottom-6 -z-10 h-32 w-32 rounded-lg transform -rotate-6 animate-pulse"
              style={{ animationDelay: "1s" }}
            ></div>

            <div className="relative overflow-hidden rounded-xl shadow-2xl">
              <div className="absolute inset-0 bg-gradient-to-tr from-primary-color/30 to-transparent opacity-60"></div>
              <img
                src={missionImageUrl}
                alt="Our Mission - Team Collaboration"
                className="h-auto w-full object-cover transition-transform duration-700 hover:scale-105"
                width={600}
                height={400}
                style={{ maxHeight: "450px" }}
              />
            </div>
          </div>

          <div className="space-y-8 order-1 lg:order-2">
            <div className="relative">
              <div
                className="absolute -left-4 -top-4 h-16 w-16 rounded-full bg-primary-color/10 animate-ping"
                style={{ animationDuration: "3s" }}
              ></div>
              <Text
                className={`mb-8 text-xl leading-relaxed relative ${isDark ? "text-gray-300" : "text-gray-700"}`}
              >
                Our mission is to create a{" "}
                <span className="font-semibold text-primary-color">
                  seamless and efficient platform
                </span>{" "}
                for job postings and candidate applications, making the hiring
                process easier for both employers and job seekers. We aim to
                bridge the gap between employers and potential employees by
                providing a user-friendly and effective solution for job
                searching and hiring.
              </Text>
            </div>

            <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
              {missionPoints.map((point, index) => (
                <Card
                  key={index}
                  padding="lg"
                  radius="lg"
                  className={`h-full text-center transition-all duration-300 hover:shadow-lg hover:-translate-y-1 ${
                    isDark ? "bg-gray-800 border-gray-700" : "bg-white border-0"
                  }`}
                  shadow="sm"
                  withBorder
                >
                  <div className="relative mb-6">
                    <div
                      className={
                        point.color === "blue"
                          ? `absolute inset-0 rounded-full scale-[1.8] opacity-20 ${isDark ? "bg-blue-900" : "bg-blue-100"}`
                          : point.color === "green"
                            ? `absolute inset-0 rounded-full scale-[1.8] opacity-20 ${isDark ? "bg-green-900" : "bg-green-100"}`
                            : `absolute inset-0 rounded-full scale-[1.8] opacity-20 ${isDark ? "bg-violet-900" : "bg-violet-100"}`
                      }
                    ></div>
                    <ThemeIcon
                      size={60}
                      radius="xl"
                      color={point.color}
                      className="mx-auto relative z-10"
                      variant="light"
                    >
                      <point.icon size={28} />
                    </ThemeIcon>
                  </div>

                  <Title
                    order={3}
                    className={`mb-2 text-lg font-bold ${isDark ? "text-white" : ""}`}
                  >
                    {point.title}
                  </Title>
                  <Text
                    className={`mb-4 text-sm ${isDark ? "text-gray-300" : "text-gray-600"}`}
                  >
                    {point.description}
                  </Text>

                  <div
                    className={`mt-auto pt-2 border-t ${isDark ? "border-gray-700" : "border-gray-100"}`}
                  >
                    <Text size="sm" className="font-medium text-primary-color">
                      {point.stats}
                    </Text>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
