import {
  Button,
  Card,
  Group,
  Select,
  Textarea,
  TextInput,
  useMantineColorScheme,
} from "@mantine/core";
import {
  FaEnvelope,
  FaFacebook,
  FaInstagram,
  FaLinkedin,
  FaMapMarkerAlt,
  FaPhone,
  Fa<PERSON>witter,
  FaUser,
} from "react-icons/fa";
import { Link } from "react-router";

export default function ContactUsForm() {
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  return (
    <div className="grid gap-8 md:grid-cols-3">
      {/* Contact Information */}
      <div className="md:col-span-1">
        <div
          className={`rounded-lg p-8 text-white shadow-lg ${
            isDark
              ? "bg-gradient-to-br from-primary-700 to-accent-700"
              : "bg-gradient-to-br from-primary-500 to-accent-500"
          }`}
        >
          <h3 className="mb-6 text-2xl font-bold">Contact Information</h3>
          <p className="mb-8 text-white/80">
            Fill out the form and our team will get back to you within 24 hours.
          </p>

          <div className="space-y-6">
            <div className="flex items-start gap-4">
              <div className="flex h-10 w-10 shrink-0 items-center justify-center rounded-full bg-white/20">
                <FaPhone className="text-white" size={18} />
              </div>
              <div>
                <p className="text-sm font-medium text-white/60">Phone</p>
                <p className="text-lg">+1 (*************</p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="flex h-10 w-10 shrink-0 items-center justify-center rounded-full bg-white/20">
                <FaEnvelope className="text-white" size={18} />
              </div>
              <div>
                <p className="text-sm font-medium text-white/60">Email</p>
                <p className="text-lg"><EMAIL></p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="flex h-10 w-10 shrink-0 items-center justify-center rounded-full bg-white/20">
                <FaMapMarkerAlt className="text-white" size={18} />
              </div>
              <div>
                <p className="text-sm font-medium text-white/60">Address</p>
                <p className="text-lg">123 Business Ave, Suite 100</p>
                <p className="text-lg">San Francisco, CA 94107</p>
              </div>
            </div>
          </div>

          <div className="mt-12 h-px w-full bg-white/20"></div>

          <div className="mt-12">
            <p className="mb-4 text-sm font-medium text-white/60">Follow Us</p>
            <div className="flex gap-4">
              <Link
                to="#"
                className="flex h-10 w-10 items-center justify-center rounded-full bg-white/20 transition-colors hover:bg-white/30"
                aria-label="Facebook"
              >
                <FaFacebook className="text-white" size={18} />
              </Link>
              <Link
                to="#"
                className="flex h-10 w-10 items-center justify-center rounded-full bg-white/20 transition-colors hover:bg-white/30"
                aria-label="Twitter"
              >
                <FaTwitter className="text-white" size={18} />
              </Link>
              <Link
                to="#"
                className="flex h-10 w-10 items-center justify-center rounded-full bg-white/20 transition-colors hover:bg-white/30"
                aria-label="LinkedIn"
              >
                <FaLinkedin className="text-white" size={18} />
              </Link>
              <Link
                to="#"
                className="flex h-10 w-10 items-center justify-center rounded-full bg-white/20 transition-colors hover:bg-white/30"
                aria-label="Instagram"
              >
                <FaInstagram className="text-white" size={18} />
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Contact Form */}
      <Card
        withBorder
        className={`md:col-span-2 ${isDark ? "bg-gray-800 border-gray-700" : ""}`}
        padding="xl"
        radius="md"
      >
        <form>
          <h3
            className={`mb-6 text-2xl font-bold ${isDark ? "text-white" : ""}`}
          >
            Send us a message
          </h3>

          <div className="mb-6 grid gap-6 md:grid-cols-2">
            <TextInput
              label="Full Name"
              placeholder="John Doe"
              required
              leftSection={
                <FaUser size={16} className={isDark ? "text-gray-400" : ""} />
              }
              classNames={{
                label: isDark ? "text-gray-300" : "",
                input: isDark
                  ? "border-gray-700 bg-gray-800 text-gray-200"
                  : "",
              }}
            />
            <TextInput
              label="Email"
              placeholder="<EMAIL>"
              type="email"
              required
              leftSection={
                <FaEnvelope
                  size={16}
                  className={isDark ? "text-gray-400" : ""}
                />
              }
              classNames={{
                label: isDark ? "text-gray-300" : "",
                input: isDark
                  ? "border-gray-700 bg-gray-800 text-gray-200"
                  : "",
              }}
            />
          </div>

          <div className="mb-6 grid gap-6 md:grid-cols-2">
            <TextInput
              label="Phone Number"
              placeholder="(*************"
              leftSection={
                <FaPhone size={16} className={isDark ? "text-gray-400" : ""} />
              }
              classNames={{
                label: isDark ? "text-gray-300" : "",
                input: isDark
                  ? "border-gray-700 bg-gray-800 text-gray-200"
                  : "",
              }}
            />
            <Select
              label="Inquiry Type"
              placeholder="Select an option"
              required
              data={[
                { value: "general", label: "General Inquiry" },
                { value: "support", label: "Technical Support" },
                { value: "billing", label: "Billing Question" },
                { value: "partnership", label: "Partnership Opportunity" },
              ]}
              classNames={{
                label: isDark ? "text-gray-300" : "",
                input: isDark
                  ? "border-gray-700 bg-gray-800 text-gray-200"
                  : "",
                dropdown: isDark ? "bg-gray-800 border-gray-700" : "",
                option: isDark ? "text-gray-200 hover:bg-gray-700" : "",
              }}
            />
          </div>

          <TextInput
            label="Subject"
            placeholder="How can we help you?"
            required
            className="mb-6"
            classNames={{
              label: isDark ? "text-gray-300" : "",
              input: isDark ? "border-gray-700 bg-gray-800 text-gray-200" : "",
            }}
          />

          <Textarea
            label="Message"
            placeholder="Please provide as much detail as possible..."
            required
            minRows={5}
            className="mb-6"
            classNames={{
              label: isDark ? "text-gray-300" : "",
              input: isDark ? "border-gray-700 bg-gray-800 text-gray-200" : "",
            }}
          />

          <Group justify="flex-end">
            <Button
              type="submit"
              size="md"
              color="primary"
              className={isDark ? "bg-primary-600 hover:bg-primary-700" : ""}
            >
              Send Message
            </Button>
          </Group>
        </form>
      </Card>
    </div>
  );
}
