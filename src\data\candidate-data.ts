export const candidateData = {
  name: "<PERSON>",
  email: "<EMAIL>",
  phone: "************",
  jobTitle: "Software Engineer",
  appliedDate: "2023-10-01",
  resume: "https://example.com/resume.pdf",
  coverLetter: "https://example.com/cover-letter.pdf",
  linkedin: "https://linkedin.com/in/johndoe",
  github: "https://github.com/johndoe",
  portfolio: "https://johndoeportfolio.com",
  status: "Applied",
  skills: ["React", "Node.js", "TypeScript", "JavaScript", "HTML", "CSS"],
  experience: [
    {
      company: "Tech Corp",
      position: "Frontend Developer",
      duration: "Jan 2020 - Present",
      description:
        "Developed and maintained web applications using React and Node.js. Collaborated with cross-functional teams to deliver high-quality software solutions.",
    },
    {
      company: "Web Solutions",
      position: "Junior Developer",
      duration: "Jun 2018 - Dec 2019",
      description:
        "Assisted in the development of web applications and provided support for existing projects. Gained experience in debugging and optimizing code.",
    },
  ],
  education: [
    {
      institution: "University of Technology",
      degree: "Bachelor of Science in Computer Science",
      duration: "2014 - 2018",
      description:
        "Graduated with honors. Relevant coursework: Data Structures, Algorithms, Web Development, and Database Systems.",
    },
    {
      institution: "Online Course Platform",
      degree: "Advanced React Certification",
      duration: "2021",
      description:
        "Completed an advanced React course covering hooks, context API, and performance optimization.",
    },
  ],
  certifications: [
    {
      name: "Certified React Developer",
      issuer: "React Certification Institute",
      date: "2021",
    },
    {
      name: "AWS Certified Developer",
      issuer: "Amazon Web Services",
      date: "2022",
    },
  ],
  references: [
    {
      name: "Jane Smith",
      position: "Senior Developer",
      company: "Tech Corp",
      email: "<EMAIL>",
      phone: "************",
    },
    {
      name: "Michael Brown",
      position: "Team Lead",
      company: "Web Solutions",
      email: "<EMAIL>",
      phone: "************",
    },
  ],
};
