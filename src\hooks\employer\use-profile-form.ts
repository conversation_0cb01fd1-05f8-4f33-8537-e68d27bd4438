import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import { useEffect } from "react";
import { z } from "zod";
import { profileSchema } from "../../schemas/employer/profile-schema";

// Type for form values
type FormValues = z.infer<typeof profileSchema>;

export default function useProfileForm() {
  const form = useForm<FormValues>({
    initialValues: {
      companyName: "",
      contactPerson: "",
      email: "",
      phone: "",
      website: "",
      companyDescription: "",
      industry: "",
      location: "",
      companyLogo: undefined,
      linkedin: "",
      twitter: "",
      facebook: "",
      instagram: "",
      companySize: "",
      companyType: "",
    },
    validate: zodResolver(profileSchema),
  });

  useEffect(() => {
    form.setValues({
      companyName: "Tech Corp",
      contactPerson: "<PERSON>",
      email: "<EMAIL>",
      phone: "1234567890",
      website: "https://techcorp.com",
      companyDescription:
        "A leading tech company specializing in software solutions.",
      industry: "Technology",
      location: "San Francisco, CA",
      companyLogo: undefined,
      linkedin: "https://linkedin.com/company/techcorp",
      twitter: "https://twitter.com/techcorp",
      facebook: "https://facebook.com/techcorp",
      instagram: "https://instagram.com/techcorp",
      companySize: "201-500",
      companyType: "corporate",
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSubmit = (values: FormValues) => {
    console.log(values);
  };

  return {
    form,
    handleSubmit,
  };
}
