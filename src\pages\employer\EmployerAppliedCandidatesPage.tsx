import CandidatesList from "@/components/employer/candidates/CandidatesList";
import CandidatesSearchFilter from "@/components/employer/candidates/CandidatesSearchFilter";
import { PageContainer, PageHeading } from "@/design-system/components";
import {
  Box,
  Button,
  Card,
  Divider,
  Group,
  Pagination,
  SegmentedControl,
  Text,
  useMantineColorScheme,
} from "@mantine/core";
import { useMediaQuery } from "@mantine/hooks";
import { useState } from "react";
import { FaThLarge, FaThList } from "react-icons/fa";

export default function EmployerAppliedCandidatesPage() {
  // State for view mode (grid or list)
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

  // State for active filters
  const [activeFilters, setActiveFilters] = useState<string[]>([]);

  // Media query for responsive design
  const isMobile = useMediaQuery("(max-width: 768px)");

  // Theme helpers
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  // Function to clear all filters
  const clearAllFilters = () => {
    setActiveFilters([]);
    // Additional logic to reset filter components
  };

  return (
    <PageContainer
      breadcrumbItems={[{ title: "Home", href: "/" }, { title: "Candidates" }]}
      variant="employer"
      className="pb-12"
    >
      {/* Hero Section */}
      <Box
        className="mb-8 rounded-lg px-4 py-8 md:px-6 md:py-10"
        mx={{ base: -4, sm: -6 }}
        style={{
          background: isDark
            ? "linear-gradient(to right, #1a1b2e, #141b2d, #1a1b2e)"
            : "linear-gradient(to right, #f0f4ff, #e6f0ff, #f0f4ff)",
        }}
      >
        <PageHeading
          title="Candidates"
          subtitle="View and manage applicants for your job postings"
          className="mb-0 text-center"
          variant="employer"
        />
      </Box>

      {/* Filters and Search Bar */}
      <Card withBorder radius="md" className="mb-6 shadow-sm" p="md">
        <CandidatesSearchFilter />

        {activeFilters.length > 0 && (
          <>
            <Divider my="sm" />
            <Group justify="space-between" align="center">
              <Group gap="xs">
                {activeFilters.map((filter) => (
                  <Button
                    key={filter}
                    variant="light"
                    size="xs"
                    radius="xl"
                    rightSection="×"
                    onClick={() => {
                      setActiveFilters(
                        activeFilters.filter((f) => f !== filter),
                      );
                    }}
                  >
                    {filter}
                  </Button>
                ))}
              </Group>
              <Button variant="subtle" size="xs" onClick={clearAllFilters}>
                Clear All
              </Button>
            </Group>
          </>
        )}
      </Card>

      {/* Results Header */}
      <Card withBorder radius="md" className="mb-6 shadow-sm" p="md">
        <Group justify="space-between" align="center">
          <Text size="sm" c="dimmed">
            Showing 1-5 of 5 candidates
          </Text>
          <SegmentedControl
            value={viewMode}
            onChange={(value) => setViewMode(value as "grid" | "list")}
            data={[
              {
                value: "grid",
                label: (
                  <div className="flex items-center gap-2">
                    <FaThLarge size={16} />
                    <Text size="sm" className="hidden sm:inline">
                      Grid
                    </Text>
                  </div>
                ),
              },
              {
                value: "list",
                label: (
                  <div className="flex items-center gap-2">
                    <FaThList size={16} />
                    <Text size="sm" className="hidden sm:inline">
                      List
                    </Text>
                  </div>
                ),
              },
            ]}
          />
        </Group>
      </Card>

      {/* Candidates Grid/List */}
      <CandidatesList viewMode={viewMode} />

      {/* Pagination */}
      <Group justify="center" mt="xl">
        <Pagination total={5} size={isMobile ? "sm" : "md"} radius="md" />
      </Group>
    </PageContainer>
  );
}
