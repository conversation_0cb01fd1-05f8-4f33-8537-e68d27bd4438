import { <PERSON><PERSON>, Card, ThemeIcon, useMantineColorScheme } from "@mantine/core";
import {
  FaArrowRight,
  FaBriefcase,
  FaChartLine,
  FaCode,
  FaUsers,
} from "react-icons/fa";
import { Link } from "react-router";

const features = [
  {
    id: 1,
    icon: FaCode,
    color: "blue",
    title: "Easy Job Posting",
    description:
      "Post job openings quickly and easily with our user-friendly interface. Reach thousands of qualified candidates in minutes.",
    link: "/employer/create-job",
  },
  {
    id: 2,
    icon: FaUsers,
    color: "green",
    title: "Find Top Talent",
    description:
      "Access a large pool of qualified candidates to find the perfect fit for your company. Filter by skills, experience, and more.",
    link: "/employer/candidates",
  },
  {
    id: 3,
    icon: FaBriefcase,
    color: "purple",
    title: "Career Opportunities",
    description:
      "Explore a wide range of job opportunities and find your dream job. Apply with just a few clicks and track your applications.",
    link: "/candidate/jobs",
  },
  {
    id: 4,
    icon: FaChartLine,
    color: "orange",
    title: "Analytics & Insights",
    description:
      "Get detailed analytics and insights to make informed hiring decisions. Track application metrics and optimize your job listings.",
    link: "/employer/dashboard",
  },
];

export default function Features() {
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";
  return (
    <section className="relative py-24">
      {/* Background decoration */}
      <div
        className={`absolute inset-0 ${
          isDark
            ? "bg-gradient-to-b from-gray-900 via-gray-850 to-gray-900"
            : "bg-gradient-to-b from-white via-gray-50 to-white"
        }`}
      ></div>

      <div className="relative container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-3xl text-center">
          <h2 className="text-primary-color mb-4 text-sm font-semibold tracking-wider uppercase">
            Why Choose JobNest
          </h2>
          <h3
            className={`mb-6 text-4xl font-bold tracking-tight sm:text-5xl ${
              isDark ? "text-white" : "text-gray-900"
            }`}
          >
            Powerful features for employers and job seekers
          </h3>
          <p
            className={`mb-12 text-lg ${
              isDark ? "text-gray-300" : "text-gray-600"
            }`}
          >
            Our platform offers a comprehensive set of tools to streamline the
            hiring process and help candidates find their perfect career match.
          </p>
        </div>

        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
          {features.map(
            ({ id, description, icon: Icon, title, color, link }) => (
              <div
                key={id}
                className="transform transition-transform duration-300 hover:-translate-y-2"
              >
                <Card
                  padding="xl"
                  radius="md"
                  className={`group h-full border-t-4 transition-all duration-300 hover:shadow-lg ${
                    isDark ? "bg-gray-800 shadow-gray-900/50" : "bg-white"
                  }`}
                  style={{ borderTopColor: `var(--mantine-color-${color}-6)` }}
                >
                  <ThemeIcon
                    size={60}
                    radius="md"
                    color={color}
                    className="mb-6 transition-transform duration-300 group-hover:scale-110"
                  >
                    <Icon size={28} />
                  </ThemeIcon>

                  <h3
                    className={`mb-3 text-2xl font-bold ${
                      isDark ? "text-white" : "text-gray-900"
                    }`}
                  >
                    {title}
                  </h3>
                  <p
                    className={`mb-6 ${
                      isDark ? "text-gray-300" : "text-gray-600"
                    }`}
                  >
                    {description}
                  </p>

                  <Button
                    variant="subtle"
                    color={color}
                    rightSection={<FaArrowRight size={14} />}
                    className="mt-auto"
                    component={Link}
                    to={link}
                  >
                    Learn More
                  </Button>
                </Card>
              </div>
            ),
          )}
        </div>
      </div>
    </section>
  );
}
