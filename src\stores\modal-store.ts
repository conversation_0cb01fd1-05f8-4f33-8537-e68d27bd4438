import { create } from "zustand";

// Define the modal store type
type ModalState = {
  // User modals
  openUserDetailsModal: boolean;
  openUserEditModal: boolean;
  openDeleteUserModal: boolean;

  // Job modals
  openJobDetailsModal: boolean;
  openJobRejectModal: boolean;

  // Company modals
  openCompanyDetailsModal: boolean;
  openCompanyBlockModal: boolean;

  // Employer modals
  openCandidateDetailsModal: boolean;
  openConfirmDeleteJobModal: boolean;

  // Actions
  openModal: (modalName: string) => void;
  closeModal: (modalName: string) => void;
  toggleModal: (modalName: string) => void;
};

// Create the modal store
export const useModalStore = create<ModalState>((set) => ({
  // Initial state - all modals closed
  openUserDetailsModal: false,
  openUserEditModal: false,
  openDeleteUserModal: false,
  openJobDetailsModal: false,
  openJobRejectModal: false,
  openCompanyDetailsModal: false,
  openCompanyBlockModal: false,
  openCandidateDetailsModal: false,
  openConfirmDeleteJobModal: false,

  // Actions
  openModal: (modalName: string) =>
    set({ [modalName]: true } as unknown as Partial<ModalState>),

  closeModal: (modalName: string) =>
    set({ [modalName]: false } as unknown as Partial<ModalState>),

  toggleModal: (modalName: string) =>
    set(
      (state) =>
        ({
          [modalName]: !state[modalName as keyof ModalState],
        }) as unknown as Partial<ModalState>,
    ),
}));

// Helper hooks for specific modals
export const useUserDetailsModal = () => {
  const isOpen = useModalStore((state) => state.openUserDetailsModal);
  const openModal = useModalStore((state) => state.openModal);
  const closeModal = useModalStore((state) => state.closeModal);

  return {
    isOpen,
    open: () => openModal("openUserDetailsModal"),
    close: () => closeModal("openUserDetailsModal"),
  };
};

export const useUserEditModal = () => {
  const isOpen = useModalStore((state) => state.openUserEditModal);
  const openModal = useModalStore((state) => state.openModal);
  const closeModal = useModalStore((state) => state.closeModal);

  return {
    isOpen,
    open: () => openModal("openUserEditModal"),
    close: () => closeModal("openUserEditModal"),
  };
};

export const useDeleteUserModal = () => {
  const isOpen = useModalStore((state) => state.openDeleteUserModal);
  const openModal = useModalStore((state) => state.openModal);
  const closeModal = useModalStore((state) => state.closeModal);

  return {
    isOpen,
    open: () => openModal("openDeleteUserModal"),
    close: () => closeModal("openDeleteUserModal"),
  };
};

export const useJobDetailsModal = () => {
  const isOpen = useModalStore((state) => state.openJobDetailsModal);
  const openModal = useModalStore((state) => state.openModal);
  const closeModal = useModalStore((state) => state.closeModal);

  return {
    isOpen,
    open: () => openModal("openJobDetailsModal"),
    close: () => closeModal("openJobDetailsModal"),
  };
};

export const useJobRejectModal = () => {
  const isOpen = useModalStore((state) => state.openJobRejectModal);
  const openModal = useModalStore((state) => state.openModal);
  const closeModal = useModalStore((state) => state.closeModal);

  return {
    isOpen,
    open: () => openModal("openJobRejectModal"),
    close: () => closeModal("openJobRejectModal"),
  };
};

export const useCompanyDetailsModal = () => {
  const isOpen = useModalStore((state) => state.openCompanyDetailsModal);
  const openModal = useModalStore((state) => state.openModal);
  const closeModal = useModalStore((state) => state.closeModal);

  return {
    isOpen,
    open: () => openModal("openCompanyDetailsModal"),
    close: () => closeModal("openCompanyDetailsModal"),
  };
};

export const useCompanyBlockModal = () => {
  const isOpen = useModalStore((state) => state.openCompanyBlockModal);
  const openModal = useModalStore((state) => state.openModal);
  const closeModal = useModalStore((state) => state.closeModal);

  return {
    isOpen,
    open: () => openModal("openCompanyBlockModal"),
    close: () => closeModal("openCompanyBlockModal"),
  };
};

export const useCandidateDetailsModal = () => {
  const isOpen = useModalStore((state) => state.openCandidateDetailsModal);
  const openModal = useModalStore((state) => state.openModal);
  const closeModal = useModalStore((state) => state.closeModal);

  return {
    isOpen,
    open: () => openModal("openCandidateDetailsModal"),
    close: () => closeModal("openCandidateDetailsModal"),
  };
};

export const useConfirmDeleteJobModal = () => {
  const isOpen = useModalStore((state) => state.openConfirmDeleteJobModal);
  const openModal = useModalStore((state) => state.openModal);
  const closeModal = useModalStore((state) => state.closeModal);

  return {
    isOpen,
    open: () => openModal("openConfirmDeleteJobModal"),
    close: () => closeModal("openConfirmDeleteJobModal"),
  };
};
