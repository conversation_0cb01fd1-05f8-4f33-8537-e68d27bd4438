import { z } from "zod";

// Create a simplified schema for employer registration
export const employerSimpleSchema = z
  .object({
    // Company information as the main user data
    companyName: z
      .string()
      .min(2, "Company name must be at least 2 characters"),
    email: z.string().email("Invalid email address"),
    password: z
      .string()
      .min(8, "Password must be at least 8 characters")
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
        "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character",
      ),
    passwordConfirmation: z.string(),
    phone: z.string().min(10, "Phone number must be at least 10 digits"),
    website: z.string().url("Invalid URL").optional().or(z.literal("")),
    description: z
      .string()
      .min(10, "Description must be at least 10 characters"),
    industry: z.string().min(1, "Industry is required"),
    companySize: z.string().min(1, "Company size is required"),
    role: z.literal("employer"),
  })
  .refine((data) => data.password === data.passwordConfirmation, {
    message: "Passwords do not match",
    path: ["passwordConfirmation"],
  });

