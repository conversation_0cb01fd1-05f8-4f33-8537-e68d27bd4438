import { Select, TextInput } from "@mantine/core";
import { FaFilter, FaSearch } from "react-icons/fa";

type ApplicationsSearchFilterProps = {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  statusFilter: string | null;
  setStatusFilter: (status: string | null) => void;
};

export default function ApplicationsSearchFilter({
  searchQuery,
  setSearchQuery,
  statusFilter,
  setStatusFilter,
}: ApplicationsSearchFilterProps) {
  return (
    <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
      <TextInput
        placeholder="Search by candidate name, email or job title"
        leftSection={<FaSearch size={16} className="text-gray-500" />}
        className="flex-1"
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
      />
      <Select
        placeholder="Filter by status"
        data={[
          { value: "pending", label: "Pending" },
          { value: "reviewed", label: "Reviewed" },
          { value: "accepted", label: "Accepted" },
          { value: "rejected", label: "Rejected" },
        ]}
        leftSection={<FaFilter size={16} className="text-gray-500" />}
        className="w-full sm:w-48"
        value={statusFilter}
        onChange={setStatusFilter}
        clearable
      />
    </div>
  );
}
