import { URLS } from "@/utils/urls";
import { Button, Group, useMantineColorScheme } from "@mantine/core";
import { FaArrowRight, FaBriefcase, FaSearch } from "react-icons/fa";
import { Link } from "react-router";

// Company logos for the "jobs posted this month" section
const companyLogos = [
  "https://img.icons8.com/color/64/000000/google-logo.png",
  "https://img.icons8.com/color/64/000000/microsoft.png",
  "https://img.icons8.com/color/64/000000/amazon.png",
  "https://img.icons8.com/color/64/000000/netflix.png",
];

export default function Hero() {
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  return (
    <section
      className={`relative overflow-hidden py-20 ${
        isDark
          ? "bg-gradient-to-br from-gray-900/50 via-gray-800/30 to-gray-900/80"
          : "bg-gradient-to-br from-primary-color/5 via-primary-color/10 to-blue-50"
      }`}
    >
      {/* Background decorative elements */}
      <div
        className={`absolute -top-24 -right-24 h-64 w-64 rounded-full ${
          isDark ? "bg-primary-color/10" : "bg-primary-color/5"
        }`}
      ></div>
      <div
        className={`absolute bottom-12 -left-12 h-40 w-40 rounded-full ${
          isDark ? "bg-primary-color/10" : "bg-primary-color/5"
        }`}
      ></div>
      <div
        className={`absolute top-1/2 right-1/4 h-20 w-20 rounded-full ${
          isDark ? "bg-primary-color/10" : "bg-primary-color/5"
        }`}
      ></div>

      <div className="relative container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid items-center gap-12 md:grid-cols-2">
          {/* Left column - Text content */}
          <div className="text-left">
            <h1
              className={`mb-6 text-4xl leading-tight font-bold tracking-tight sm:text-5xl md:text-6xl ${
                isDark ? "text-white" : "text-gray-900"
              }`}
            >
              Find Your <span className="text-primary-color">Dream Job</span> or
              Perfect Candidate
            </h1>
            <p
              className={`mb-8 text-lg ${
                isDark ? "text-gray-300" : "text-gray-600"
              }`}
            >
              JobNest connects talented professionals with their ideal careers
              and helps employers discover the perfect candidates. Our platform
              streamlines the hiring process, making it efficient and effective
              for everyone involved.
            </p>

            <Group gap="md">
              <Button
                component={Link}
                to={URLS.auth.register}
                size="lg"
                rightSection={<FaArrowRight size={14} />}
                className="bg-primary-color hover:bg-primary-color/90"
              >
                Get Started
              </Button>
              <Button
                component={Link}
                to={URLS.candidate.jobs}
                size="lg"
                variant="outline"
                leftSection={<FaSearch size={14} />}
                className={`border-primary-color text-primary-color ${
                  isDark
                    ? "hover:bg-primary-color/20"
                    : "hover:bg-primary-color/5"
                }`}
              >
                Browse Jobs
              </Button>
            </Group>

            <div className="mt-8 flex items-center gap-6">
              <div className="flex -space-x-3">
                {companyLogos.map((logo, i) => (
                  <div
                    key={i}
                    className={`h-11 w-11 rounded-full border-2 overflow-hidden flex items-center justify-center shadow-md transform transition-transform hover:scale-110 hover:z-10 relative ${
                      isDark
                        ? "border-gray-700 bg-gray-800"
                        : "border-white bg-white"
                    }`}
                    style={{ zIndex: 10 - i }}
                  >
                    <div
                      className={`h-8 w-8 rounded-full border p-1 flex items-center justify-center ${
                        isDark
                          ? "border-gray-600 bg-gray-700"
                          : "border-gray-200 bg-gray-50"
                      }`}
                    >
                      <img
                        src={logo}
                        alt={`Company ${i + 1}`}
                        className="h-full w-full object-contain"
                      />
                    </div>
                  </div>
                ))}
              </div>
              <p
                className={`text-sm ${
                  isDark ? "text-gray-300" : "text-gray-600"
                }`}
              >
                <span className="font-semibold">1,000+</span> jobs posted this
                month
              </p>
            </div>
          </div>

          {/* Right column - Visual element */}
          <div className="relative hidden md:block">
            <div
              className={`relative rounded-lg p-6 shadow-xl ${
                isDark ? "bg-gray-800" : "bg-white"
              }`}
            >
              <div className="mb-4 flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div
                    className={`flex h-12 w-12 items-center justify-center rounded-full ${
                      isDark ? "bg-primary-color/20" : "bg-primary-color/10"
                    }`}
                  >
                    <FaBriefcase className="text-primary-color" size={20} />
                  </div>
                  <div>
                    <h3
                      className={`font-semibold ${
                        isDark ? "text-white" : "text-gray-900"
                      }`}
                    >
                      Senior Developer
                    </h3>
                    <p
                      className={`text-sm ${
                        isDark ? "text-gray-400" : "text-gray-500"
                      }`}
                    >
                      TechCorp Inc.
                    </p>
                  </div>
                </div>
                <span
                  className={`rounded-full px-3 py-1 text-xs font-medium ${
                    isDark
                      ? "bg-green-900/30 text-green-400"
                      : "bg-green-100 text-green-800"
                  }`}
                >
                  New
                </span>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <span
                    className={`text-sm font-medium ${
                      isDark ? "text-gray-300" : "text-gray-700"
                    }`}
                  >
                    Location:
                  </span>
                  <span
                    className={`text-sm ${
                      isDark ? "text-gray-400" : "text-gray-600"
                    }`}
                  >
                    Remote
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <span
                    className={`text-sm font-medium ${
                      isDark ? "text-gray-300" : "text-gray-700"
                    }`}
                  >
                    Salary:
                  </span>
                  <span
                    className={`text-sm ${
                      isDark ? "text-gray-400" : "text-gray-600"
                    }`}
                  >
                    $80K - $120K
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <span
                    className={`text-sm font-medium ${
                      isDark ? "text-gray-300" : "text-gray-700"
                    }`}
                  >
                    Type:
                  </span>
                  <span
                    className={`text-sm ${
                      isDark ? "text-gray-400" : "text-gray-600"
                    }`}
                  >
                    Full-time
                  </span>
                </div>
              </div>

              <Button
                fullWidth
                className="bg-primary-color hover:bg-primary-color/90 mt-4"
              >
                Apply Now
              </Button>
            </div>

            {/* Decorative elements */}
            <div
              className={`absolute -right-4 -bottom-4 h-24 w-24 rounded-lg ${
                isDark ? "bg-primary-color/20" : "bg-primary-color/10"
              }`}
            ></div>
            <div
              className={`absolute -top-4 -left-4 h-16 w-16 rounded-lg ${
                isDark ? "bg-primary-color/20" : "bg-primary-color/10"
              }`}
            ></div>
          </div>
        </div>
      </div>
    </section>
  );
}
