import { URLS } from "@/utils/urls";
import { Badge, Button, Group, Tabs } from "@mantine/core";
import {
  FaBriefcase,
  FaPlus,
  FaTachometerAlt,
  FaUsers,
  FaCog,
} from "react-icons/fa";
import { Link, useLocation } from "react-router";

export default function EmployerNavigation() {
  const location = useLocation();
  const currentPath = location.pathname;

  // Determine active tab based on current path
  const getActiveTab = () => {
    if (currentPath.includes("/dashboard")) return "dashboard";
    if (currentPath.includes("/manage-jobs")) return "manage-jobs";
    if (currentPath.includes("/candidates")) return "candidates";
    if (currentPath.includes("/create-job")) return "create-job";
    if (currentPath.includes("/profile")) return "profile";
    return "dashboard";
  };

  const navigationItems = [
    {
      value: "dashboard",
      label: "Dashboard",
      icon: <FaTachometerAlt size={16} />,
      href: URLS.employer.dashboard,
    },
    {
      value: "create-job",
      label: "Post Job",
      icon: <FaPlus size={16} />,
      href: URLS.employer.createJob,
      badge: "New",
    },
    {
      value: "manage-jobs",
      label: "Manage Jobs",
      icon: <FaBriefcase size={16} />,
      href: URLS.employer.manageJobs,
    },
    {
      value: "candidates",
      label: "Candidates",
      icon: <FaUsers size={16} />,
      href: URLS.employer.candidates,
    },
    {
      value: "profile",
      label: "Profile",
      icon: <FaCog size={16} />,
      href: URLS.employer.profile,
    },
  ];

  return (
    <div className="mb-6">
      {/* Desktop Navigation */}
      <div className="hidden md:block">
        <Tabs value={getActiveTab()} variant="pills" radius="md">
          <Tabs.List>
            {navigationItems.map((item) => (
              <Link key={item.value} to={item.href}>
                <Tabs.Tab
                  value={item.value}
                  leftSection={item.icon}
                  rightSection={
                    item.badge ? (
                      <Badge size="xs" color="blue">
                        {item.badge}
                      </Badge>
                    ) : null
                  }
                >
                  {item.label}
                </Tabs.Tab>
              </Link>
            ))}
          </Tabs.List>
        </Tabs>
      </div>

      {/* Mobile Navigation */}
      <div className="block md:hidden">
        <Group gap="xs" className="overflow-x-auto pb-2">
          {navigationItems.map((item) => (
            <Button
              key={item.value}
              component={Link}
              to={item.href}
              variant={getActiveTab() === item.value ? "filled" : "light"}
              size="sm"
              leftSection={item.icon}
              rightSection={
                item.badge ? (
                  <Badge size="xs" color="blue">
                    {item.badge}
                  </Badge>
                ) : null
              }
              className="flex-shrink-0"
            >
              {item.label}
            </Button>
          ))}
        </Group>
      </div>
    </div>
  );
}
