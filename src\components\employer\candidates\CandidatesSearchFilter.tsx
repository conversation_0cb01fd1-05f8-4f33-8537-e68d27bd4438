import { Button, Grid, Group, Select, TextInput } from "@mantine/core";
import { DatePickerInput } from "@mantine/dates";
import { useState } from "react";
import {
  FaCalendarAlt,
  FaFilter,
  FaSearch,
  FaSortAmountDown,
  FaUserTag,
} from "react-icons/fa";

// Sample jobs data
const jobs = [
  {
    id: 1,
    title: "Software Engineer",
  },
  {
    id: 2,
    title: "Product Manager",
  },
  {
    id: 3,
    title: "UX Designer",
  },
];

export default function CandidatesSearchFilter() {
  const [selectedJob, setSelectedJob] = useState<string | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState<[string | null, string | null]>([
    null,
    null,
  ]);
  const [sortBy, setSortBy] = useState<string | null>("recent");
  const [searchQuery, setSearchQuery] = useState("");

  // Reset all filters
  const resetFilters = () => {
    setSelectedJob(null);
    setSelectedStatus(null);
    setDateRange([null, null]);
    setSortBy("recent");
    setSearchQuery("");
  };

  // Apply filters
  const applyFilters = () => {
    // Logic to apply filters and update the parent component
    console.log({
      job: selectedJob,
      status: selectedStatus,
      dateRange,
      sortBy,
      searchQuery,
    });
  };

  return (
    <div className="space-y-4">
      {/* Search and main filters */}
      <Grid gutter="md">
        <Grid.Col span={{ base: 12, md: 6 }}>
          <TextInput
            placeholder="Search by name, email, or job title"
            leftSection={<FaSearch size={16} />}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </Grid.Col>
        <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
          <Select
            placeholder="Filter by job"
            data={jobs.map((job) => ({
              value: job.id.toString(),
              label: job.title,
            }))}
            leftSection={<FaFilter size={16} />}
            value={selectedJob}
            onChange={setSelectedJob}
            clearable
          />
        </Grid.Col>
        <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
          <Select
            placeholder="Filter by status"
            data={[
              { value: "applied", label: "Applied" },
              { value: "shortlisted", label: "Shortlisted" },
              { value: "interviewed", label: "Interviewed" },
              { value: "rejected", label: "Rejected" },
              { value: "hired", label: "Hired" },
            ]}
            leftSection={<FaUserTag size={16} />}
            value={selectedStatus}
            onChange={setSelectedStatus}
            clearable
          />
        </Grid.Col>
      </Grid>

      {/* Advanced filters */}
      <Grid gutter="md">
        <Grid.Col span={{ base: 12, md: 6 }}>
          <DatePickerInput
            type="range"
            placeholder="Filter by application date"
            leftSection={<FaCalendarAlt size={16} />}
            value={dateRange}
            onChange={setDateRange}
            clearable
          />
        </Grid.Col>
        <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
          <Select
            placeholder="Sort by"
            data={[
              { value: "recent", label: "Most Recent" },
              { value: "oldest", label: "Oldest First" },
              { value: "name_asc", label: "Name (A-Z)" },
              { value: "name_desc", label: "Name (Z-A)" },
            ]}
            leftSection={<FaSortAmountDown size={16} />}
            value={sortBy}
            onChange={setSortBy}
          />
        </Grid.Col>
        <Grid.Col span={{ base: 12, sm: 6, md: 3 }} className="flex items-end">
          <Group grow>
            <Button variant="outline" onClick={resetFilters} size="sm">
              Reset
            </Button>
            <Button onClick={applyFilters} size="sm">
              Apply
            </Button>
          </Group>
        </Grid.Col>
      </Grid>
    </div>
  );
}
