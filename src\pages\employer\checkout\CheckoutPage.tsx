import { <PERSON><PERSON><PERSON><PERSON>, PageHeading } from "@/design-system/components";
import { URLS } from "@/utils/urls";
import {
  Anchor,
  Box,
  Button,
  Card,
  Divider,
  Grid,
  Group,
  List,
  Select,
  Stepper,
  Text,
  TextInput,
  ThemeIcon,
  Title,
} from "@mantine/core";
import { useForm } from "@mantine/form";
import { notifications } from "@mantine/notifications";
import { useState } from "react";
import {
  FaCheck,
  FaChevronRight,
  FaCreditCard,
  FaInfoCircle,
  FaShieldAlt,
  FaUser,
} from "react-icons/fa";
import { Link, useNavigate, useSearchParams } from "react-router";

// Plan data - in a real app, this would come from an API or context
const plans = {
  basic: {
    name: "Basic",
    price: 29,
    annualPrice: 295,
    features: [
      "2 active job postings",
      "30-day job visibility",
      "50 CV views per month",
      "Basic candidate filtering",
      "Email notifications",
    ],
  },
  professional: {
    name: "Professional",
    price: 59,
    annualPrice: 565,
    features: [
      "5 active job postings",
      "45-day job visibility",
      "200 CV views per month",
      "Advanced candidate filtering",
      "Priority placement",
    ],
  },
  enterprise: {
    name: "Enterprise",
    price: 149,
    annualPrice: 1341,
    features: [
      "Unlimited active job postings",
      "60-day job visibility",
      "Unlimited CV views",
      "Advanced analytics and reporting",
      "Dedicated account manager",
    ],
  },
};

// Country options for the form
const countries = [
  { value: "us", label: "United States" },
  { value: "ca", label: "Canada" },
  { value: "uk", label: "United Kingdom" },
  { value: "au", label: "Australia" },
  { value: "de", label: "Germany" },
  { value: "fr", label: "France" },
  { value: "jp", label: "Japan" },
  { value: "in", label: "India" },
  { value: "br", label: "Brazil" },
  { value: "mx", label: "Mexico" },
];

export default function CheckoutPage() {
  const [searchParams] = useSearchParams();
  const planId = searchParams.get("plan") || "professional";
  const billingPeriod = searchParams.get("billing") || "month";
  const plan = plans[planId as keyof typeof plans];
  const navigate = useNavigate();

  // Stepper state
  const [active, setActive] = useState(0);

  // Format price with currency
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      maximumFractionDigits: 0,
    }).format(price);
  };

  // Get price based on billing period
  const getPrice = () => {
    if (billingPeriod === "month") {
      return plan.price;
    } else {
      return plan.annualPrice;
    }
  };

  // Calculate tax (example: 10%)
  const calculateTax = () => {
    return getPrice() * 0.1;
  };

  // Calculate total
  const calculateTotal = () => {
    return getPrice() + calculateTax();
  };

  // Form validation
  const form = useForm({
    initialValues: {
      firstName: "",
      lastName: "",
      email: "",
      company: "",
      address: "",
      city: "",
      state: "",
      zipCode: "",
      country: "us",
    },
    validate: {
      firstName: (value) =>
        value.length < 2 ? "First name is too short" : null,
      lastName: (value) => (value.length < 2 ? "Last name is too short" : null),
      email: (value) => (/^\S+@\S+$/.test(value) ? null : "Invalid email"),
      company: (value) =>
        value.length < 2 ? "Company name is too short" : null,
      address: (value) => (value.length < 5 ? "Address is too short" : null),
      city: (value) => (value.length < 2 ? "City is too short" : null),
      state: (value) => (value.length < 2 ? "State is too short" : null),
      zipCode: (value) =>
        /^\d{5}(-\d{4})?$/.test(value) ? null : "Invalid ZIP code",
    },
  });

  // Handle form submission
  const handleSubmit = (values: typeof form.values) => {
    // In a real app, you would send this data to your backend
    console.log(values);

    // Show success notification
    notifications.show({
      title: "Billing information saved",
      message: "Proceeding to payment method selection",
      color: "green",
    });

    // Move to next step
    setActive(1);
  };

  // Handle continue to payment
  const handleContinueToPayment = () => {
    navigate(
      `/employer/checkout/payment?plan=${planId}&billing=${billingPeriod}`,
    );
  };

  return (
    <PageContainer
      breadcrumbItems={[
        { title: "Home", href: "/" },
        { title: "Pricing", href: URLS.employer.pricing },
        { title: "Checkout" },
      ]}
      variant="employer"
      className="pb-12"
    >
      <PageHeading
        title="Complete Your Purchase"
        subtitle="You're just a few steps away from upgrading your hiring capabilities"
        className="mb-8"
        variant="employer"
      />

      {/* Checkout Process Stepper */}
      <Stepper
        active={active}
        onStepClick={(step) => {
          // Only allow clicking on previous steps
          if (step < active) {
            setActive(step);
          }
        }}
        className="mb-8"
        color="primary"
        allowNextStepsSelect={false}
      >
        <Stepper.Step
          label="Billing Information"
          description="Enter your details"
          icon={<FaUser size={18} />}
        />
        <Stepper.Step
          label="Payment Method"
          description="Choose how to pay"
          icon={<FaCreditCard size={18} />}
        />
        <Stepper.Step
          label="Confirmation"
          description="Review and confirm"
          icon={<FaCheck size={18} />}
        />
      </Stepper>

      <Grid gutter="xl">
        {/* Left Column - Form */}
        <Grid.Col span={{ base: 12, md: 8 }}>
          {active === 0 ? (
            <Card withBorder radius="md" className="mb-4">
              <Title order={3} className="mb-4">
                Billing Information
              </Title>
              <Box>
                <Grid>
                  <Grid.Col span={{ base: 12, sm: 6 }}>
                    <TextInput
                      label="First Name"
                      placeholder="John"
                      required
                      {...form.getInputProps("firstName")}
                    />
                  </Grid.Col>
                  <Grid.Col span={{ base: 12, sm: 6 }}>
                    <TextInput
                      label="Last Name"
                      placeholder="Doe"
                      required
                      {...form.getInputProps("lastName")}
                    />
                  </Grid.Col>
                  <Grid.Col span={12}>
                    <TextInput
                      label="Email"
                      placeholder="<EMAIL>"
                      required
                      {...form.getInputProps("email")}
                    />
                  </Grid.Col>
                  <Grid.Col span={12}>
                    <TextInput
                      label="Company"
                      placeholder="Acme Inc."
                      required
                      {...form.getInputProps("company")}
                    />
                  </Grid.Col>
                  <Grid.Col span={12}>
                    <TextInput
                      label="Address"
                      placeholder="123 Main St"
                      required
                      {...form.getInputProps("address")}
                    />
                  </Grid.Col>
                  <Grid.Col span={{ base: 12, sm: 6 }}>
                    <TextInput
                      label="City"
                      placeholder="New York"
                      required
                      {...form.getInputProps("city")}
                    />
                  </Grid.Col>
                  <Grid.Col span={{ base: 12, sm: 6 }}>
                    <TextInput
                      label="State/Province"
                      placeholder="NY"
                      required
                      {...form.getInputProps("state")}
                    />
                  </Grid.Col>
                  <Grid.Col span={{ base: 12, sm: 6 }}>
                    <TextInput
                      label="ZIP/Postal Code"
                      placeholder="10001"
                      required
                      {...form.getInputProps("zipCode")}
                    />
                  </Grid.Col>
                  <Grid.Col span={{ base: 12, sm: 6 }}>
                    <Select
                      label="Country"
                      placeholder="Select country"
                      data={countries}
                      required
                      {...form.getInputProps("country")}
                    />
                  </Grid.Col>
                  <Grid.Col span={12} className="mt-4">
                    <Group justify="flex-end">
                      <Button
                        onClick={() => {
                          const validation = form.validate();
                          if (!validation.hasErrors) {
                            handleSubmit(form.values);
                          }
                        }}
                        size="md"
                        rightSection={<FaChevronRight size={14} />}
                      >
                        Continue
                      </Button>
                    </Group>
                  </Grid.Col>
                </Grid>
              </Box>
            </Card>
          ) : (
            <Card withBorder radius="md" className="mb-4">
              <Title order={3} className="mb-4">
                Review and Continue
              </Title>
              <Text className="!my-4">
                Your billing information has been saved. Click the button below
                to proceed to payment.
              </Text>
              <Button
                onClick={handleContinueToPayment}
                size="md"
                rightSection={<FaChevronRight size={14} />}
              >
                Continue to Payment
              </Button>
            </Card>
          )}
        </Grid.Col>

        {/* Right Column - Order Summary */}
        <Grid.Col span={{ base: 12, md: 4 }}>
          <Card withBorder radius="md" className="bg-gray-50">
            <Title order={3} className="mb-4">
              Order Summary
            </Title>
            <Box className="mb-4">
              <Group justify="space-between" className="mb-2">
                <Text fw={500}>{plan.name} Plan</Text>
                <Text fw={500}>{formatPrice(getPrice())}</Text>
              </Group>
              <Text size="sm" c="dimmed">
                {billingPeriod === "month" ? "Monthly" : "Annual"} subscription
              </Text>
            </Box>

            <List
              spacing="xs"
              size="sm"
              center
              icon={
                <ThemeIcon color="green" size={20} radius="xl">
                  <FaCheck size={10} />
                </ThemeIcon>
              }
              className="mb-4"
            >
              {plan.features.map((feature, index) => (
                <List.Item key={index}>{feature}</List.Item>
              ))}
            </List>

            <Divider my="md" />

            <Group justify="space-between" className="mb-2">
              <Text>Subtotal</Text>
              <Text>{formatPrice(getPrice())}</Text>
            </Group>
            <Group justify="space-between" className="mb-2">
              <Text>Tax (10%)</Text>
              <Text>{formatPrice(calculateTax())}</Text>
            </Group>
            <Divider my="md" />
            <Group justify="space-between" className="mb-2">
              <Text fw={700}>Total</Text>
              <Text fw={700} size="lg">
                {formatPrice(calculateTotal())}
              </Text>
            </Group>
            <Text size="xs" c="dimmed" className="mt-4">
              {billingPeriod === "month"
                ? "You will be charged monthly until you cancel."
                : "You will be charged annually until you cancel."}
            </Text>

            <Box className="mt-6 bg-primary-50 p-3 rounded-md">
              <Group gap="xs">
                <ThemeIcon
                  color="primary"
                  variant="light"
                  size="md"
                  radius="xl"
                >
                  <FaShieldAlt size={14} />
                </ThemeIcon>
                <Text size="sm" fw={500}>
                  Secure Checkout
                </Text>
              </Group>
              <Text size="xs" c="dimmed" className="mt-2">
                Your payment information is encrypted and secure. We use
                industry-standard security measures to protect your data.
              </Text>
            </Box>
          </Card>

          <Box className="mt-4">
            <Group gap="xs" className="mb-2">
              <FaInfoCircle size={14} className="text-gray-500" />
              <Text size="sm">Need help?</Text>
            </Group>
            <Text size="xs" c="dimmed">
              If you have any questions about your purchase, please{" "}
              <Anchor component={Link} to="/contact-us" size="xs">
                contact our support team
              </Anchor>
              .
            </Text>
          </Box>
        </Grid.Col>
      </Grid>
    </PageContainer>
  );
}
