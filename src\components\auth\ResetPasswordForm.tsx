import { resetPasswordSchema } from "@/schemas/auth/auth-schemas";
import { Button, PasswordInput, Text } from "@mantine/core";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import { useState } from "react";
import { FaLock } from "react-icons/fa";
import { Link } from "react-router";

export default function ResetPasswordForm() {
  const [isSubmitted, setIsSubmitted] = useState(false);

  const form = useForm({
    initialValues: {
      password: "",
      passwordConfirmation: "",
    },
    validate: zodResolver(resetPasswordSchema),
  });

  const handleSubmit = (values: typeof form.values) => {
    // Form is valid, you can handle reset password logic here
    console.log("Reset password form submitted:", values);
    setIsSubmitted(true);
  };

  if (isSubmitted) {
    return (
      <div className="flex flex-col items-center gap-4 text-center">
        <Text size="lg" fw={500}>
          Password Reset Successful
        </Text>
        <Text>
          Your password has been reset successfully. You can now login with your
          new password.
        </Text>
        <Link to="/auth/login">
          <Button mt="xl">Go to Login</Button>
        </Link>
      </div>
    );
  }

  return (
    <form
      noValidate
      onSubmit={form.onSubmit(handleSubmit)}
      className="flex flex-col gap-4"
    >
      <Text size="lg" fw={500} ta="center" mb="md">
        Reset Your Password
      </Text>
      <Text size="sm" ta="center" mb="lg" c="dimmed">
        Enter your new password below.
      </Text>

      <PasswordInput
        label="New Password"
        placeholder="Your new password"
        required
        leftSection={<FaLock />}
        {...form.getInputProps("password")}
      />

      <PasswordInput
        label="Confirm New Password"
        placeholder="Confirm your new password"
        required
        leftSection={<FaLock />}
        {...form.getInputProps("passwordConfirmation")}
      />

      <Button type="submit" fullWidth mt="xl">
        Reset Password
      </Button>
    </form>
  );
}
