import { candidateData } from "@/data/candidate-data";
import {
  ActionIcon,
  Avatar,
  Badge,
  Button,
  Card,
  Divider,
  Grid,
  Group,
  Progress,
  Tabs,
  Text,
  Title,
  Tooltip,
} from "@mantine/core";
import { useState } from "react";
import {
  FaBriefcase,
  FaCalendarAlt,
  FaCertificate,
  FaDownload,
  FaEnvelope,
  FaFileAlt,
  FaGithub,
  FaGlobe,
  FaGraduationCap,
  FaLinkedin,
  FaPhone,
  FaStar,
  FaUserCheck,
  FaUserFriends,
  FaUserTimes,
} from "react-icons/fa";
import { Link, useParams } from "react-router";

export default function CandidateProfilePageContent() {
  const params = useParams();
  const candidateId = params.candidate_id;

  console.log(candidateId);

  // In a real app, you would fetch the candidate data based on the ID
  // For now, we'll use the mock data

  const [activeTab, setActiveTab] = useState<string | null>("profile");

  // Calculate a match score (this would be calculated based on job requirements in a real app)
  const matchScore = 85;

  return (
    <>
      {/* Hero Section with Candidate Overview */}
      <Card withBorder radius="md" className="mb-8 overflow-hidden">
        <div className="relative">
          {/* Background gradient */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-indigo-50"></div>

          <div className="relative p-6 md:p-8">
            <Grid>
              <Grid.Col span={{ base: 12, sm: 8 }}>
                <Group align="flex-start" className="mb-4">
                  <Avatar
                    size={100}
                    radius="md"
                    color="blue"
                    className="border-4 border-white shadow-md"
                  >
                    {candidateData.name.substring(0, 2).toUpperCase()}
                  </Avatar>

                  <div>
                    <Group gap="xs" align="center">
                      <Title order={1} className="text-2xl font-bold">
                        {candidateData.name}
                      </Title>
                      <Badge
                        color={
                          candidateData.status === "Applied"
                            ? "blue"
                            : candidateData.status === "Shortlisted"
                              ? "green"
                              : candidateData.status === "Interviewed"
                                ? "violet"
                                : candidateData.status === "Hired"
                                  ? "teal"
                                  : "red"
                        }
                        size="lg"
                      >
                        {candidateData.status}
                      </Badge>
                    </Group>

                    <Text size="lg" className="mb-2 text-gray-700">
                      {candidateData.jobTitle}
                    </Text>

                    <Group gap="lg" className="mt-3">
                      <Group gap="xs">
                        <FaEnvelope className="text-blue-500" />
                        <Text size="sm">{candidateData.email}</Text>
                      </Group>

                      <Group gap="xs">
                        <FaPhone className="text-blue-500" />
                        <Text size="sm">{candidateData.phone}</Text>
                      </Group>

                      <Group gap="xs">
                        <FaCalendarAlt className="text-blue-500" />
                        <Text size="sm">
                          Applied: {candidateData.appliedDate}
                        </Text>
                      </Group>
                    </Group>
                  </div>
                </Group>
              </Grid.Col>

              <Grid.Col span={{ base: 12, sm: 4 }}>
                <Card withBorder radius="md" className="bg-white shadow-sm">
                  <Text fw={700} ta="center" className="mb-2">
                    Match Score
                  </Text>
                  <Text
                    ta="center"
                    size="xl"
                    fw={700}
                    className="text-blue-600"
                  >
                    {matchScore}%
                  </Text>
                  <Progress
                    value={matchScore}
                    color={
                      matchScore > 80
                        ? "green"
                        : matchScore > 60
                          ? "blue"
                          : "orange"
                    }
                    size="lg"
                    radius="xl"
                    className="mt-2 mb-3"
                  />
                  <Group grow>
                    <Button
                      leftSection={<FaUserCheck size={16} />}
                      color="green"
                      variant="light"
                    >
                      Shortlist
                    </Button>
                    <Button
                      leftSection={<FaUserTimes size={16} />}
                      color="red"
                      variant="light"
                    >
                      Reject
                    </Button>
                  </Group>
                </Card>
              </Grid.Col>
            </Grid>

            {/* Quick Action Buttons */}
            <Group className="mt-4">
              <Button
                component={Link}
                to={candidateData.resume}
                target="_blank"
                leftSection={<FaDownload size={16} />}
                variant="filled"
                color="blue"
              >
                Download Resume
              </Button>

              <Button
                component={Link}
                to={candidateData.coverLetter}
                target="_blank"
                leftSection={<FaFileAlt size={16} />}
                variant="outline"
              >
                View Cover Letter
              </Button>

              <Group ml="auto" gap="xs">
                <Tooltip label="LinkedIn Profile">
                  <ActionIcon
                    component={Link}
                    to={candidateData.linkedin}
                    target="_blank"
                    variant="light"
                    color="blue"
                    size="lg"
                  >
                    <FaLinkedin size={20} />
                  </ActionIcon>
                </Tooltip>

                <Tooltip label="GitHub Profile">
                  <ActionIcon
                    component={Link}
                    to={candidateData.github}
                    target="_blank"
                    variant="light"
                    color="dark"
                    size="lg"
                  >
                    <FaGithub size={20} />
                  </ActionIcon>
                </Tooltip>

                <Tooltip label="Portfolio">
                  <ActionIcon
                    component={Link}
                    to={candidateData.portfolio}
                    target="_blank"
                    variant="light"
                    color="indigo"
                    size="lg"
                  >
                    <FaGlobe size={20} />
                  </ActionIcon>
                </Tooltip>
              </Group>
            </Group>
          </div>
        </div>
      </Card>

      {/* Tabbed Content */}
      <Tabs value={activeTab} onChange={setActiveTab} className="mb-6">
        <Tabs.List grow>
          <Tabs.Tab value="profile" leftSection={<FaUserFriends size={16} />}>
            Profile
          </Tabs.Tab>
          <Tabs.Tab value="skills" leftSection={<FaStar size={16} />}>
            Skills
          </Tabs.Tab>
          <Tabs.Tab value="experience" leftSection={<FaBriefcase size={16} />}>
            Experience
          </Tabs.Tab>
          <Tabs.Tab
            value="education"
            leftSection={<FaGraduationCap size={16} />}
          >
            Education
          </Tabs.Tab>
          <Tabs.Tab
            value="certifications"
            leftSection={<FaCertificate size={16} />}
          >
            Certifications
          </Tabs.Tab>
          <Tabs.Tab
            value="references"
            leftSection={<FaUserFriends size={16} />}
          >
            References
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="profile" pt="md">
          <Card withBorder radius="md" className="p-6 shadow-sm">
            <Title
              order={3}
              className="mb-4 text-xl font-semibold text-blue-800"
            >
              Candidate Overview
            </Title>
            <Grid>
              <Grid.Col span={{ base: 12, md: 6 }}>
                <Text className="mb-4 leading-relaxed text-gray-700">
                  {candidateData.name} is a {candidateData.jobTitle} who applied
                  on {candidateData.appliedDate}. They have experience in{" "}
                  {candidateData.skills.slice(0, 3).join(", ")}, and more.
                </Text>

                <Text className="mb-4 leading-relaxed text-gray-700">
                  Their most recent role was as a{" "}
                  {candidateData.experience[0].position} at{" "}
                  {candidateData.experience[0].company}, where they worked from{" "}
                  {candidateData.experience[0].duration}.
                </Text>

                <Text className="leading-relaxed text-gray-700">
                  They hold a {candidateData.education[0].degree} from{" "}
                  {candidateData.education[0].institution}.
                </Text>
              </Grid.Col>

              <Grid.Col span={{ base: 12, md: 6 }}>
                <Title
                  order={4}
                  className="mb-3 text-lg font-semibold text-blue-800"
                >
                  Top Skills
                </Title>

                {candidateData.skills.slice(0, 5).map((skill, index) => (
                  <div key={index} className="mb-3">
                    <Group justify="space-between" className="mb-1">
                      <Text size="sm" fw={500}>
                        {skill}
                      </Text>
                      <Text size="sm" c="dimmed">
                        {90 - index * 5}%
                      </Text>
                    </Group>
                    <Progress
                      value={90 - index * 5}
                      color={
                        index === 0 ? "green" : index < 3 ? "blue" : "cyan"
                      }
                      size="sm"
                      radius="xl"
                    />
                  </div>
                ))}
              </Grid.Col>
            </Grid>
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value="skills" pt="md">
          <Card withBorder radius="md" className="p-6 shadow-sm">
            <Title
              order={3}
              className="mb-4 text-xl font-semibold text-blue-800"
            >
              Skills & Expertise
            </Title>

            <Grid>
              {candidateData.skills.map((skill, index) => (
                <Grid.Col key={index} span={{ base: 12, sm: 6, md: 4 }}>
                  <Card withBorder radius="md" className="h-full">
                    <Group justify="space-between" className="mb-2">
                      <Text fw={600}>{skill}</Text>
                      <Badge
                        color={
                          index < 2 ? "green" : index < 4 ? "blue" : "cyan"
                        }
                      >
                        {index < 2
                          ? "Expert"
                          : index < 4
                            ? "Advanced"
                            : "Intermediate"}
                      </Badge>
                    </Group>
                    <Progress
                      value={95 - index * 7}
                      color={index < 2 ? "green" : index < 4 ? "blue" : "cyan"}
                      size="md"
                      radius="xl"
                      className="mb-2"
                    />
                    <Text size="sm" c="dimmed">
                      {index < 2
                        ? "Highly proficient with extensive experience"
                        : index < 4
                          ? "Strong working knowledge and practical experience"
                          : "Solid understanding and working experience"}
                    </Text>
                  </Card>
                </Grid.Col>
              ))}
            </Grid>
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value="experience" pt="md">
          <Card withBorder radius="md" className="p-6 shadow-sm">
            <Title
              order={3}
              className="mb-4 text-xl font-semibold text-blue-800"
            >
              Work Experience
            </Title>

            <div className="relative border-l-2 border-blue-200 pl-8">
              {candidateData.experience.map((exp, index) => (
                <div key={index} className="relative mb-8">
                  <div className="absolute -left-[41px] rounded-full border-2 border-blue-400 bg-white p-1">
                    <FaBriefcase size={20} className="text-blue-500" />
                  </div>

                  <Title order={4} className="mb-1 text-lg font-semibold">
                    {exp.position}
                  </Title>

                  <Group className="mb-2">
                    <Text fw={500} c="dimmed">
                      {exp.company}
                    </Text>
                    <Divider orientation="vertical" />
                    <Text size="sm" c="dimmed">
                      {exp.duration}
                    </Text>
                  </Group>

                  <Text className="text-gray-700">{exp.description}</Text>
                </div>
              ))}
            </div>
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value="education" pt="md">
          <Card withBorder radius="md" className="p-6 shadow-sm">
            <Title
              order={3}
              className="mb-4 text-xl font-semibold text-blue-800"
            >
              Education
            </Title>

            <div className="relative border-l-2 border-indigo-200 pl-8">
              {candidateData.education.map((edu, index) => (
                <div key={index} className="relative mb-8">
                  <div className="absolute -left-[41px] rounded-full border-2 border-indigo-400 bg-white p-1">
                    <FaGraduationCap size={20} className="text-indigo-500" />
                  </div>

                  <Title order={4} className="mb-1 text-lg font-semibold">
                    {edu.degree}
                  </Title>

                  <Group className="mb-2">
                    <Text fw={500} c="dimmed">
                      {edu.institution}
                    </Text>
                    <Divider orientation="vertical" />
                    <Text size="sm" c="dimmed">
                      {edu.duration}
                    </Text>
                  </Group>

                  <Text className="text-gray-700">{edu.description}</Text>
                </div>
              ))}
            </div>
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value="certifications" pt="md">
          <Card withBorder radius="md" className="p-6 shadow-sm">
            <Title
              order={3}
              className="mb-4 text-xl font-semibold text-blue-800"
            >
              Certifications
            </Title>

            <Grid>
              {candidateData.certifications.map((cert, index) => (
                <Grid.Col key={index} span={{ base: 12, sm: 6 }}>
                  <Card withBorder radius="md" className="h-full">
                    <Group justify="space-between" className="mb-2">
                      <FaCertificate size={20} className="text-amber-500" />
                      <Badge color="amber">{cert.date}</Badge>
                    </Group>

                    <Title order={4} className="mb-1 text-lg font-semibold">
                      {cert.name}
                    </Title>

                    <Text c="dimmed">Issued by {cert.issuer}</Text>
                  </Card>
                </Grid.Col>
              ))}
            </Grid>
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value="references" pt="md">
          <Card withBorder radius="md" className="p-6 shadow-sm">
            <Title
              order={3}
              className="mb-4 text-xl font-semibold text-blue-800"
            >
              Professional References
            </Title>

            <Grid>
              {candidateData.references.map((ref, index) => (
                <Grid.Col key={index} span={{ base: 12, sm: 6 }}>
                  <Card withBorder radius="md" className="h-full">
                    <Group justify="space-between" className="mb-3">
                      <Avatar
                        radius="xl"
                        color={index === 0 ? "blue" : "indigo"}
                      >
                        {ref.name.substring(0, 1).toUpperCase()}
                      </Avatar>
                      <Badge color={index === 0 ? "blue" : "indigo"}>
                        Reference {index + 1}
                      </Badge>
                    </Group>

                    <Title order={4} className="mb-1 text-lg font-semibold">
                      {ref.name}
                    </Title>

                    <Text className="mb-2">
                      {ref.position} at {ref.company}
                    </Text>

                    <Group gap="xs" className="mb-1">
                      <FaEnvelope size={14} className="text-gray-500" />
                      <Text size="sm">{ref.email}</Text>
                    </Group>

                    <Group gap="xs">
                      <FaPhone size={14} className="text-gray-500" />
                      <Text size="sm">{ref.phone}</Text>
                    </Group>
                  </Card>
                </Grid.Col>
              ))}
            </Grid>
          </Card>
        </Tabs.Panel>
      </Tabs>

      {/* Action Footer */}
      <Card withBorder radius="md" className="bg-gray-50 p-4 shadow-sm">
        <Group justify="space-between">
          <Group>
            <Button
              leftSection={<FaUserCheck size={16} />}
              color="green"
              variant="filled"
            >
              Move to Interview
            </Button>

            <Button
              leftSection={<FaUserTimes size={16} />}
              color="red"
              variant="outline"
            >
              Reject Candidate
            </Button>
          </Group>

          <Button variant="subtle" color="gray">
            Back to Applications
          </Button>
        </Group>
      </Card>
    </>
  );
}
