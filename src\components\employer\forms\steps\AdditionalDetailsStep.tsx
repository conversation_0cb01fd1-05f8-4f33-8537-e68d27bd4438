import { useThemeClasses } from "@/design-system/utils/theme-utils";
import {
  ActionIcon,
  <PERSON>ton,
  Card,
  Divider,
  FileInput,
  MultiSelect,
  Select,
  Text,
  TextInput,
  Tooltip,
} from "@mantine/core";
import { DatePickerInput } from "@mantine/dates";
import {
  FaCalendar,
  FaFileAlt,
  FaGift,
  FaInfoCircle,
  FaQuestionCircle,
  FaTrash,
} from "react-icons/fa";

const predefinedQuestions = [
  "Why do you want to work for our company?",
  "What are your strengths and weaknesses?",
  "Where do you see yourself in 5 years?",
  "Describe a challenging situation you faced and how you handled it.",
];

interface AdditionalDetailsStepProps {
  form: any;
  questions: string[];
  handleQuestionChange: (index: number, value: string) => void;
  addQuestion: () => void;
  removeQuestion: (index: number) => void;
  addPredefinedQuestion: (question: string) => void;
}

export default function AdditionalDetailsStep({
  form,
  questions,
  handleQuestionChange,
  addQuestion,
  removeQuestion,
  addPredefinedQuestion,
}: AdditionalDetailsStepProps) {
  return (
    <div className="space-y-8">
      <div
        className={useThemeClasses(
          "border-b border-gray-200 pb-4",
          "border-b border-dark-4 pb-4",
        )}
      >
        <div className="flex flex-wrap items-center justify-between gap-2">
          <div className="flex items-center gap-2">
            <div
              className={useThemeClasses(
                "flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-blue-600",
                "flex h-8 w-8 items-center justify-center rounded-full bg-blue-900/30 text-blue-300",
              )}
            >
              <FaGift size={16} />
            </div>
            <Text
              className={useThemeClasses(
                "text-xl font-semibold text-gray-800",
                "text-xl font-semibold text-gray-100",
              )}
            >
              Benefits & Perks
            </Text>
          </div>
        </div>
        <Text size="sm" c="dimmed" mt="xs">
          Highlight the benefits and perks that come with this position
        </Text>
      </div>

      <MultiSelect
        label={
          <div className="flex items-center gap-1">
            <Text className="font-medium">Benefits and Perks</Text>
            <Tooltip label="Highlight the benefits that make your company stand out">
              <FaInfoCircle size={14} className="cursor-help text-gray-400" />
            </Tooltip>
          </div>
        }
        placeholder="Select benefits and perks"
        data={[
          { value: "health-insurance", label: "Health Insurance" },
          { value: "dental-insurance", label: "Dental Insurance" },
          { value: "vision-insurance", label: "Vision Insurance" },
          { value: "remote-work", label: "Remote Work" },
          { value: "flexible-hours", label: "Flexible Hours" },
          { value: "paid-time-off", label: "Paid Time Off" },
          { value: "bonuses", label: "Bonuses" },
          { value: "stock-options", label: "Stock Options" },
          { value: "retirement-plan", label: "Retirement Plan" },
          { value: "parental-leave", label: "Parental Leave" },
          {
            value: "professional-development",
            label: "Professional Development",
          },
          { value: "gym-membership", label: "Gym Membership" },
          { value: "free-lunch", label: "Free Lunch/Meals" },
          { value: "company-events", label: "Company Events" },
        ]}
        leftSection={<FaGift size={18} className="text-primary-color" />}
        {...form.getInputProps("benefits")}
        error={form.errors.benefits}
        aria-label="Benefits and Perks"
        searchable
        clearable
        size="md"
        radius="md"
        className="mb-4"
      />

      <Divider my="lg" />

      <div
        className={useThemeClasses(
          "border-t border-gray-200 pt-6",
          "border-t border-dark-4 pt-6",
        )}
      >
        <div className="mb-4 flex items-center gap-2">
          <div
            className={useThemeClasses(
              "flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-blue-600",
              "flex h-8 w-8 items-center justify-center rounded-full bg-blue-900/30 text-blue-300",
            )}
          >
            <FaCalendar size={16} />
          </div>
          <Text
            className={useThemeClasses(
              "text-xl font-semibold text-gray-800",
              "text-xl font-semibold text-gray-100",
            )}
          >
            Application Details
          </Text>
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <DatePickerInput
            label={
              <div className="flex items-center gap-1">
                <Text className="font-medium">Application Deadline</Text>
                <Tooltip label="The last day candidates can apply for this position">
                  <FaInfoCircle
                    size={14}
                    className="cursor-help text-gray-400"
                  />
                </Tooltip>
              </div>
            }
            placeholder="Select deadline"
            leftSection={
              <FaCalendar size={18} className="text-primary-color" />
            }
            {...form.getInputProps("applicationDeadline")}
            error={form.errors.applicationDeadline}
            aria-label="Application Deadline"
            size="md"
            radius="md"
            clearable
          />
          <FileInput
            label={
              <div className="flex items-center gap-1">
                <Text className="font-medium">Job Attachment (Optional)</Text>
                <Tooltip label="Upload additional documents like detailed job description">
                  <FaInfoCircle
                    size={14}
                    className="cursor-help text-gray-400"
                  />
                </Tooltip>
              </div>
            }
            placeholder="Upload job description document"
            accept=".pdf,.doc,.docx"
            leftSection={<FaFileAlt size={18} className="text-primary-color" />}
            {...form.getInputProps("jobAttachment")}
            error={form.errors.jobAttachment}
            aria-label="Job Attachment"
            size="md"
            radius="md"
            clearable
          />
        </div>
      </div>

      <Divider my="lg" />

      <div
        className={useThemeClasses(
          "border-t border-gray-200 pt-6",
          "border-t border-dark-4 pt-6",
        )}
      >
        <div className="mb-4 flex flex-wrap items-center justify-between gap-3">
          <div className="flex items-center gap-2">
            <div
              className={useThemeClasses(
                "flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-blue-600",
                "flex h-8 w-8 items-center justify-center rounded-full bg-blue-900/30 text-blue-300",
              )}
            >
              <FaQuestionCircle size={16} />
            </div>
            <Text
              className={useThemeClasses(
                "text-xl font-semibold text-gray-800",
                "text-xl font-semibold text-gray-100",
              )}
            >
              Screening Questions
            </Text>
          </div>
          <Button
            size="sm"
            variant="light"
            color="blue"
            onClick={addQuestion}
            leftSection={<FaQuestionCircle size={14} />}
            radius="md"
            className={useThemeClasses(
              "transition-all duration-200 hover:bg-blue-100",
              "transition-all duration-200 hover:bg-blue-900/30",
            )}
          >
            Add Question
          </Button>
        </div>
        <Text size="sm" c="dimmed" mb="md">
          Add questions to pre-screen candidates during the application process
        </Text>

        <Card
          withBorder
          radius="md"
          className={useThemeClasses("mb-6 bg-gray-50/50", "mb-6 bg-dark-6")}
        >
          <Select
            label={
              <div className="flex items-center gap-1">
                <Text className="font-medium">Predefined Questions</Text>
                <Tooltip label="Choose from common screening questions">
                  <FaInfoCircle
                    size={14}
                    className={useThemeClasses(
                      "cursor-help text-gray-400",
                      "cursor-help text-gray-500",
                    )}
                  />
                </Tooltip>
              </div>
            }
            placeholder="Select a predefined question to add"
            data={predefinedQuestions.map((question) => ({
              value: question,
              label: question,
            }))}
            onChange={(value) => {
              if (value) {
                addPredefinedQuestion(value);
              }
            }}
            size="md"
            radius="md"
            searchable
            clearable
          />
        </Card>

        {questions.length > 0 ? (
          <div className="space-y-4">
            {questions.map((question, index) => (
              <div key={index} className="flex items-end gap-3">
                <TextInput
                  label={
                    <div className="flex items-center gap-1">
                      <Text className="font-medium">Question {index + 1}</Text>
                    </div>
                  }
                  placeholder="Enter a question for candidates to answer"
                  value={question}
                  onChange={(event) =>
                    handleQuestionChange(index, event.currentTarget.value)
                  }
                  className="flex-1"
                  size="md"
                  radius="md"
                />
                <ActionIcon
                  color="red"
                  variant="light"
                  onClick={() => removeQuestion(index)}
                  className="mb-1"
                  size="lg"
                  radius="md"
                >
                  <FaTrash size={16} />
                </ActionIcon>
              </div>
            ))}
          </div>
        ) : (
          <Text
            size="sm"
            c="dimmed"
            className={useThemeClasses("py-4 text-center", "py-4 text-center")}
          >
            No screening questions added yet. Add questions to help identify the
            best candidates.
          </Text>
        )}
      </div>
    </div>
  );
}
