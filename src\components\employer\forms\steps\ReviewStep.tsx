import { useThemeClasses } from "@/design-system/utils/theme-utils";
import { Card, Text } from "@mantine/core";
import {
  FaBriefcase,
  FaClipboardCheck,
  FaDollarSign,
  FaGift,
  FaInfoCircle,
  FaMapMarkerAlt,
} from "react-icons/fa";

interface ReviewStepProps {
  form: any;
}

export default function ReviewStep({ form }: ReviewStepProps) {
  // Use form values or fallback to dummy data for preview
  const formValues = form.values;

  // Dummy data for preview
  const dummyData = {
    jobTitle: "Senior Frontend Developer",
    requiredExperience: "3-5 years",
    location: "Remote",
    jobType: "Full Time",
    jobCategory: "Web Development",
    minSalary: 80000,
    maxSalary: 120000,
    currency: "USD",
    showSalary: true,
    jobDescription:
      "We are looking for a skilled Senior Frontend Developer to join our growing team. You will be responsible for building user interfaces using React, Next.js, and modern CSS frameworks. You'll work closely with designers, backend developers, and product managers to deliver exceptional user experiences.",
    jobRequirements:
      "• 3+ years of experience with React and modern JavaScript\n• Experience with Next.js and TypeScript\n• Strong understanding of responsive design principles\n• Experience with state management libraries (Redux, Zustand, etc.)\n• Excellent problem-solving skills and attention to detail\n• Good communication skills and ability to work in a team",
    benefits: [
      "Health Insurance",
      "Dental Insurance",
      "Remote Work",
      "Flexible Hours",
      "Paid Time Off",
      "Professional Development",
    ],
    jobTags: ["React", "Next.js", "TypeScript", "Frontend", "UI/UX"],
    applicationDeadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
  };

  // Use form values if available, otherwise use dummy data
  const displayData = {
    jobTitle: formValues.jobTitle || dummyData.jobTitle,
    requiredExperience:
      formValues.requiredExperience || dummyData.requiredExperience,
    location: formValues.location || dummyData.location,
    jobType: formValues.jobType || dummyData.jobType,
    jobCategory: formValues.jobCategory || dummyData.jobCategory,
    minSalary: formValues.minSalary || dummyData.minSalary,
    maxSalary: formValues.maxSalary || dummyData.maxSalary,
    currency: formValues.currency || dummyData.currency,
    showSalary:
      formValues.showSalary !== undefined
        ? formValues.showSalary
        : dummyData.showSalary,
    jobDescription: formValues.jobDescription || dummyData.jobDescription,
    jobRequirements: formValues.jobRequirements || dummyData.jobRequirements,
    benefits:
      formValues.benefits?.length > 0
        ? formValues.benefits
        : dummyData.benefits,
    jobTags:
      formValues.jobTags?.length > 0 ? formValues.jobTags : dummyData.jobTags,
    applicationDeadline:
      formValues.applicationDeadline || dummyData.applicationDeadline,
  };

  return (
    <div className="space-y-8">
      <div
        className={useThemeClasses(
          "border-b border-gray-200 pb-4",
          "border-b border-dark-4 pb-4",
        )}
      >
        <div className="flex flex-wrap items-center justify-between gap-2">
          <div className="flex items-center gap-2">
            <div
              className={useThemeClasses(
                "flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-blue-600",
                "flex h-8 w-8 items-center justify-center rounded-full bg-blue-900/30 text-blue-300",
              )}
            >
              <FaClipboardCheck size={16} />
            </div>
            <Text
              className={useThemeClasses(
                "text-xl font-semibold text-gray-800",
                "text-xl font-semibold text-gray-100",
              )}
            >
              Review Your Job Posting
            </Text>
          </div>
        </div>
        <Text size="sm" c="dimmed" mt="xs">
          Please review all information before submitting
        </Text>
      </div>

      {/* Job Preview Card */}
      <div
        className={useThemeClasses(
          "overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm",
          "overflow-hidden rounded-lg border border-dark-4 bg-dark-7 shadow-dark-lg",
        )}
      >
        {/* Header with company info */}
        <div
          className={useThemeClasses(
            "bg-gradient-to-r from-blue-50 to-indigo-50 p-6",
            "bg-gradient-to-r from-dark-6 to-dark-5 p-6",
          )}
        >
          <div className="flex items-start gap-4">
            <div
              className={useThemeClasses(
                "flex h-16 w-16 items-center justify-center rounded-md bg-white shadow-sm",
                "flex h-16 w-16 items-center justify-center rounded-md bg-dark-8 shadow-dark-sm",
              )}
            >
              <FaBriefcase size={24} className="text-primary-color" />
            </div>
            <div className="flex-1">
              <Text
                className={useThemeClasses(
                  "text-2xl font-bold text-gray-800",
                  "text-2xl font-bold text-gray-100",
                )}
              >
                {displayData.jobTitle}
              </Text>
              <Text className="text-primary-color font-medium">
                Your Company Name
              </Text>
              <div className="mt-2 flex flex-wrap gap-3">
                <div
                  className={useThemeClasses(
                    "flex items-center gap-1 rounded-full bg-blue-100 px-3 py-1 text-blue-700",
                    "flex items-center gap-1 rounded-full bg-blue-900/30 px-3 py-1 text-blue-300",
                  )}
                >
                  <FaBriefcase size={14} />
                  <Text size="sm" fw={500}>
                    {displayData.jobType}
                  </Text>
                </div>
                <div
                  className={useThemeClasses(
                    "flex items-center gap-1 rounded-full bg-green-100 px-3 py-1 text-green-700",
                    "flex items-center gap-1 rounded-full bg-green-900/30 px-3 py-1 text-green-300",
                  )}
                >
                  <FaMapMarkerAlt size={14} />
                  <Text size="sm" fw={500}>
                    {displayData.location}
                  </Text>
                </div>
                {displayData.showSalary && (
                  <div
                    className={useThemeClasses(
                      "flex items-center gap-1 rounded-full bg-yellow-100 px-3 py-1 text-yellow-700",
                      "flex items-center gap-1 rounded-full bg-yellow-900/30 px-3 py-1 text-yellow-300",
                    )}
                  >
                    <FaDollarSign size={14} />
                    <Text size="sm" fw={500}>
                      {displayData.minSalary.toLocaleString()} -{" "}
                      {displayData.maxSalary.toLocaleString()}{" "}
                      {displayData.currency}
                    </Text>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Job details */}
        <div className="p-6">
          <div
            className={useThemeClasses(
              "mb-6 grid grid-cols-1 gap-4 rounded-lg bg-gray-50 p-4 sm:grid-cols-2 md:grid-cols-3",
              "mb-6 grid grid-cols-1 gap-4 rounded-lg bg-dark-6 p-4 sm:grid-cols-2 md:grid-cols-3",
            )}
          >
            <div>
              <Text size="sm" c="dimmed">
                Category
              </Text>
              <Text fw={500}>{displayData.jobCategory}</Text>
            </div>
            <div>
              <Text size="sm" c="dimmed">
                Experience
              </Text>
              <Text fw={500}>{displayData.requiredExperience}</Text>
            </div>
            <div>
              <Text size="sm" c="dimmed">
                Application Deadline
              </Text>
              <Text fw={500}>
                {displayData.applicationDeadline instanceof Date
                  ? displayData.applicationDeadline.toLocaleDateString(
                      "en-US",
                      {
                        year: "numeric",
                        month: "short",
                        day: "numeric",
                      },
                    )
                  : "30 days from posting"}
              </Text>
            </div>
          </div>

          <div className="mb-6">
            <Text
              className={useThemeClasses(
                "mb-2 text-lg font-semibold text-gray-800",
                "mb-2 text-lg font-semibold text-gray-100",
              )}
            >
              Job Description
            </Text>
            <Text
              size="sm"
              className={useThemeClasses(
                "whitespace-pre-line text-gray-700",
                "whitespace-pre-line text-gray-300",
              )}
            >
              {displayData.jobDescription}
            </Text>
          </div>

          <div className="mb-6">
            <Text
              className={useThemeClasses(
                "mb-2 text-lg font-semibold text-gray-800",
                "mb-2 text-lg font-semibold text-gray-100",
              )}
            >
              Requirements
            </Text>
            <Text
              size="sm"
              className={useThemeClasses(
                "whitespace-pre-line text-gray-700",
                "whitespace-pre-line text-gray-300",
              )}
            >
              {displayData.jobRequirements}
            </Text>
          </div>

          <div className="mb-6">
            <Text
              className={useThemeClasses(
                "mb-2 text-lg font-semibold text-gray-800",
                "mb-2 text-lg font-semibold text-gray-100",
              )}
            >
              Benefits & Perks
            </Text>
            <div className="grid grid-cols-1 gap-2 sm:grid-cols-2 md:grid-cols-3">
              {displayData.benefits.map((benefit: string, index: number) => (
                <div
                  key={index}
                  className={useThemeClasses(
                    "flex items-center gap-2 rounded-md bg-blue-50 p-2",
                    "flex items-center gap-2 rounded-md bg-blue-900/30 p-2",
                  )}
                >
                  <FaGift
                    size={14}
                    className={useThemeClasses(
                      "text-blue-500",
                      "text-blue-300",
                    )}
                  />
                  <Text size="sm">{benefit}</Text>
                </div>
              ))}
            </div>
          </div>

          <div>
            <Text
              className={useThemeClasses(
                "mb-2 text-lg font-semibold text-gray-800",
                "mb-2 text-lg font-semibold text-gray-100",
              )}
            >
              Skills & Tags
            </Text>
            <div className="flex flex-wrap gap-2">
              {displayData.jobTags.map((tag: string, index: number) => (
                <div
                  key={index}
                  className={useThemeClasses(
                    "rounded-full bg-gray-100 px-3 py-1 text-gray-700",
                    "rounded-full bg-dark-5 px-3 py-1 text-gray-300",
                  )}
                >
                  <Text size="sm">{tag}</Text>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Submit notice */}
      <Card
        withBorder
        radius="md"
        className={useThemeClasses(
          "border-blue-200 bg-blue-50/50",
          "border-blue-900/50 bg-blue-900/20",
        )}
      >
        <div className="flex gap-3">
          <div
            className={useThemeClasses(
              "flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-blue-100 text-blue-600",
              "flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-blue-900/50 text-blue-300",
            )}
          >
            <FaInfoCircle size={20} />
          </div>
          <div>
            <Text
              size="sm"
              className={useThemeClasses(
                "mb-1 font-medium text-blue-600",
                "mb-1 font-medium text-blue-300",
              )}
            >
              Ready to submit?
            </Text>
            <Text size="sm" c="dimmed">
              Once submitted, your job posting will be reviewed by our team and
              published shortly. Make sure all the information is accurate and
              complete.
            </Text>
          </div>
        </div>
      </Card>

      <Text className="text-center text-sm" c="dimmed">
        By submitting this job posting, you agree to our{" "}
        <span className="text-primary-color cursor-pointer hover:underline">
          terms and conditions
        </span>
        .
      </Text>
    </div>
  );
}
