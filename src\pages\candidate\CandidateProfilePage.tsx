import { <PERSON><PERSON><PERSON><PERSON>, PageHeading } from "@/design-system/components";
import { useThemeClasses } from "@/design-system/utils/theme-utils";
import {
  ActionIcon,
  Avatar,
  Badge,
  Button,
  Card,
  Divider,
  FileInput,
  Grid,
  Group,
  Modal,
  Progress,
  RingProgress,
  Select,
  Tabs,
  Text,
  TextInput,
  Textarea,
  Title,
  Tooltip,
  useMantineColorScheme,
} from "@mantine/core";
import { useForm } from "@mantine/form";
import { useDisclosure } from "@mantine/hooks";
import { notifications } from "@mantine/notifications";
import { useState } from "react";
import {
  FaBriefcase,
  FaBuilding,
  FaCamera,
  FaCheck,
  FaEnvelope,
  FaGithub,
  FaGlobe,
  FaGraduationCap,
  FaLinkedin,
  FaMapMarkerAlt,
  FaPhone,
  FaPlus,
  FaSave,
  FaStar,
  FaTrash,
  FaUpload,
  FaUser,
} from "react-icons/fa";
import { Link } from "react-router";

// Types for form data
interface EducationItem {
  degree: string;
  institution: string;
  year: string;
}

interface ExperienceItem {
  position: string;
  company: string;
  duration: string;
  description: string;
}

export default function CandidateProfilePage() {
  const [activeTab, setActiveTab] = useState<string | null>("personal");
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  // Modal states
  const [skillModalOpened, { open: openSkillModal, close: closeSkillModal }] =
    useDisclosure(false);
  const [
    experienceModalOpened,
    { open: openExperienceModal, close: closeExperienceModal },
  ] = useDisclosure(false);
  const [
    educationModalOpened,
    { open: openEducationModal, close: closeEducationModal },
  ] = useDisclosure(false);
  const [
    profileImageModalOpened,
    { open: openProfileImageModal, close: closeProfileImageModal },
  ] = useDisclosure(false);

  // New item states
  const [newSkill, setNewSkill] = useState("");
  const [newExperience, setNewExperience] = useState<ExperienceItem>({
    position: "",
    company: "",
    duration: "",
    description: "",
  });
  const [newEducation, setNewEducation] = useState<EducationItem>({
    degree: "",
    institution: "",
    year: "",
  });

  // Profile image state
  const [profileImage, setProfileImage] = useState<File | null>(null);
  const [profileImagePreview, setProfileImagePreview] = useState<string | null>(
    null,
  );

  console.log(profileImage);

  // Calculate profile completion percentage
  const profileCompletionPercentage = 75;

  const form = useForm({
    initialValues: {
      firstName: "John",
      lastName: "Doe",
      email: "<EMAIL>",
      phone: "************",
      location: "New York, NY",
      title: "Senior Frontend Developer",
      bio: "Experienced frontend developer with 5+ years of experience in React, TypeScript, and modern web technologies.",
      linkedin: "https://linkedin.com/in/johndoe",
      github: "https://github.com/johndoe",
      portfolio: "https://johndoe.dev",
      skills: ["React", "TypeScript", "JavaScript", "HTML/CSS", "Node.js"],
      education: [
        {
          degree: "Bachelor of Science in Computer Science",
          institution: "University of Technology",
          year: "2018",
        },
      ],
      experience: [
        {
          position: "Senior Frontend Developer",
          company: "Tech Solutions Inc.",
          duration: "2020 - Present",
          description:
            "Leading frontend development for enterprise applications",
        },
        {
          position: "Frontend Developer",
          company: "Digital Innovations",
          duration: "2018 - 2020",
          description: "Developed responsive web applications using React",
        },
      ],
    },
  });

  // Handle adding a new skill
  const handleAddSkill = () => {
    if (newSkill.trim() !== "") {
      form.setFieldValue("skills", [...form.values.skills, newSkill]);
      setNewSkill("");
      closeSkillModal();
      notifications.show({
        title: "Skill Added",
        message: `${newSkill} has been added to your skills`,
        color: "green",
      });
    }
  };

  // Handle adding a new experience
  const handleAddExperience = () => {
    if (
      newExperience.position.trim() !== "" &&
      newExperience.company.trim() !== "" &&
      newExperience.duration.trim() !== ""
    ) {
      form.setFieldValue("experience", [
        newExperience,
        ...form.values.experience,
      ]);
      setNewExperience({
        position: "",
        company: "",
        duration: "",
        description: "",
      });
      closeExperienceModal();
      notifications.show({
        title: "Experience Added",
        message: `${newExperience.position} at ${newExperience.company} has been added`,
        color: "green",
      });
    }
  };

  // Handle adding a new education
  const handleAddEducation = () => {
    if (
      newEducation.degree.trim() !== "" &&
      newEducation.institution.trim() !== "" &&
      newEducation.year.trim() !== ""
    ) {
      form.setFieldValue("education", [newEducation, ...form.values.education]);
      setNewEducation({
        degree: "",
        institution: "",
        year: "",
      });
      closeEducationModal();
      notifications.show({
        title: "Education Added",
        message: `${newEducation.degree} from ${newEducation.institution} has been added`,
        color: "green",
      });
    }
  };

  // Handle removing a skill
  const handleRemoveSkill = (index: number) => {
    const updatedSkills = [...form.values.skills];
    const removedSkill = updatedSkills[index];
    updatedSkills.splice(index, 1);
    form.setFieldValue("skills", updatedSkills);
    notifications.show({
      title: "Skill Removed",
      message: `${removedSkill} has been removed from your skills`,
      color: "red",
    });
  };

  // Handle removing an experience
  const handleRemoveExperience = (index: number) => {
    const updatedExperience = [...form.values.experience];
    const removedExp = updatedExperience[index];
    updatedExperience.splice(index, 1);
    form.setFieldValue("experience", updatedExperience);
    notifications.show({
      title: "Experience Removed",
      message: `${removedExp.position} at ${removedExp.company} has been removed`,
      color: "red",
    });
  };

  // Handle removing an education
  const handleRemoveEducation = (index: number) => {
    const updatedEducation = [...form.values.education];
    const removedEdu = updatedEducation[index];
    updatedEducation.splice(index, 1);
    form.setFieldValue("education", updatedEducation);
    notifications.show({
      title: "Education Removed",
      message: `${removedEdu.degree} from ${removedEdu.institution} has been removed`,
      color: "red",
    });
  };

  // Handle profile image upload
  const handleProfileImageChange = (file: File | null) => {
    if (file) {
      setProfileImage(file);
      const imageUrl = URL.createObjectURL(file);
      setProfileImagePreview(imageUrl);
      closeProfileImageModal();

      notifications.show({
        title: "Profile Image Updated",
        message: "Your profile image has been successfully updated",
        color: "green",
      });
    }
  };

  // Handle profile image removal
  const handleRemoveProfileImage = () => {
    setProfileImage(null);
    setProfileImagePreview(null);

    notifications.show({
      title: "Profile Image Removed",
      message: "Your profile image has been removed",
      color: "red",
    });
  };

  const handleSubmit = (values: typeof form.values) => {
    console.log(values);
    // Save profile data
    notifications.show({
      title: "Profile Updated",
      message: "Your profile has been successfully updated",
      color: "green",
      icon: <FaCheck />,
    });
  };

  return (
    <PageContainer
      breadcrumbItems={[{ title: "Home", href: "/" }, { title: "Profile" }]}
      variant="candidate"
    >
      <PageHeading
        title="My Profile"
        subtitle="Manage your personal information and preferences"
        variant="candidate"
      />
      {/* Hero Section with Profile Overview */}
      <Card withBorder radius="md" className="mb-8 overflow-hidden">
        <div className="relative">
          {/* Background gradient */}
          <div
            className={useThemeClasses(
              "absolute inset-0 bg-gradient-to-r from-blue-50 to-indigo-50",
              "absolute inset-0 bg-gradient-to-r from-blue-900/20 to-indigo-900/20",
            )}
          ></div>

          <div className="relative p-6 md:p-8">
            <Grid>
              <Grid.Col span={{ base: 12, sm: 8 }}>
                <Group align="flex-start" className="mb-4">
                  <div className="relative">
                    <Avatar
                      size={120}
                      radius="md"
                      color="blue"
                      className={useThemeClasses(
                        "border-4 border-white shadow-md",
                        "border-4 border-dark-6 shadow-md",
                      )}
                      src={profileImagePreview}
                    >
                      {!profileImagePreview &&
                        form.values.firstName.charAt(0) +
                          form.values.lastName.charAt(0)}
                    </Avatar>
                    <div className="absolute -right-2 -bottom-2 flex gap-1">
                      <Tooltip label="Change profile picture">
                        <ActionIcon
                          className={useThemeClasses(
                            "border border-gray-200 bg-white shadow-sm",
                            "border border-gray-700 bg-dark-6 shadow-sm",
                          )}
                          radius="xl"
                          variant="filled"
                          size="md"
                          color="blue"
                          onClick={openProfileImageModal}
                        >
                          <FaCamera size={14} />
                        </ActionIcon>
                      </Tooltip>
                      {profileImagePreview && (
                        <Tooltip label="Remove profile picture">
                          <ActionIcon
                            className={useThemeClasses(
                              "border border-gray-200 bg-white shadow-sm",
                              "border border-gray-700 bg-dark-6 shadow-sm",
                            )}
                            radius="xl"
                            variant="filled"
                            size="md"
                            color="red"
                            onClick={handleRemoveProfileImage}
                          >
                            <FaTrash size={14} />
                          </ActionIcon>
                        </Tooltip>
                      )}
                    </div>
                  </div>

                  <div>
                    <Title order={1} className="text-2xl font-bold">
                      {form.values.firstName} {form.values.lastName}
                    </Title>

                    <Text
                      size="lg"
                      className={useThemeClasses(
                        "mb-2 text-gray-700",
                        "mb-2 text-gray-300",
                      )}
                    >
                      {form.values.title}
                    </Text>

                    <Group gap="lg" className="mt-3">
                      <Group gap="xs">
                        <FaEnvelope className="text-blue-500" />
                        <Text size="sm">{form.values.email}</Text>
                      </Group>

                      <Group gap="xs">
                        <FaPhone className="text-blue-500" />
                        <Text size="sm">{form.values.phone}</Text>
                      </Group>

                      <Group gap="xs">
                        <FaMapMarkerAlt className="text-blue-500" />
                        <Text size="sm">{form.values.location}</Text>
                      </Group>
                    </Group>
                  </div>
                </Group>
              </Grid.Col>

              <Grid.Col span={{ base: 12, sm: 4 }}>
                <Card
                  withBorder
                  radius="md"
                  className={useThemeClasses(
                    "bg-white shadow-sm",
                    "bg-dark-7 shadow-sm border-dark-4",
                  )}
                >
                  <Text fw={700} ta="center" className="mb-2">
                    Profile Completion
                  </Text>
                  <Group justify="center" className="mb-2">
                    <RingProgress
                      size={80}
                      thickness={8}
                      roundCaps
                      sections={[
                        { value: profileCompletionPercentage, color: "blue" },
                      ]}
                      label={
                        <Text ta="center" size="sm" fw={700}>
                          {profileCompletionPercentage}%
                        </Text>
                      }
                    />
                  </Group>
                  <Text size="sm" c="dimmed" ta="center">
                    Complete your profile to increase visibility to employers
                  </Text>
                </Card>
              </Grid.Col>
            </Grid>

            {/* Quick Action Buttons */}
            <Group className="mt-4">
              <Group ml="auto" gap="xs">
                <Tooltip label="LinkedIn Profile">
                  <ActionIcon
                    component={Link}
                    to={form.values.linkedin}
                    target="_blank"
                    variant="light"
                    color="blue"
                    size="lg"
                  >
                    <FaLinkedin size={20} />
                  </ActionIcon>
                </Tooltip>

                <Tooltip label="GitHub Profile">
                  <ActionIcon
                    component={Link}
                    to={form.values.github}
                    target="_blank"
                    variant="light"
                    color="dark"
                    size="lg"
                  >
                    <FaGithub size={20} />
                  </ActionIcon>
                </Tooltip>

                <Tooltip label="Portfolio">
                  <ActionIcon
                    component={Link}
                    to={form.values.portfolio}
                    target="_blank"
                    variant="light"
                    color="indigo"
                    size="lg"
                  >
                    <FaGlobe size={20} />
                  </ActionIcon>
                </Tooltip>
              </Group>
            </Group>
          </div>
        </div>
      </Card>

      {/* Tabbed Content */}
      <form onSubmit={form.onSubmit(handleSubmit)}>
        <Tabs
          value={activeTab}
          onChange={setActiveTab}
          className="mb-6"
          styles={{
            tab: {
              color: isDark
                ? "var(--mantine-color-gray-4) !important"
                : undefined,
              "&[data-active]": {
                color: isDark
                  ? "var(--mantine-color-blue-4) !important"
                  : undefined,
                borderColor: isDark
                  ? "var(--mantine-color-blue-4) !important"
                  : undefined,
              },
            },
          }}
        >
          <Tabs.List grow>
            <Tabs.Tab value="personal" leftSection={<FaUser size={16} />}>
              Personal Info
            </Tabs.Tab>
            <Tabs.Tab value="skills" leftSection={<FaStar size={16} />}>
              Skills
            </Tabs.Tab>
            <Tabs.Tab
              value="experience"
              leftSection={<FaBriefcase size={16} />}
            >
              Experience
            </Tabs.Tab>
            <Tabs.Tab
              value="education"
              leftSection={<FaGraduationCap size={16} />}
            >
              Education
            </Tabs.Tab>
          </Tabs.List>

          {/* Personal Information Tab */}
          <Tabs.Panel value="personal" pt="md">
            <Card withBorder radius="md" className="p-6 shadow-sm">
              <Title
                order={3}
                className={useThemeClasses(
                  "mb-4 text-xl font-semibold text-blue-800",
                  "mb-4 text-xl font-semibold text-blue-400",
                )}
              >
                Personal Information
              </Title>

              <Grid gutter="md">
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <TextInput
                    label="First Name"
                    placeholder="Your first name"
                    required
                    leftSection={<FaUser size={16} className="text-gray-500" />}
                    {...form.getInputProps("firstName")}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <TextInput
                    label="Last Name"
                    placeholder="Your last name"
                    required
                    leftSection={<FaUser size={16} className="text-gray-500" />}
                    {...form.getInputProps("lastName")}
                  />
                </Grid.Col>
                <Grid.Col span={12}>
                  <TextInput
                    label="Email"
                    placeholder="Your email"
                    required
                    leftSection={
                      <FaEnvelope size={16} className="text-gray-500" />
                    }
                    {...form.getInputProps("email")}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <TextInput
                    label="Phone"
                    placeholder="Your phone number"
                    leftSection={
                      <FaPhone size={16} className="text-gray-500" />
                    }
                    {...form.getInputProps("phone")}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <TextInput
                    label="Location"
                    placeholder="City, State"
                    leftSection={
                      <FaMapMarkerAlt size={16} className="text-gray-500" />
                    }
                    {...form.getInputProps("location")}
                  />
                </Grid.Col>
                <Grid.Col span={12}>
                  <TextInput
                    label="Professional Title"
                    placeholder="e.g. Senior Frontend Developer"
                    leftSection={
                      <FaBriefcase size={16} className="text-gray-500" />
                    }
                    {...form.getInputProps("title")}
                  />
                </Grid.Col>
                <Grid.Col span={12}>
                  <Textarea
                    label="Bio"
                    placeholder="Tell employers about yourself"
                    minRows={4}
                    {...form.getInputProps("bio")}
                  />
                </Grid.Col>
              </Grid>

              <Divider my="lg" />

              <Title
                order={3}
                className={useThemeClasses(
                  "mb-4 text-xl font-semibold text-blue-800",
                  "mb-4 text-xl font-semibold text-blue-400",
                )}
              >
                Social Links
              </Title>

              <Grid gutter="md">
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <TextInput
                    label="LinkedIn"
                    placeholder="https://linkedin.com/in/username"
                    leftSection={
                      <FaLinkedin size={16} className="text-blue-500" />
                    }
                    {...form.getInputProps("linkedin")}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <TextInput
                    label="GitHub"
                    placeholder="https://github.com/username"
                    leftSection={
                      <FaGithub size={16} className="text-gray-700" />
                    }
                    {...form.getInputProps("github")}
                  />
                </Grid.Col>
                <Grid.Col span={12}>
                  <TextInput
                    label="Portfolio Website"
                    placeholder="https://yourportfolio.com"
                    leftSection={
                      <FaGlobe size={16} className="text-indigo-500" />
                    }
                    {...form.getInputProps("portfolio")}
                  />
                </Grid.Col>
              </Grid>
            </Card>
          </Tabs.Panel>

          {/* Skills Tab */}
          <Tabs.Panel value="skills" pt="md">
            <Card withBorder radius="md" className="p-6 shadow-sm">
              <Title
                order={3}
                className={useThemeClasses(
                  "mb-4 text-xl font-semibold text-blue-800",
                  "mb-4 text-xl font-semibold text-blue-400",
                )}
              >
                Skills & Expertise
              </Title>

              <Text
                className={useThemeClasses(
                  "mb-4 text-gray-600",
                  "mb-4 text-gray-400",
                )}
              >
                Add your key skills to help employers find you. Highlight your
                strongest skills first.
              </Text>

              {form.values.skills.map((skill, index) => (
                <div key={index} className="mb-4">
                  <Group justify="space-between" className="mb-1">
                    <Group>
                      <Text fw={500}>{skill}</Text>
                      <Badge
                        color={
                          index < 2 ? "green" : index < 4 ? "blue" : "cyan"
                        }
                      >
                        {index < 2
                          ? "Expert"
                          : index < 4
                            ? "Advanced"
                            : "Intermediate"}
                      </Badge>
                    </Group>
                    <ActionIcon
                      variant="subtle"
                      color="red"
                      size="sm"
                      onClick={() => handleRemoveSkill(index)}
                    >
                      <FaTrash size={14} />
                    </ActionIcon>
                  </Group>
                  <Progress
                    value={95 - index * 10}
                    color={index < 2 ? "green" : index < 4 ? "blue" : "cyan"}
                    size="md"
                    radius="xl"
                    className="mb-1"
                  />
                </div>
              ))}

              <Group mt="xl">
                <Button
                  leftSection={<FaPlus size={16} />}
                  variant="light"
                  onClick={openSkillModal}
                  fullWidth
                >
                  Add New Skill
                </Button>
              </Group>

              {/* Skill Modal */}
              <Modal
                opened={skillModalOpened}
                onClose={closeSkillModal}
                title="Add New Skill"
                centered
              >
                <TextInput
                  label="Skill Name"
                  placeholder="e.g. React, JavaScript, UI Design"
                  required
                  value={newSkill}
                  onChange={(e) => setNewSkill(e.target.value)}
                  className="mb-4"
                />
                <Select
                  label="Proficiency Level"
                  placeholder="Select your proficiency level"
                  data={[
                    { value: "expert", label: "Expert" },
                    { value: "advanced", label: "Advanced" },
                    { value: "intermediate", label: "Intermediate" },
                    { value: "beginner", label: "Beginner" },
                  ]}
                  className="mb-6"
                />
                <Group justify="flex-end">
                  <Button variant="default" onClick={closeSkillModal}>
                    Cancel
                  </Button>
                  <Button onClick={handleAddSkill}>Add Skill</Button>
                </Group>
              </Modal>
            </Card>
          </Tabs.Panel>

          {/* Experience Tab */}
          <Tabs.Panel value="experience" pt="md">
            <Card withBorder radius="md" className="p-6 shadow-sm">
              <Title
                order={3}
                className={useThemeClasses(
                  "mb-4 text-xl font-semibold text-blue-800",
                  "mb-4 text-xl font-semibold text-blue-400",
                )}
              >
                Work Experience
              </Title>

              <div
                className={useThemeClasses(
                  "relative border-l-2 border-blue-200 pl-8",
                  "relative border-l-2 border-blue-800 pl-8",
                )}
              >
                {form.values.experience.map((exp, index) => (
                  <div key={index} className="relative mb-8">
                    <div
                      className={useThemeClasses(
                        "absolute -left-[41px] rounded-full border-2 border-blue-400 bg-white p-1",
                        "absolute -left-[41px] rounded-full border-2 border-blue-600 bg-dark-7 p-1",
                      )}
                    >
                      <FaBriefcase size={20} className="text-blue-500" />
                    </div>

                    <Group justify="space-between" className="mb-2">
                      <div>
                        <Title order={4} className="mb-1 text-lg font-semibold">
                          {exp.position}
                        </Title>

                        <Group className="mb-2">
                          <Group gap="xs">
                            <FaBuilding className="text-gray-500" size={14} />
                            <Text fw={500} c="dimmed">
                              {exp.company}
                            </Text>
                          </Group>
                          <Divider orientation="vertical" />
                          <Text size="sm" c="dimmed">
                            {exp.duration}
                          </Text>
                        </Group>
                      </div>

                      <Group>
                        <ActionIcon
                          variant="subtle"
                          color="red"
                          onClick={() => handleRemoveExperience(index)}
                        >
                          <FaTrash size={16} />
                        </ActionIcon>
                      </Group>
                    </Group>

                    <Text
                      className={useThemeClasses(
                        "text-gray-700",
                        "text-gray-300",
                      )}
                    >
                      {exp.description}
                    </Text>
                  </div>
                ))}
              </div>

              <Button
                leftSection={<FaPlus size={16} />}
                variant="outline"
                color="blue"
                className="mt-4"
                onClick={openExperienceModal}
              >
                Add Experience
              </Button>

              {/* Experience Modal */}
              <Modal
                opened={experienceModalOpened}
                onClose={closeExperienceModal}
                title="Add Work Experience"
                centered
                size="lg"
              >
                <Grid gutter="md">
                  <Grid.Col span={12}>
                    <TextInput
                      label="Position"
                      placeholder="e.g. Senior Frontend Developer"
                      required
                      value={newExperience.position}
                      onChange={(e) =>
                        setNewExperience({
                          ...newExperience,
                          position: e.target.value,
                        })
                      }
                    />
                  </Grid.Col>
                  <Grid.Col span={12}>
                    <TextInput
                      label="Company"
                      placeholder="e.g. Tech Solutions Inc."
                      required
                      value={newExperience.company}
                      onChange={(e) =>
                        setNewExperience({
                          ...newExperience,
                          company: e.target.value,
                        })
                      }
                    />
                  </Grid.Col>
                  <Grid.Col span={12}>
                    <TextInput
                      label="Duration"
                      placeholder="e.g. 2020 - Present"
                      required
                      value={newExperience.duration}
                      onChange={(e) =>
                        setNewExperience({
                          ...newExperience,
                          duration: e.target.value,
                        })
                      }
                    />
                  </Grid.Col>
                  <Grid.Col span={12}>
                    <Textarea
                      label="Description"
                      placeholder="Describe your responsibilities and achievements"
                      minRows={3}
                      value={newExperience.description}
                      onChange={(e) =>
                        setNewExperience({
                          ...newExperience,
                          description: e.target.value,
                        })
                      }
                    />
                  </Grid.Col>
                </Grid>
                <Group justify="flex-end" mt="xl">
                  <Button variant="default" onClick={closeExperienceModal}>
                    Cancel
                  </Button>
                  <Button onClick={handleAddExperience}>Add Experience</Button>
                </Group>
              </Modal>
            </Card>
          </Tabs.Panel>

          {/* Education Tab */}
          <Tabs.Panel value="education" pt="md">
            <Card withBorder radius="md" className="p-6 shadow-sm">
              <Title
                order={3}
                className={useThemeClasses(
                  "mb-4 text-xl font-semibold text-blue-800",
                  "mb-4 text-xl font-semibold text-blue-400",
                )}
              >
                Education
              </Title>

              <div
                className={useThemeClasses(
                  "relative border-l-2 border-indigo-200 pl-8",
                  "relative border-l-2 border-indigo-800 pl-8",
                )}
              >
                {form.values.education.map((edu, index) => (
                  <div key={index} className="relative mb-8">
                    <div
                      className={useThemeClasses(
                        "absolute -left-[41px] rounded-full border-2 border-indigo-400 bg-white p-1",
                        "absolute -left-[41px] rounded-full border-2 border-indigo-600 bg-dark-7 p-1",
                      )}
                    >
                      <FaGraduationCap size={20} className="text-indigo-500" />
                    </div>

                    <Group justify="space-between" className="mb-2">
                      <div>
                        <Title order={4} className="mb-1 text-lg font-semibold">
                          {edu.degree}
                        </Title>

                        <Group className="mb-2">
                          <Group gap="xs">
                            <FaBuilding className="text-gray-500" size={14} />
                            <Text fw={500} c="dimmed">
                              {edu.institution}
                            </Text>
                          </Group>
                          <Divider orientation="vertical" />
                          <Text size="sm" c="dimmed">
                            {edu.year}
                          </Text>
                        </Group>
                      </div>

                      <Group>
                        <ActionIcon
                          variant="subtle"
                          color="red"
                          onClick={() => handleRemoveEducation(index)}
                        >
                          <FaTrash size={16} />
                        </ActionIcon>
                      </Group>
                    </Group>
                  </div>
                ))}
              </div>

              <Button
                leftSection={<FaPlus size={16} />}
                variant="outline"
                color="blue"
                className="mt-4"
                onClick={openEducationModal}
              >
                Add Education
              </Button>

              {/* Education Modal */}
              <Modal
                opened={educationModalOpened}
                onClose={closeEducationModal}
                title="Add Education"
                centered
                size="lg"
              >
                <Grid gutter="md">
                  <Grid.Col span={12}>
                    <TextInput
                      label="Degree"
                      placeholder="e.g. Bachelor of Science in Computer Science"
                      required
                      value={newEducation.degree}
                      onChange={(e) =>
                        setNewEducation({
                          ...newEducation,
                          degree: e.target.value,
                        })
                      }
                    />
                  </Grid.Col>
                  <Grid.Col span={12}>
                    <TextInput
                      label="Institution"
                      placeholder="e.g. University of Technology"
                      required
                      value={newEducation.institution}
                      onChange={(e) =>
                        setNewEducation({
                          ...newEducation,
                          institution: e.target.value,
                        })
                      }
                    />
                  </Grid.Col>
                  <Grid.Col span={12}>
                    <TextInput
                      label="Year"
                      placeholder="e.g. 2018"
                      required
                      value={newEducation.year}
                      onChange={(e) =>
                        setNewEducation({
                          ...newEducation,
                          year: e.target.value,
                        })
                      }
                    />
                  </Grid.Col>
                </Grid>
                <Group justify="flex-end" mt="xl">
                  <Button variant="default" onClick={closeEducationModal}>
                    Cancel
                  </Button>
                  <Button onClick={handleAddEducation}>Add Education</Button>
                </Group>
              </Modal>
            </Card>
          </Tabs.Panel>
        </Tabs>

        <Group justify="flex-end" mt="xl">
          <Button type="submit" size="md" leftSection={<FaSave size={16} />}>
            Save All Changes
          </Button>
        </Group>
      </form>

      {/* Profile Image Modal */}
      <Modal
        opened={profileImageModalOpened}
        onClose={closeProfileImageModal}
        title="Update Profile Picture"
        centered
      >
        <div className="mb-6 flex flex-col items-center">
          <Avatar
            size={150}
            radius="md"
            color="blue"
            className="mb-4 border-4 border-white shadow-md"
            src={profileImagePreview}
          >
            {!profileImagePreview &&
              form.values.firstName.charAt(0) + form.values.lastName.charAt(0)}
          </Avatar>

          <FileInput
            label="Upload new profile picture"
            placeholder="Click to select an image"
            accept="image/png,image/jpeg,image/jpg"
            className="mb-4 w-full"
            onChange={handleProfileImageChange}
            leftSection={<FaUpload size={16} />}
            clearable
          />

          <Text size="sm" c="dimmed" className="mb-4 text-center">
            Recommended: Square image, at least 400x400 pixels
          </Text>

          <Group justify="center" className="w-full">
            <Button variant="default" onClick={closeProfileImageModal}>
              Cancel
            </Button>
            {profileImagePreview && (
              <Button color="red" onClick={handleRemoveProfileImage}>
                Remove Photo
              </Button>
            )}
          </Group>
        </div>
      </Modal>
    </PageContainer>
  );
}
