import { SectionHeading } from "@/design-system/components";
import { Bar<PERSON><PERSON> } from "@mantine/charts";
import { Card } from "@mantine/core";

const candidateProgressData = [
  { label: "Hired", value: 30 },
  { label: "Interviewed", value: 20 },
  { label: "Applied", value: 70 },
];

export default function CandidateProgress() {
  return (
    <Card withBorder shadow="sm" padding="lg" radius="md">
      <SectionHeading variant="employer">Candidate Progress</SectionHeading>
      <BarChart
        data={candidateProgressData}
        dataKey="label"
        series={[{ name: "value", color: "blue" }]}
        withTooltip
        withLegend
        h={300} // Fixed height
        w="100%" // Full width
      />
    </Card>
  );
}
