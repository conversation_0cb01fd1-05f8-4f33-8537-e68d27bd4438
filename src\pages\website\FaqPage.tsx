import {
  Accordion,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Tabs,
  Text,
  TextInput,
  useMantineColorScheme,
} from "@mantine/core";
import { FaSearch } from "react-icons/fa";
import { Link } from "react-router";

// FAQ categories and questions
const faqCategories = [
  {
    value: "general",
    label: "General",
    icon: "🔍",
    questions: [
      {
        id: "what-is-jobnest",
        question: "What is JobNest?",
        answer:
          "JobNest is a comprehensive job platform that connects employers with job seekers. Our platform offers tools for posting jobs, searching for opportunities, and managing the hiring process efficiently.",
      },
      {
        id: "is-jobnest-free",
        question: "Is JobNest free to use?",
        answer:
          "JobNest offers both free and premium plans. Job seekers can create profiles, search for jobs, and apply to positions for free. Employers can access basic posting features for free, with premium plans available for advanced features and increased visibility.",
      },
      {
        id: "account-types",
        question: "What types of accounts can I create?",
        answer:
          "JobNest offers three types of accounts: Job Seeker accounts for those looking for work, Employer accounts for companies posting jobs, and Admin accounts for platform management.",
      },
    ],
  },
  {
    value: "employers",
    label: "For Employers",
    icon: "💼",
    questions: [
      {
        id: "post-job",
        question: "How do I post a job?",
        answer:
          "To post a job, log in to your employer account, navigate to the 'Create Job' section, fill out the job details form including title, description, requirements, and compensation, then submit. Your job will be live after review.",
      },
      {
        id: "manage-applications",
        question: "How can I manage applications?",
        answer:
          "All applications are accessible through your employer dashboard. You can view candidate profiles, resumes, and application details. You can also sort, filter, and manage the status of applications through our intuitive interface.",
      },
      {
        id: "premium-features",
        question: "What premium features are available for employers?",
        answer:
          "Premium employer features include featured job listings, advanced candidate filtering, bulk messaging, applicant tracking system integration, and detailed analytics on your job postings' performance.",
      },
    ],
  },
  {
    value: "jobseekers",
    label: "For Job Seekers",
    icon: "👨‍💼",
    questions: [
      {
        id: "create-profile",
        question: "How do I create an effective profile?",
        answer:
          "To create an effective profile, include a professional photo, detailed work history, education, skills, and a compelling bio. Complete all sections and keep your information up-to-date. Use keywords relevant to your desired positions to improve visibility to employers.",
      },
      {
        id: "apply-jobs",
        question: "How do I apply for jobs?",
        answer:
          "To apply for jobs, search for positions using filters like location, industry, and job type. When you find a suitable position, click 'Apply Now', review your profile information, attach any requested documents, and submit your application.",
      },
      {
        id: "track-applications",
        question: "Can I track my job applications?",
        answer:
          "Yes, you can track all your job applications through your dashboard. You'll see the status of each application, receive notifications when employers view your profile, and get updates when your application status changes.",
      },
    ],
  },
  {
    value: "technical",
    label: "Technical",
    icon: "🔧",
    questions: [
      {
        id: "browser-compatibility",
        question: "Which browsers are supported?",
        answer:
          "JobNest supports all modern browsers including Chrome, Firefox, Safari, and Edge. For the best experience, we recommend using the latest version of these browsers.",
      },
      {
        id: "mobile-app",
        question: "Is there a mobile app available?",
        answer:
          "Yes, JobNest offers mobile apps for both iOS and Android devices. You can download them from the App Store or Google Play Store. Our mobile website is also fully responsive for use on any device.",
      },
      {
        id: "data-security",
        question: "How is my data protected?",
        answer:
          "We take data security seriously. All data is encrypted using industry-standard protocols, and we never share your personal information with third parties without your consent. We comply with all relevant data protection regulations.",
      },
    ],
  },
];

export default function FaqPage() {
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  return (
    <div className={`py-20 ${isDark ? "bg-gray-900" : "bg-gray-50"}`}>
      <div className="container">
        <div className="mx-auto mb-16 max-w-3xl text-center">
          <Badge color="primary_color" size="lg" radius="sm" className="mb-4">
            Support
          </Badge>
          <h1
            className={`mb-6 text-4xl font-bold tracking-tight sm:text-5xl ${isDark ? "text-white" : ""}`}
          >
            Frequently Asked Questions
          </h1>
          <p
            className={`mb-8 text-lg ${isDark ? "text-gray-300" : "text-gray-600"}`}
          >
            Find answers to common questions about JobNest. If you can&apos;t
            find what you&apos;re looking for, feel free to contact our support
            team.
          </p>

          {/* Search bar */}
          <div className="mx-auto mb-12 max-w-xl">
            <TextInput
              placeholder="Search for answers..."
              size="md"
              radius="md"
              leftSection={
                <FaSearch size={16} className={isDark ? "text-gray-400" : ""} />
              }
              rightSectionWidth={42}
              rightSection={<Button variant="subtle">Search</Button>}
              classNames={{
                input: isDark
                  ? "border-gray-700 bg-gray-800 text-gray-200"
                  : "",
              }}
            />
          </div>
        </div>

        <Card
          withBorder
          radius="md"
          className={`mx-auto max-w-4xl ${isDark ? "bg-gray-800 border-gray-700" : ""}`}
        >
          <Tabs
            defaultValue="general"
            variant="outline"
            classNames={{
              list: isDark ? "border-gray-700" : "",
              tab: isDark
                ? "text-gray-300 hover:bg-gray-700 data-[active]:border-primary-color data-[active]:text-primary-color"
                : "",
            }}
          >
            <Tabs.List>
              {faqCategories.map((category) => (
                <Tabs.Tab
                  key={category.value}
                  value={category.value}
                  leftSection={<span className="text-xl">{category.icon}</span>}
                >
                  {category.label}
                </Tabs.Tab>
              ))}
            </Tabs.List>

            {faqCategories.map((category) => (
              <Tabs.Panel key={category.value} value={category.value} pt="md">
                <Accordion
                  classNames={{
                    item: isDark ? "border-gray-700" : "",
                    control: isDark ? "hover:bg-gray-700" : "",
                    content: isDark ? "bg-gray-800" : "",
                  }}
                >
                  {category.questions.map((item) => (
                    <Accordion.Item key={item.id} value={item.id}>
                      <Accordion.Control>
                        <Text
                          fw={500}
                          className={isDark ? "text-gray-200" : ""}
                        >
                          {item.question}
                        </Text>
                      </Accordion.Control>
                      <Accordion.Panel>
                        <Text
                          size="sm"
                          className={isDark ? "text-gray-300" : "text-gray-600"}
                        >
                          {item.answer}
                        </Text>
                      </Accordion.Panel>
                    </Accordion.Item>
                  ))}
                </Accordion>
              </Tabs.Panel>
            ))}
          </Tabs>
        </Card>

        <div
          className={`mx-auto mt-16 max-w-2xl rounded-lg p-8 text-center ${
            isDark ? "bg-primary-color/20" : "bg-primary-color/5"
          }`}
        >
          <h3
            className={`mb-4 text-2xl font-bold ${isDark ? "text-white" : ""}`}
          >
            Still have questions?
          </h3>
          <p className={`mb-6 ${isDark ? "text-gray-300" : "text-gray-600"}`}>
            If you couldn&apos;t find the answer to your question, our support
            team is here to help.
          </p>
          <Button
            component={Link}
            to="/contact-us"
            size="lg"
            className={`${
              isDark
                ? "bg-primary-600 hover:bg-primary-700"
                : "bg-primary-color hover:bg-primary-color/90"
            }`}
          >
            Contact Support
          </Button>
        </div>
      </div>
    </div>
  );
}
