import { PageContainer } from "@/design-system/components";
import { useThemeClasses } from "@/design-system/utils/theme-utils";
import {
  Avatar,
  Badge,
  Card,
  Grid,
  Group,
  LoadingOverlay,
  SimpleGrid,
  Tabs,
  Text,
  Title,
} from "@mantine/core";
import { useState } from "react";
import {
  FaBriefcase,
  FaBuilding,
  FaCheckCircle,
  FaHourglassHalf,
  FaMapMarkerAlt,
  FaTimesCircle,
  FaUsers,
} from "react-icons/fa";
import { useNavigate, useParams } from "react-router";

// Import our new components
import ApplicationCard from "@/components/employer/shared/ApplicationCard";
import SearchAndFilter from "@/components/employer/shared/SearchAndFilter";
import {
  useApplicationFilters,
  useApplications,
  useJobById,
} from "@/hooks/employer/useEmployerData";

export default function EmployerJobApplicationsPage() {
  const navigate = useNavigate();
  const params = useParams();
  const jobId = params.job_id;

  // Use our new hooks for data management
  const {
    applications,
    loading: applicationsLoading,
    updateApplicationStatus,
  } = useApplications(jobId);
  const { job, loading: jobLoading } = useJobById(jobId);
  const {
    searchTerm,
    setSearchTerm,
    statusFilter,
    setStatusFilter,
    filteredApplications,
    clearFilters,
  } = useApplicationFilters(applications);

  const [activeTab, setActiveTab] = useState<string | null>("all");

  // Combined loading state
  const loading = applicationsLoading || jobLoading;

  // Format salary for display
  const formatSalary = (salary?: {
    min: number;
    max: number;
    currency: string;
  }) => {
    if (!salary) return "Competitive";
    return `$${salary.min.toLocaleString()} - $${salary.max.toLocaleString()}`;
  };

  // Calculate statistics
  const totalApplications = applications.length;
  const pendingApplications = applications.filter(
    (app) => app.status === "pending",
  ).length;
  const approvedApplications = applications.filter(
    (app) => app.status === "approved",
  ).length;
  const rejectedApplications = applications.filter(
    (app) => app.status === "rejected",
  ).length;

  // Filter applications by tab
  const tabFilteredApplications =
    activeTab === "all"
      ? filteredApplications
      : filteredApplications.filter((app) => app.status === activeTab);

  // Event handlers
  const handleViewApplication = (applicationId: string) => {
    navigate(`/employer/applications/${applicationId}`);
  };

  const handleUpdateStatus = async (
    applicationId: string,
    status: "approved" | "rejected",
  ) => {
    try {
      await updateApplicationStatus(applicationId, status);
    } catch (error) {
      console.error("Failed to update application status:", error);
    }
  };

  const handleDownloadResume = (applicationId: string) => {
    // In a real app, this would download the resume
    console.log("Downloading resume for application:", applicationId);
  };

  return (
    <PageContainer
      breadcrumbItems={[
        { title: "Home", href: "/" },
        { title: "Manage Jobs", href: "/employer/manage-jobs" },
        { title: job?.title || "Job Applications" },
      ]}
      variant="employer"
    >
      <div className="relative">
        <LoadingOverlay visible={loading} />

        {/* Job Overview Section */}
        <Card withBorder radius="md" className="mb-8 overflow-hidden">
          <div className="relative">
            <div
              className={useThemeClasses(
                "absolute inset-0 bg-gradient-to-r from-blue-50 to-indigo-50",
                "absolute inset-0 bg-gradient-to-r from-dark-6 to-dark-5",
              )}
            />
            <div className="relative p-6 md:p-8">
              <Grid>
                <Grid.Col span={{ base: 12, md: 8 }}>
                  <Group align="flex-start" className="mb-4">
                    <Avatar
                      size={80}
                      radius="md"
                      color="blue"
                      className="border-4 border-white shadow-md"
                    >
                      {job?.company.substring(0, 2).toUpperCase()}
                    </Avatar>
                    <div>
                      <Title order={1} className="text-2xl font-bold">
                        {job?.title}
                      </Title>
                      <Group gap="xs" className="mt-1">
                        <Group gap="xs">
                          <FaBuilding className="text-blue-500" size={14} />
                          <Text size="sm" c="dimmed">
                            {job?.company}
                          </Text>
                        </Group>
                        <Group gap="xs">
                          <FaMapMarkerAlt className="text-blue-500" size={14} />
                          <Text size="sm" c="dimmed">
                            {job?.location}
                          </Text>
                        </Group>
                        <Group gap="xs">
                          <FaBriefcase className="text-blue-500" size={14} />
                          <Text size="sm" c="dimmed">
                            {job?.jobType}
                          </Text>
                        </Group>
                      </Group>
                      <Group gap="md" className="mt-4">
                        <Badge color="blue" size="lg">
                          {job?.status}
                        </Badge>
                        <Badge color="cyan" size="lg">
                          {formatSalary(job?.salary)}
                        </Badge>
                        <Badge color="indigo" size="lg">
                          {job?.experience} Experience
                        </Badge>
                      </Group>
                    </div>
                  </Group>
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 4 }}>
                  <Card
                    withBorder
                    radius="md"
                    className={useThemeClasses(
                      "bg-white shadow-sm",
                      "bg-dark-6 shadow-dark-lg",
                    )}
                  >
                    <Title order={4} className="mb-3 text-center">
                      Applications Summary
                    </Title>
                    <Group justify="space-between" className="mb-2">
                      <Text size="sm">Total Applications</Text>
                      <Badge size="lg" color="blue">
                        {totalApplications}
                      </Badge>
                    </Group>
                    <Group justify="space-between" className="mb-2">
                      <Text size="sm">Pending</Text>
                      <Badge size="sm" color="yellow">
                        {pendingApplications}
                      </Badge>
                    </Group>
                    <Group justify="space-between" className="mb-2">
                      <Text size="sm">Approved</Text>
                      <Badge size="sm" color="green">
                        {approvedApplications}
                      </Badge>
                    </Group>
                    <Group justify="space-between" className="mb-2">
                      <Text size="sm">Rejected</Text>
                      <Badge size="sm" color="red">
                        {rejectedApplications}
                      </Badge>
                    </Group>
                  </Card>
                </Grid.Col>
              </Grid>
            </div>
          </div>
        </Card>

        {/* Tabs and Search/Filter */}
        <div className="mb-6">
          <Tabs value={activeTab} onChange={setActiveTab} className="mb-4">
            <Tabs.List>
              <Tabs.Tab value="all" leftSection={<FaUsers size={16} />}>
                All ({totalApplications})
              </Tabs.Tab>
              <Tabs.Tab
                value="pending"
                leftSection={<FaHourglassHalf size={16} />}
              >
                Pending ({pendingApplications})
              </Tabs.Tab>
              <Tabs.Tab
                value="approved"
                leftSection={<FaCheckCircle size={16} />}
              >
                Approved ({approvedApplications})
              </Tabs.Tab>
              <Tabs.Tab
                value="rejected"
                leftSection={<FaTimesCircle size={16} />}
              >
                Rejected ({rejectedApplications})
              </Tabs.Tab>
            </Tabs.List>
          </Tabs>

          <SearchAndFilter
            searchValue={searchTerm}
            onSearchChange={setSearchTerm}
            filters={{
              status: {
                value: statusFilter,
                onChange: (value: string | null) =>
                  setStatusFilter(value || ""),
                options: [
                  { value: "pending", label: "Pending" },
                  { value: "approved", label: "Approved" },
                  { value: "rejected", label: "Rejected" },
                ],
              },
            }}
            onClearFilters={clearFilters}
            placeholder="Search applications by candidate name or email..."
          />
        </div>

        {/* Applications List */}
        {tabFilteredApplications.length === 0 ? (
          <Card withBorder p="xl" radius="md" className="text-center">
            <Title order={3} className="mb-2">
              No applications found
            </Title>
            <Text c="dimmed">Try adjusting your search or filter criteria</Text>
          </Card>
        ) : (
          <SimpleGrid cols={{ base: 1, sm: 2, lg: 3 }} spacing="md">
            {tabFilteredApplications.map((application) => (
              <ApplicationCard
                key={application.id}
                application={application}
                onView={handleViewApplication}
                onUpdateStatus={handleUpdateStatus}
                onDownloadResume={handleDownloadResume}
              />
            ))}
          </SimpleGrid>
        )}
      </div>
    </PageContainer>
  );
}
