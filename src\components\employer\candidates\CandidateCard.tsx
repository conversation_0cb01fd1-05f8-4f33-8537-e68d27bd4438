import { useSelectedCandidateStore } from "@/stores/employer-store";
import { useCandidateDetailsModal } from "@/stores/modal-store";
import { type Candidate } from "@/types";
import { Avatar, Badge, Button, Card, Group, Stack, Text } from "@mantine/core";
import { FaBriefcase, FaCalendarAlt, FaEye } from "react-icons/fa";

type CandidateCardProps = {
  candidate: Candidate;
  job: any;
};

export default function CandidateCard({ candidate, job }: CandidateCardProps) {
  const candidateDetailsModal = useCandidateDetailsModal();
  const setSelectedCandidateId = useSelectedCandidateStore(
    (state) => state.setSelectedCandidateId,
  );

  const openModal = () => {
    setSelectedCandidateId(candidate.id);
    candidateDetailsModal.open();
  };

  // Function to get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "Applied":
        return "blue";
      case "Shortlisted":
        return "green";
      case "Interviewed":
        return "yellow";
      case "Rejected":
        return "red";
      case "Hired":
        return "violet";
      default:
        return "gray";
    }
  };

  // Function to get initials from name
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  return (
    <Card withBorder radius="md" className="h-full shadow-sm" p="lg">
      <Stack gap="md" className="h-full">
        {/* Header with name and status */}
        <Group justify="space-between" align="flex-start">
          <Group wrap="nowrap">
            <Avatar
              radius="xl"
              size="md"
              color={getStatusColor(candidate.status)}
            >
              {getInitials(candidate.name)}
            </Avatar>
            <div>
              <Text fw={600} size="lg" className="line-clamp-1">
                {candidate.name}
              </Text>
              <Text size="sm" c="dimmed" className="line-clamp-1">
                {candidate.email}
              </Text>
            </div>
          </Group>
          <Badge color={getStatusColor(candidate.status)} size="md">
            {candidate.status}
          </Badge>
        </Group>

        {/* Job and application details */}
        <Stack gap="xs" className="flex-grow">
          <Group gap="xs" wrap="nowrap">
            <FaBriefcase size={14} className="flex-shrink-0 text-blue-500" />
            <Text size="sm" className="line-clamp-1">
              Applied for: <strong>{job?.title}</strong>
            </Text>
          </Group>
          <Group gap="xs" wrap="nowrap">
            <FaCalendarAlt size={14} className="flex-shrink-0 text-blue-500" />
            <Text size="sm">Applied on: {candidate.appliedDate}</Text>
          </Group>
        </Stack>

        {/* Skills */}
        {candidate.skills && candidate.skills.length > 0 && (
          <Group gap="xs">
            {candidate.skills.slice(0, 3).map((skill, index) => (
              <Badge key={index} color="blue" variant="light" size="sm">
                {skill}
              </Badge>
            ))}
            {candidate.skills.length > 3 && (
              <Badge color="gray" variant="light" size="sm">
                +{candidate.skills.length - 3} more
              </Badge>
            )}
          </Group>
        )}

        {/* Action button */}
        <Button
          variant="light"
          leftSection={<FaEye size={16} />}
          onClick={() => openModal()}
          fullWidth
        >
          View Details
        </Button>
      </Stack>
    </Card>
  );
}
