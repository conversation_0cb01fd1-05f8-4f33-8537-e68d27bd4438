import { Badge } from "@mantine/core";

interface StatusBadgeProps {
  status: "active" | "paused" | "closed" | "draft" | "pending" | "approved" | "rejected";
  size?: "xs" | "sm" | "md" | "lg" | "xl";
  variant?: "light" | "filled" | "outline" | "dot";
}

export default function StatusBadge({ 
  status, 
  size = "sm", 
  variant = "light" 
}: StatusBadgeProps) {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case "active":
        return {
          color: "green",
          label: "Active",
        };
      case "paused":
        return {
          color: "yellow",
          label: "Paused",
        };
      case "closed":
        return {
          color: "red",
          label: "Closed",
        };
      case "draft":
        return {
          color: "gray",
          label: "Draft",
        };
      case "pending":
        return {
          color: "orange",
          label: "Pending",
        };
      case "approved":
        return {
          color: "green",
          label: "Approved",
        };
      case "rejected":
        return {
          color: "red",
          label: "Rejected",
        };
      default:
        return {
          color: "blue",
          label: status.charAt(0).toUpperCase() + status.slice(1),
        };
    }
  };

  const config = getStatusConfig(status);

  return (
    <Badge
      color={config.color}
      variant={variant}
      size={size}
    >
      {config.label}
    </Badge>
  );
}
