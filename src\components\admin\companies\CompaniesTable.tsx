import { useThemeClasses } from "@/design-system/utils/theme-utils";
import {
  ActionIcon,
  Avatar,
  Badge,
  Checkbox,
  Group,
  Menu,
  Table,
  Text,
  ThemeIcon,
  Tooltip,
  useMantineColorScheme,
} from "@mantine/core";
import { useState } from "react";
import {
  FaBuilding,
  FaCalendarAlt,
  FaCheck,
  FaEdit,
  FaEllipsisV,
  FaEye,
  FaGlobe,
  FaTimes,
  FaTrash,
  FaUsers,
} from "react-icons/fa";
import { Link } from "react-router";

// Enhanced mock data for companies
const mockCompanies = [
  {
    id: 1,
    name: "Tech Corp",
    logo: "/images/company-logo-placeholder.png",
    industry: "Technology",
    size: "201-500",
    location: "San Francisco, CA",
    status: "active",
    registeredDate: "2023-10-10",
    website: "techcorp.com",
    jobCount: 12,
    verified: true,
  },
  {
    id: 2,
    name: "Innovate Inc",
    logo: "/images/company-logo-placeholder.png",
    industry: "Technology",
    size: "51-200",
    location: "New York, NY",
    status: "pending",
    registeredDate: "2023-10-09",
    website: "innovateinc.com",
    jobCount: 5,
    verified: false,
  },
  {
    id: 3,
    name: "Design Studio",
    logo: "/images/company-logo-placeholder.png",
    industry: "Design",
    size: "11-50",
    location: "Los Angeles, CA",
    status: "active",
    registeredDate: "2023-10-08",
    website: "designstudio.com",
    jobCount: 8,
    verified: true,
  },
  {
    id: 4,
    name: "Marketing Pro",
    logo: "/images/company-logo-placeholder.png",
    industry: "Marketing",
    size: "11-50",
    status: "blocked",
    registeredDate: "2023-10-07",
    website: "marketingpro.com",
    jobCount: 0,
    verified: false,
  },
  {
    id: 5,
    name: "Finance Group",
    logo: "/images/company-logo-placeholder.png",
    industry: "Finance",
    size: "51-200",
    location: "Boston, MA",
    status: "active",
    registeredDate: "2023-10-06",
    website: "financegroup.com",
    jobCount: 15,
    verified: true,
  },
];

interface CompaniesTableProps {
  onView: (companyId: number) => void;
  onApprove: (companyId: number) => void;
  onBlock: (companyId: number) => void;
}

export default function CompaniesTable({
  onView,
  onApprove,
  onBlock,
}: CompaniesTableProps) {
  const [companies] = useState(mockCompanies);
  const [selectedCompanies, setSelectedCompanies] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  const toggleSelectAll = () => {
    if (selectAll) {
      setSelectedCompanies([]);
    } else {
      setSelectedCompanies(companies.map((company) => company.id));
    }
    setSelectAll(!selectAll);
  };

  const toggleSelectCompany = (companyId: number) => {
    if (selectedCompanies.includes(companyId)) {
      setSelectedCompanies(selectedCompanies.filter((id) => id !== companyId));
      setSelectAll(false);
    } else {
      setSelectedCompanies([...selectedCompanies, companyId]);
      if (selectedCompanies.length + 1 === companies.length) {
        setSelectAll(true);
      }
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "active":
        return "green";
      case "pending":
        return "yellow";
      case "blocked":
        return "red";
      default:
        return "blue";
    }
  };

  const getIndustryColor = (industry: string) => {
    switch (industry) {
      case "Technology":
        return "blue";
      case "Design":
        return "violet";
      case "Marketing":
        return "orange";
      case "Finance":
        return "green";
      default:
        return "gray";
    }
  };

  return (
    <div className="overflow-x-auto">
      <Table
        striped
        highlightOnHover
        styles={{
          table: {
            backgroundColor: isDark ? "var(--mantine-color-dark-7)" : undefined,
          },
          thead: {
            backgroundColor: isDark ? "var(--mantine-color-dark-6)" : undefined,
          },
          tr: {
            "&:hover": {
              backgroundColor: isDark
                ? "var(--mantine-color-dark-5) !important"
                : undefined,
            },
          },
        }}
      >
        <Table.Thead>
          <Table.Tr>
            <Table.Th style={{ width: 40 }}>
              <Checkbox
                checked={selectAll}
                onChange={toggleSelectAll}
                aria-label="Select all companies"
              />
            </Table.Th>
            <Table.Th>Company</Table.Th>
            <Table.Th className="hidden md:table-cell">Industry</Table.Th>
            <Table.Th className="hidden lg:table-cell">Size</Table.Th>
            <Table.Th>Status</Table.Th>
            <Table.Th className="hidden lg:table-cell">Registered</Table.Th>
            <Table.Th>Actions</Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>
          {companies.map((company) => (
            <Table.Tr key={company.id} className="group">
              <Table.Td>
                <Checkbox
                  checked={selectedCompanies.includes(company.id)}
                  onChange={() => toggleSelectCompany(company.id)}
                  aria-label={`Select ${company.name}`}
                />
              </Table.Td>
              <Table.Td>
                <Group gap="sm">
                  <Avatar
                    src={company.logo}
                    alt={company.name}
                    radius="xl"
                    size="md"
                    className={useThemeClasses(
                      "border border-gray-200",
                      "border border-dark-4",
                    )}
                  />
                  <div>
                    <Group gap={6}>
                      <Text size="sm" fw={500}>
                        {company.name}
                      </Text>
                      {company.verified && (
                        <Tooltip label="Verified Company">
                          <ThemeIcon
                            color="blue"
                            variant="light"
                            radius="xl"
                            size="xs"
                          >
                            <FaCheck size={8} />
                          </ThemeIcon>
                        </Tooltip>
                      )}
                    </Group>
                    <Group gap={6} className="md:hidden">
                      <Text size="xs" c="dimmed">
                        {company.industry}
                      </Text>
                      <Text size="xs" c="dimmed">
                        •
                      </Text>
                      <Text size="xs" c="dimmed">
                        {company.jobCount} jobs
                      </Text>
                    </Group>
                    <Text size="xs" c="dimmed" className="hidden md:block">
                      <span
                        className={useThemeClasses(
                          "text-blue-500 hover:underline",
                          "text-blue-400 hover:underline",
                        )}
                      >
                        {company.website}
                      </span>
                    </Text>
                  </div>
                </Group>
              </Table.Td>
              <Table.Td className="hidden md:table-cell">
                <Group gap="xs">
                  <ThemeIcon
                    color={getIndustryColor(company.industry)}
                    variant="light"
                    size="sm"
                    radius="xl"
                  >
                    <FaBuilding size={10} />
                  </ThemeIcon>
                  <Text size="sm">{company.industry}</Text>
                </Group>
              </Table.Td>
              <Table.Td className="hidden lg:table-cell">
                <Group gap="xs">
                  <ThemeIcon color="gray" variant="light" size="sm" radius="xl">
                    <FaUsers size={10} />
                  </ThemeIcon>
                  <Text size="sm">{company.size}</Text>
                </Group>
              </Table.Td>
              <Table.Td>
                <Badge
                  color={getStatusBadgeColor(company.status)}
                  variant="light"
                  size="sm"
                >
                  {company.status}
                </Badge>
              </Table.Td>
              <Table.Td className="hidden lg:table-cell">
                <Group gap="xs">
                  <FaCalendarAlt
                    size={12}
                    className={useThemeClasses(
                      "text-gray-500",
                      "text-gray-400",
                    )}
                  />
                  <Text size="sm">{company.registeredDate}</Text>
                </Group>
              </Table.Td>
              <Table.Td>
                <Group gap="xs" className="hidden sm:flex">
                  <Tooltip label="View Details">
                    <ActionIcon
                      variant="subtle"
                      color="blue"
                      onClick={() => onView(company.id)}
                      aria-label="View company details"
                    >
                      <FaEye size={16} />
                    </ActionIcon>
                  </Tooltip>
                  {company.status === "pending" && (
                    <Tooltip label="Approve Company">
                      <ActionIcon
                        variant="subtle"
                        color="green"
                        onClick={() => onApprove(company.id)}
                        aria-label="Approve company"
                      >
                        <FaCheck size={16} />
                      </ActionIcon>
                    </Tooltip>
                  )}
                  {company.status !== "blocked" && (
                    <Tooltip label="Block Company">
                      <ActionIcon
                        variant="subtle"
                        color="red"
                        onClick={() => onBlock(company.id)}
                        aria-label="Block company"
                      >
                        <FaTimes size={16} />
                      </ActionIcon>
                    </Tooltip>
                  )}
                </Group>

                {/* Mobile menu */}
                <div className="sm:hidden">
                  <Menu position="bottom-end" shadow="md" width={160}>
                    <Menu.Target>
                      <div className="cursor-pointer">
                        <ActionIcon variant="subtle">
                          <FaEllipsisV size={16} />
                        </ActionIcon>
                      </div>
                    </Menu.Target>
                    <Menu.Dropdown>
                      <Menu.Item
                        leftSection={<FaEye size={14} />}
                        onClick={() => onView(company.id)}
                      >
                        View Details
                      </Menu.Item>
                      <Menu.Item leftSection={<FaEdit size={14} />}>
                        Edit Company
                      </Menu.Item>
                      <Menu.Item
                        leftSection={<FaGlobe size={14} />}
                        component={Link}
                        to={`https://${company.website}`}
                        target="_blank"
                      >
                        Visit Website
                      </Menu.Item>
                      {company.status === "pending" && (
                        <Menu.Item
                          leftSection={<FaCheck size={14} />}
                          color="green"
                          onClick={() => onApprove(company.id)}
                        >
                          Approve
                        </Menu.Item>
                      )}
                      {company.status !== "blocked" && (
                        <Menu.Item
                          leftSection={<FaTimes size={14} />}
                          color="red"
                          onClick={() => onBlock(company.id)}
                        >
                          Block
                        </Menu.Item>
                      )}
                      <Menu.Divider />
                      <Menu.Item
                        leftSection={<FaTrash size={14} />}
                        color="red"
                      >
                        Delete
                      </Menu.Item>
                    </Menu.Dropdown>
                  </Menu>
                </div>
              </Table.Td>
            </Table.Tr>
          ))}
        </Table.Tbody>
      </Table>
    </div>
  );
}
