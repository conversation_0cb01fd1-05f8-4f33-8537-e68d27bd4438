import {
  Badge,
  Card,
  Group,
  Progress,
  RingProgress,
  Text,
  ThemeIcon,
  Title,
  useMantineColorScheme,
} from "@mantine/core";
import {
  FaBullseye,
  FaChartLine,
  FaGlobe,
  FaLightbulb,
  FaRocket,
  FaUsers,
} from "react-icons/fa";

// Using a new image URL for vision
const visionImageUrl =
  "https://images.unsplash.com/photo-1507679799987-c73779587ccf?q=80&w=2942&auto=format&fit=crop";

const visionGoals = [
  {
    icon: FaGlobe,
    title: "Global Reach",
    description: "Connect talent and opportunities across borders",
    progress: 75,
    color: "blue",
    target: "150+ countries",
  },
  {
    icon: FaRocket,
    title: "Innovation",
    description: "Pioneering new solutions for the evolving job market",
    progress: 85,
    color: "violet",
    target: "10+ new features yearly",
  },
  {
    icon: FaChartLine,
    title: "Growth",
    description: "Expanding our platform to serve more industries and regions",
    progress: 65,
    color: "green",
    target: "50% annual growth",
  },
];

export default function Vision() {
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  return (
    <section
      className={`py-20 relative overflow-hidden ${isDark ? "bg-gray-900" : ""}`}
    >
      {/* Background with modern gradient and shapes */}
      <div
        className={`absolute inset-0 bg-gradient-to-br ${
          isDark
            ? "from-blue-900/20 via-gray-900 to-purple-900/20"
            : "from-blue-50 via-white to-purple-50"
        } -z-10`}
      ></div>
      <div
        className={`absolute top-0 right-0 w-96 h-96 ${
          isDark ? "bg-primary-color/10" : "bg-primary-color/5"
        } rounded-full -z-10 blur-3xl`}
      ></div>
      <div
        className={`absolute bottom-0 left-0 w-96 h-96 ${
          isDark ? "bg-blue-500/10" : "bg-blue-500/5"
        } rounded-full -z-10 blur-3xl`}
      ></div>

      {/* Decorative elements */}
      <div
        className={`absolute top-20 left-10 w-20 h-20 border-2 ${
          isDark ? "border-primary-color/30" : "border-primary-color/20"
        } rounded-full -z-10`}
      ></div>
      <div
        className={`absolute bottom-20 right-10 w-16 h-16 border-2 ${
          isDark ? "border-blue-500/30" : "border-blue-500/20"
        } rounded-full -z-10`}
      ></div>
      <div
        className={`absolute top-1/3 right-1/4 w-8 h-8 ${
          isDark ? "bg-primary-color/20" : "bg-primary-color/10"
        } rounded-full -z-10`}
      ></div>

      <div className="container relative">
        <div className="mb-16 text-center">
          <Badge color="primary_color" size="lg" radius="sm" className="mb-4">
            Our Aspiration
          </Badge>
          <Title
            order={2}
            className="mb-6 text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary-color to-blue-700"
          >
            Our Vision
          </Title>
          <div className="bg-primary-color mx-auto h-1 w-24 rounded-full"></div>
        </div>

        <div className="grid grid-cols-1 items-center gap-16 lg:grid-cols-2">
          <div className="order-2 lg:order-1">
            <div className="mb-10 relative">
              {/* Decorative icon */}
              <div className="absolute -left-10 -top-10 opacity-10">
                <FaBullseye size={80} className="text-primary-color" />
              </div>

              <Title
                order={3}
                className="text-primary-color !mb-6 text-2xl font-bold relative"
              >
                Shaping the Future of Work
              </Title>
              <Text
                className={`!mb-6 text-xl leading-relaxed ${isDark ? "text-gray-300" : "text-gray-700"}`}
              >
                We envision a world where finding the right job or the right
                candidate is{" "}
                <span className="font-semibold text-primary-color">
                  simple, fast, and effective
                </span>
                . We strive to be the leading platform for job postings and
                candidate applications, continuously improving our services to
                meet the evolving needs of the job market.
              </Text>
              <Text
                className={`text-lg border-l-4 border-primary-color !pl-4 italic ${isDark ? "text-gray-400" : "text-gray-600"}`}
              >
                By 2025, we aim to connect over{" "}
                <span className={`font-bold ${isDark ? "text-gray-200" : ""}`}>
                  1 million professionals
                </span>{" "}
                with their dream careers and help{" "}
                <span className={`font-bold ${isDark ? "text-gray-200" : ""}`}>
                  100,000 companies
                </span>{" "}
                build exceptional teams.
              </Text>
            </div>

            <div className="space-y-6">
              {visionGoals.map((goal, index) => (
                <Card
                  key={index}
                  className={`rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-300 ${
                    isDark ? "bg-gray-800 border-gray-700" : "bg-white"
                  }`}
                  withBorder
                >
                  <div className="flex items-start gap-6">
                    <div className="hidden sm:block">
                      <RingProgress
                        size={80}
                        thickness={4}
                        roundCaps
                        sections={[
                          {
                            value: goal.progress,
                            color: `var(--mantine-color-${goal.color}-6)`,
                          },
                        ]}
                        label={
                          <div className="flex items-center justify-center">
                            <goal.icon
                              size={24}
                              className={
                                goal.color === "blue"
                                  ? "text-blue-500"
                                  : goal.color === "green"
                                    ? "text-green-500"
                                    : "text-violet-500"
                              }
                            />
                          </div>
                        }
                      />
                    </div>

                    <div className="flex-1">
                      <Group
                        justify="space-between"
                        align="flex-start"
                        className="mb-2"
                      >
                        <div>
                          <Title
                            order={4}
                            className={`font-bold text-lg flex items-center gap-2 ${isDark ? "text-white" : ""}`}
                          >
                            <ThemeIcon
                              size={28}
                              radius="xl"
                              color={goal.color}
                              className="sm:hidden"
                            >
                              <goal.icon size={16} />
                            </ThemeIcon>
                            {goal.title}
                          </Title>
                          <Text
                            className={
                              isDark ? "text-gray-300" : "text-gray-600"
                            }
                          >
                            {goal.description}
                          </Text>
                        </div>
                        <Badge color={goal.color} size="lg" variant="light">
                          Target: {goal.target}
                        </Badge>
                      </Group>

                      <div className="mt-4">
                        <Group justify="space-between" className="mb-1">
                          <Text
                            size="sm"
                            fw={500}
                            className={
                              isDark ? "text-gray-300" : "text-gray-700"
                            }
                          >
                            Progress
                          </Text>
                          <Text
                            size="sm"
                            fw={700}
                            className={
                              goal.color === "blue"
                                ? isDark
                                  ? "text-blue-400"
                                  : "text-blue-600"
                                : goal.color === "green"
                                  ? isDark
                                    ? "text-green-400"
                                    : "text-green-600"
                                  : isDark
                                    ? "text-violet-400"
                                    : "text-violet-600"
                            }
                          >
                            {goal.progress}%
                          </Text>
                        </Group>
                        <Progress
                          value={goal.progress}
                          color={goal.color}
                          size="md"
                          radius="xl"
                          striped
                          animated
                        />
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>

          <div className="relative order-1 lg:order-2">
            {/* Enhanced image styling */}
            <div className="relative">
              <div className="absolute -inset-4 bg-gradient-to-tr from-primary-color/20 to-blue-500/20 rounded-xl blur-lg -z-10"></div>
              <div className="relative overflow-hidden rounded-xl shadow-2xl">
                <div className="absolute inset-0 bg-gradient-to-tr from-primary-color/30 to-transparent opacity-60 z-10"></div>
                <img
                  src={visionImageUrl}
                  alt="Our Vision - Future of Work"
                  className="h-auto w-full object-cover transition-transform duration-700 hover:scale-105"
                  width={600}
                  height={400}
                  style={{ maxHeight: "500px" }}
                />

                {/* Floating elements */}
                <div
                  className={`absolute top-6 right-6 ${
                    isDark ? "bg-gray-800/90" : "bg-white/90"
                  } backdrop-blur-sm p-3 rounded-lg shadow-lg z-20 flex items-center gap-2`}
                >
                  <ThemeIcon size={30} radius="xl" color="blue" variant="light">
                    <FaLightbulb size={16} />
                  </ThemeIcon>
                  <Text
                    size="sm"
                    fw={600}
                    className={isDark ? "text-gray-200" : ""}
                  >
                    Future-focused
                  </Text>
                </div>

                <div
                  className={`absolute bottom-6 left-6 ${
                    isDark ? "bg-gray-800/90" : "bg-white/90"
                  } backdrop-blur-sm p-3 rounded-lg shadow-lg z-20 flex items-center gap-2`}
                >
                  <ThemeIcon
                    size={30}
                    radius="xl"
                    color="green"
                    variant="light"
                  >
                    <FaUsers size={16} />
                  </ThemeIcon>
                  <Text
                    size="sm"
                    fw={600}
                    className={isDark ? "text-gray-200" : ""}
                  >
                    People-centered
                  </Text>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
