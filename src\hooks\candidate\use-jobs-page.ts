import { filterSchema, searchSchema } from "@/schemas/candidate";
import { getQueryParams } from "@/utils/helpers";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import { useEffect } from "react";

export default function useJobsPage() {
  const searchForm = useForm({
    initialValues: { search: "" },
    validate: zodResolver(searchSchema),
  });

  const filterForm = useForm({
    initialValues: {
      jobType: "",
      workType: "",
      category: "",
      datePosted: "",
      experience: "",
      careerLevel: "",
    },
    validate: zodResolver(filterSchema),
  });

  useEffect(() => {
    const { queryParams } = getQueryParams({
      ...searchForm.values,
      ...filterForm.values,
    });

    console.log(queryParams);
  }, [filterForm, searchForm]);

  const handleFilterRemove = (filterType: string) => {
    if (filterType === "search") {
      searchForm.reset();
    } else {
      filterForm.setFieldValue(filterType, "");
    }
  };

  return {
    searchForm,
    filterForm,
    handleFilterRemove,
  };
}
