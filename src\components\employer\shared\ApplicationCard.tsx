import { useThemeClasses } from "@/design-system/utils/theme-utils";
import { Avatar, Button, Card, Group, Text } from "@mantine/core";
import {
  FaCalendar,
  FaDownload,
  FaEnvelope,
  FaEye,
  FaGraduationCap,
  FaMapMarkerAlt,
  FaPhone,
} from "react-icons/fa";
import StatusBadge from "./StatusBadge";

interface ApplicationCardProps {
  application: {
    id: string;
    candidateName: string;
    candidateEmail: string;
    candidatePhone?: string;
    candidateLocation?: string;
    candidateAvatar?: string;
    jobTitle: string;
    appliedDate: string;
    status: "pending" | "approved" | "rejected";
    experience?: string;
    education?: string;
    resumeUrl?: string;
    coverLetter?: string;
  };
  onView?: (applicationId: string) => void;
  onUpdateStatus?: (applicationId: string, status: "approved" | "rejected") => void;
  onDownloadResume?: (applicationId: string) => void;
  showActions?: boolean;
  variant?: "default" | "compact";
}

export default function ApplicationCard({
  application,
  onView,
  onUpdateStatus,
  onDownloadResume,
  showActions = true,
  variant = "default",
}: ApplicationCardProps) {
  return (
    <Card
      shadow="sm"
      radius="md"
      withBorder
      className={useThemeClasses(
        "transition-all duration-200 hover:shadow-md",
        "transition-all duration-200 hover:shadow-dark-md",
      )}
    >
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-start gap-3">
          <Avatar
            src={application.candidateAvatar}
            alt={application.candidateName}
            size={variant === "compact" ? "md" : "lg"}
            radius="md"
            className="flex-shrink-0"
          >
            {application.candidateName.charAt(0).toUpperCase()}
          </Avatar>
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between gap-2 mb-1">
              <Text
                size={variant === "compact" ? "sm" : "md"}
                fw={600}
                className={useThemeClasses(
                  "text-gray-800 truncate",
                  "text-gray-100 truncate",
                )}
              >
                {application.candidateName}
              </Text>
              <StatusBadge status={application.status} />
            </div>
            <Text
              size="sm"
              className={useThemeClasses(
                "text-gray-600 mb-1",
                "text-gray-400 mb-1",
              )}
            >
              Applied for: {application.jobTitle}
            </Text>
            <div className="flex items-center gap-2">
              <FaCalendar
                size={12}
                className={useThemeClasses(
                  "text-gray-500",
                  "text-gray-400",
                )}
              />
              <Text size="xs" c="dimmed">
                Applied {application.appliedDate}
              </Text>
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div className="grid grid-cols-1 gap-2 sm:grid-cols-2">
          <div className="flex items-center gap-2">
            <FaEnvelope
              size={14}
              className={useThemeClasses(
                "text-gray-500",
                "text-gray-400",
              )}
            />
            <Text size="sm" c="dimmed" className="truncate">
              {application.candidateEmail}
            </Text>
          </div>
          {application.candidatePhone && (
            <div className="flex items-center gap-2">
              <FaPhone
                size={14}
                className={useThemeClasses(
                  "text-gray-500",
                  "text-gray-400",
                )}
              />
              <Text size="sm" c="dimmed">
                {application.candidatePhone}
              </Text>
            </div>
          )}
          {application.candidateLocation && (
            <div className="flex items-center gap-2">
              <FaMapMarkerAlt
                size={14}
                className={useThemeClasses(
                  "text-gray-500",
                  "text-gray-400",
                )}
              />
              <Text size="sm" c="dimmed">
                {application.candidateLocation}
              </Text>
            </div>
          )}
          {application.experience && (
            <div className="flex items-center gap-2">
              <FaGraduationCap
                size={14}
                className={useThemeClasses(
                  "text-gray-500",
                  "text-gray-400",
                )}
              />
              <Text size="sm" c="dimmed">
                {application.experience}
              </Text>
            </div>
          )}
        </div>

        {/* Cover Letter Preview */}
        {application.coverLetter && variant !== "compact" && (
          <div
            className={useThemeClasses(
              "rounded-md bg-gray-50 p-3",
              "rounded-md bg-dark-6 p-3",
            )}
          >
            <Text size="xs" fw={500} mb="xs" c="dimmed">
              Cover Letter
            </Text>
            <Text
              size="sm"
              c="dimmed"
              lineClamp={2}
              className={useThemeClasses(
                "text-gray-600",
                "text-gray-400",
              )}
            >
              {application.coverLetter}
            </Text>
          </div>
        )}

        {/* Actions */}
        {showActions && (
          <Group gap="sm" mt="md">
            {onView && (
              <Button
                variant="light"
                size="sm"
                leftSection={<FaEye size={14} />}
                onClick={() => onView(application.id)}
                className="flex-1"
              >
                View Details
              </Button>
            )}
            {application.resumeUrl && onDownloadResume && (
              <Button
                variant="outline"
                size="sm"
                leftSection={<FaDownload size={14} />}
                onClick={() => onDownloadResume(application.id)}
              >
                Resume
              </Button>
            )}
            {onUpdateStatus && application.status === "pending" && (
              <>
                <Button
                  color="green"
                  size="sm"
                  onClick={() => onUpdateStatus(application.id, "approved")}
                >
                  Approve
                </Button>
                <Button
                  color="red"
                  size="sm"
                  onClick={() => onUpdateStatus(application.id, "rejected")}
                >
                  Reject
                </Button>
              </>
            )}
          </Group>
        )}
      </div>
    </Card>
  );
}
