import Charts from "@/components/employer/dashboard-page/Charts";
import KeyMetrics from "@/components/employer/dashboard-page/KeyMetrics";
import QuickActions from "@/components/employer/dashboard-page/QuickActions";
import RecentActivity from "@/components/employer/dashboard-page/RecentActivity";
import { PageContainer, PageHeading } from "@/design-system/components";
import { useThemeClasses } from "@/design-system/utils/theme-utils";
import { Card, Grid, Group, RingProgress, Text, Title } from "@mantine/core";
import { FaBell, FaBriefcase, FaCalendarAlt, FaUsers } from "react-icons/fa";

export default function EmployerDashboardPage() {
  // Mock data for the welcome section
  const employerName = "Tech Solutions Inc.";
  const activeJobsCount = 8;
  const totalApplications = 120;
  const newApplications = 12;
  const interviewsScheduled = 5;
  const profileCompletion = 85;

  return (
    <PageContainer
      breadcrumbItems={[{ title: "Home", href: "/" }, { title: "Dashboard" }]}
      variant="employer"
    >
      {/* Hero Section */}
      <Card withBorder radius="md" className="mb-8 overflow-hidden">
        <div className="relative">
          {/* Background gradient */}
          <div
            className={useThemeClasses(
              "absolute inset-0 bg-gradient-to-r from-blue-50 to-indigo-50",
              "absolute inset-0 bg-gradient-to-r from-dark-6 to-dark-5",
            )}
          ></div>

          <div className="relative p-6 md:p-8">
            <Grid>
              <Grid.Col span={{ base: 12, md: 8 }}>
                <Title
                  order={2}
                  className={useThemeClasses(
                    "mb-2 text-2xl font-bold text-blue-800",
                    "mb-2 text-2xl font-bold text-blue-300",
                  )}
                >
                  Welcome back, {employerName}!
                </Title>
                <Text
                  className={useThemeClasses(
                    "mb-4 text-gray-600",
                    "mb-4 text-gray-400",
                  )}
                >
                  Here&apos;s what&apos;s happening with your job postings today
                </Text>

                <Grid className="mt-6">
                  <Grid.Col span={{ base: 6, md: 3 }}>
                    <Group gap="xs">
                      <FaBriefcase className="text-blue-500" />
                      <div>
                        <Text fw={700} size="xl">
                          {activeJobsCount}
                        </Text>
                        <Text size="xs" c="dimmed">
                          Active Jobs
                        </Text>
                      </div>
                    </Group>
                  </Grid.Col>
                  <Grid.Col span={{ base: 6, md: 3 }}>
                    <Group gap="xs">
                      <FaUsers className="text-green-500" />
                      <div>
                        <Text fw={700} size="xl">
                          {totalApplications}
                        </Text>
                        <Text size="xs" c="dimmed">
                          Total Applications
                        </Text>
                      </div>
                    </Group>
                  </Grid.Col>
                  <Grid.Col span={{ base: 6, md: 3 }}>
                    <Group gap="xs">
                      <FaBell className="text-yellow-500" />
                      <div>
                        <Text fw={700} size="xl">
                          {newApplications}
                        </Text>
                        <Text size="xs" c="dimmed">
                          New Applications
                        </Text>
                      </div>
                    </Group>
                  </Grid.Col>
                  <Grid.Col span={{ base: 6, md: 3 }}>
                    <Group gap="xs">
                      <FaCalendarAlt className="text-purple-500" />
                      <div>
                        <Text fw={700} size="xl">
                          {interviewsScheduled}
                        </Text>
                        <Text size="xs" c="dimmed">
                          Interviews Scheduled
                        </Text>
                      </div>
                    </Group>
                  </Grid.Col>
                </Grid>
              </Grid.Col>

              <Grid.Col span={{ base: 12, md: 4 }}>
                <Card
                  withBorder
                  radius="md"
                  className={useThemeClasses(
                    "bg-white shadow-sm",
                    "bg-dark-6 shadow-dark-lg",
                  )}
                >
                  <Text fw={700} ta="center" className="mb-2">
                    Profile Completion
                  </Text>
                  <Group justify="center" className="mb-2">
                    <RingProgress
                      size={120}
                      thickness={12}
                      roundCaps
                      sections={[{ value: profileCompletion, color: "blue" }]}
                      label={
                        <Text ta="center" size="lg" fw={700}>
                          {profileCompletion}%
                        </Text>
                      }
                    />
                  </Group>
                  <Text size="sm" c="dimmed" ta="center">
                    Complete your profile to attract more candidates
                  </Text>
                </Card>
              </Grid.Col>
            </Grid>
          </div>
        </div>
      </Card>

      {/* Page Title */}
      <PageHeading
        title="Dashboard Overview"
        subtitle="Monitor your recruitment activities and performance"
        variant="employer"
      />

      {/* Key Metrics Section */}
      <KeyMetrics />

      {/* Quick Actions Section */}
      <QuickActions />

      {/* Recent Activity Section */}
      <RecentActivity />

      {/* Charts Section */}
      <Charts />
    </PageContainer>
  );
}
