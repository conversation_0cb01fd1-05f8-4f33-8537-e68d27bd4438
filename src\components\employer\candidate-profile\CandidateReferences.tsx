import { candidateData } from "@/data/candidate-data";
import { Card, Text, Title } from "@mantine/core";

export default function CandidateReferences() {
  return (
    <Card withBorder radius="md" className="mt-6 p-6 shadow-sm">
      <Title order={2} className="mb-4 text-lg font-semibold">
        References
      </Title>
      {candidateData.references.map((ref, index) => (
        <div
          key={index}
          className="mb-4 border-b border-gray-200 pb-4 last:border-b-0"
        >
          <Text size="md" className="mb-1" component="div">
            <strong>Name:</strong> {ref.name}
          </Text>
          <Text size="md" className="mb-1" component="div">
            <strong>Position:</strong> {ref.position}
          </Text>
          <Text size="md" className="mb-1" component="div">
            <strong>Company:</strong> {ref.company}
          </Text>
          <Text size="md" className="mb-1" component="div">
            <strong>Email:</strong> {ref.email}
          </Text>
          <Text size="md" className="mb-1" component="div">
            <strong>Phone:</strong> {ref.phone}
          </Text>
        </div>
      ))}
    </Card>
  );
}
