import { TrackingModal } from "@/components/candidate/applications/tracking-modal";
import { type Application, mockApplications } from "@/data/applications";
import { PageContainer, PageHeading } from "@/design-system/components";
import { useThemeClasses } from "@/design-system/utils/theme-utils";
import {
  Badge,
  Button,
  Card,
  Divider,
  Grid,
  Group,
  Paper,
  Progress,
  RingProgress,
  Select,
  SimpleGrid,
  Stack,
  Tabs,
  Text,
  TextInput,
  Title,
  useMantineColorScheme,
} from "@mantine/core";
import { useMemo, useState } from "react";
import {
  BiBriefcase,
  BiBuildings,
  BiCalendar,
  BiCheckCircle,
  BiChevronRight,
  BiEnvelope,
  BiMap,
  BiSearch,
  BiSortAlt2,
  BiTime,
  BiX,
} from "react-icons/bi";

export default function CandidateApplicationsPage() {
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState<string>("newest");
  const [selectedApplication, setSelectedApplication] =
    useState<Application | null>(null);
  const [activeTab, setActiveTab] = useState<string | null>("all");
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  // Calculate application statistics
  const stats = useMemo(() => {
    const total = mockApplications.length;
    const pending = mockApplications.filter(
      (app) => app.status === "pending",
    ).length;
    const reviewed = mockApplications.filter(
      (app) => app.status === "reviewed",
    ).length;
    const accepted = mockApplications.filter(
      (app) => app.status === "accepted",
    ).length;
    const rejected = mockApplications.filter(
      (app) => app.status === "rejected",
    ).length;

    return { total, pending, reviewed, accepted, rejected };
  }, []);

  // Filter applications based on status, search query, and active tab
  const filteredApplications = useMemo(() => {
    let filtered = mockApplications;

    // Filter by tab
    if (activeTab && activeTab !== "all") {
      filtered = filtered.filter((app) => app.status === activeTab);
    }

    // Filter by status dropdown (if different from tab)
    if (statusFilter && statusFilter !== activeTab) {
      filtered = filtered.filter((app) => app.status === statusFilter);
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (app) =>
          app.jobTitle.toLowerCase().includes(query) ||
          app.company.toLowerCase().includes(query) ||
          app.location.toLowerCase().includes(query),
      );
    }

    // Sort applications
    return filtered.sort((a, b) => {
      if (sortBy === "newest") {
        return (
          new Date(b.appliedDate).getTime() - new Date(a.appliedDate).getTime()
        );
      } else if (sortBy === "oldest") {
        return (
          new Date(a.appliedDate).getTime() - new Date(b.appliedDate).getTime()
        );
      } else if (sortBy === "company") {
        return a.company.localeCompare(b.company);
      } else {
        return 0;
      }
    });
  }, [statusFilter, searchQuery, sortBy, activeTab]);

  // Get color based on status
  const getStatusColor = (status: string) => {
    const colors = {
      pending: "yellow",
      reviewed: "blue",
      rejected: "red",
      accepted: "green",
    };
    return colors[status as keyof typeof colors];
  };

  // Get icon based on status
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending":
        return <BiTime size={18} />;
      case "reviewed":
        return <BiEnvelope size={18} />;
      case "accepted":
        return <BiCheckCircle size={18} />;
      case "rejected":
        return <BiX size={18} />;
      default:
        return null;
    }
  };

  return (
    <PageContainer
      breadcrumbItems={[
        { title: "Home", href: "/" },
        { title: "Applications" },
      ]}
      variant="candidate"
    >
      {/* Header with title and search */}
      <div className="mb-6">
        <PageHeading
          title="My Applications"
          subtitle="Track the status of your job applications"
          variant="candidate"
        />
      </div>

      {/* Dashboard Summary Cards */}
      <SimpleGrid
        cols={{ base: 1, sm: 2, md: 4 }}
        spacing="md"
        className="mb-6"
      >
        <Card
          withBorder
          radius="md"
          p="md"
          className={useThemeClasses("bg-blue-50", "bg-blue-900/20")}
        >
          <Group justify="space-between" align="flex-start">
            <div>
              <Text size="xs" c="dimmed" fw={500} className="mb-1">
                TOTAL APPLICATIONS
              </Text>
              <Text fw={700} size="xl">
                {stats.total}
              </Text>
            </div>
            <div
              className={useThemeClasses(
                "rounded-full bg-blue-100 p-2",
                "rounded-full bg-blue-800/30 p-2",
              )}
            >
              <BiBriefcase size={24} className="text-blue-500" />
            </div>
          </Group>
        </Card>

        <Card
          withBorder
          radius="md"
          p="md"
          className={useThemeClasses("bg-yellow-50", "bg-yellow-900/20")}
        >
          <Group justify="space-between" align="flex-start">
            <div>
              <Text size="xs" c="dimmed" fw={500} className="mb-1">
                PENDING
              </Text>
              <Text fw={700} size="xl">
                {stats.pending}
              </Text>
            </div>
            <div
              className={useThemeClasses(
                "rounded-full bg-yellow-100 p-2",
                "rounded-full bg-yellow-800/30 p-2",
              )}
            >
              <BiTime size={24} className="text-yellow-500" />
            </div>
          </Group>
        </Card>

        <Card
          withBorder
          radius="md"
          p="md"
          className={useThemeClasses("bg-green-50", "bg-green-900/20")}
        >
          <Group justify="space-between" align="flex-start">
            <div>
              <Text size="xs" c="dimmed" fw={500} className="mb-1">
                ACCEPTED
              </Text>
              <Text fw={700} size="xl">
                {stats.accepted}
              </Text>
            </div>
            <div
              className={useThemeClasses(
                "rounded-full bg-green-100 p-2",
                "rounded-full bg-green-800/30 p-2",
              )}
            >
              <BiCheckCircle size={24} className="text-green-500" />
            </div>
          </Group>
        </Card>

        <Card
          withBorder
          radius="md"
          p="md"
          className={useThemeClasses("bg-red-50", "bg-red-900/20")}
        >
          <Group justify="space-between" align="flex-start">
            <div>
              <Text size="xs" c="dimmed" fw={500} className="mb-1">
                REJECTED
              </Text>
              <Text fw={700} size="xl">
                {stats.rejected}
              </Text>
            </div>
            <div
              className={useThemeClasses(
                "rounded-full bg-red-100 p-2",
                "rounded-full bg-red-800/30 p-2",
              )}
            >
              <BiX size={24} className="text-red-500" />
            </div>
          </Group>
        </Card>
      </SimpleGrid>

      {/* Progress Overview */}
      <Card withBorder radius="md" className="mb-6">
        <Group justify="space-between" className="mb-4">
          <Title order={4}>Application Progress</Title>
          <Text size="sm" c="dimmed">
            Last updated: {new Date().toLocaleDateString()}
          </Text>
        </Group>

        <Grid>
          <Grid.Col span={{ base: 12, md: 8 }}>
            <div className="mb-4">
              <Group justify="space-between" className="mb-1">
                <Text size="sm" fw={500}>
                  Pending
                </Text>
                <Text size="sm" c="dimmed">
                  {stats.pending}/{stats.total}
                </Text>
              </Group>
              <Progress
                value={(stats.pending / stats.total) * 100}
                color="yellow"
                size="md"
                radius="xl"
              />
            </div>

            <div className="mb-4">
              <Group justify="space-between" className="mb-1">
                <Text size="sm" fw={500}>
                  Reviewed
                </Text>
                <Text size="sm" c="dimmed">
                  {stats.reviewed}/{stats.total}
                </Text>
              </Group>
              <Progress
                value={(stats.reviewed / stats.total) * 100}
                color="blue"
                size="md"
                radius="xl"
              />
            </div>

            <div className="mb-4">
              <Group justify="space-between" className="mb-1">
                <Text size="sm" fw={500}>
                  Accepted
                </Text>
                <Text size="sm" c="dimmed">
                  {stats.accepted}/{stats.total}
                </Text>
              </Group>
              <Progress
                value={(stats.accepted / stats.total) * 100}
                color="green"
                size="md"
                radius="xl"
              />
            </div>

            <div>
              <Group justify="space-between" className="mb-1">
                <Text size="sm" fw={500}>
                  Rejected
                </Text>
                <Text size="sm" c="dimmed">
                  {stats.rejected}/{stats.total}
                </Text>
              </Group>
              <Progress
                value={(stats.rejected / stats.total) * 100}
                color="red"
                size="md"
                radius="xl"
              />
            </div>
          </Grid.Col>

          <Grid.Col
            span={{ base: 12, md: 4 }}
            className="flex items-center justify-center"
          >
            <div className="text-center">
              <RingProgress
                size={180}
                thickness={16}
                roundCaps
                sections={[
                  {
                    value: (stats.pending / stats.total) * 100,
                    color: "yellow",
                  },
                  {
                    value: (stats.reviewed / stats.total) * 100,
                    color: "blue",
                  },
                  {
                    value: (stats.accepted / stats.total) * 100,
                    color: "green",
                  },
                  { value: (stats.rejected / stats.total) * 100, color: "red" },
                ]}
                label={
                  <div className="text-center">
                    <Text fw={700} size="xl">
                      {stats.total}
                    </Text>
                    <Text size="xs" c="dimmed">
                      Applications
                    </Text>
                  </div>
                }
              />
            </div>
          </Grid.Col>
        </Grid>
      </Card>

      {/* Filters and Search */}
      <Card withBorder radius="md" className="mb-6">
        <Group justify="space-between" wrap="wrap" gap="md">
          <TextInput
            placeholder="Search jobs or companies"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.currentTarget.value)}
            leftSection={<BiSearch size={18} />}
            style={{ flexGrow: 1, maxWidth: "400px" }}
          />

          <Group gap="md">
            <Select
              placeholder="Sort by"
              value={sortBy}
              onChange={(value) => setSortBy(value || "newest")}
              data={[
                { value: "newest", label: "Newest first" },
                { value: "oldest", label: "Oldest first" },
                { value: "company", label: "Company name" },
              ]}
              leftSection={<BiSortAlt2 size={18} />}
            />

            <Select
              placeholder="Filter by status"
              value={statusFilter}
              onChange={setStatusFilter}
              data={[
                { value: "", label: "All statuses" },
                { value: "pending", label: "Pending" },
                { value: "reviewed", label: "Reviewed" },
                { value: "accepted", label: "Accepted" },
                { value: "rejected", label: "Rejected" },
              ]}
              clearable
            />
          </Group>
        </Group>
      </Card>

      {/* Tabs */}
      <Tabs
        value={activeTab}
        onChange={setActiveTab}
        className="mb-6"
        styles={{
          tab: {
            color: isDark
              ? "var(--mantine-color-gray-4) !important"
              : undefined,
            "&[data-active]": {
              color: isDark
                ? "var(--mantine-color-blue-4) !important"
                : undefined,
              borderColor: isDark
                ? "var(--mantine-color-blue-4) !important"
                : undefined,
            },
          },
        }}
      >
        <Tabs.List>
          <Tabs.Tab value="all">All Applications</Tabs.Tab>
          <Tabs.Tab
            value="pending"
            leftSection={<BiTime size={16} />}
            rightSection={
              <Badge size="xs" variant="light" color="yellow">
                {stats.pending}
              </Badge>
            }
          >
            Pending
          </Tabs.Tab>
          <Tabs.Tab
            value="reviewed"
            leftSection={<BiEnvelope size={16} />}
            rightSection={
              <Badge size="xs" variant="light" color="blue">
                {stats.reviewed}
              </Badge>
            }
          >
            Reviewed
          </Tabs.Tab>
          <Tabs.Tab
            value="accepted"
            leftSection={<BiCheckCircle size={16} />}
            rightSection={
              <Badge size="xs" variant="light" color="green">
                {stats.accepted}
              </Badge>
            }
          >
            Accepted
          </Tabs.Tab>
          <Tabs.Tab
            value="rejected"
            leftSection={<BiX size={16} />}
            rightSection={
              <Badge size="xs" variant="light" color="red">
                {stats.rejected}
              </Badge>
            }
          >
            Rejected
          </Tabs.Tab>
        </Tabs.List>
      </Tabs>

      {/* Applications List */}
      {filteredApplications.length === 0 ? (
        <Paper withBorder p="xl" radius="md" className="text-center">
          <Title
            order={3}
            className={useThemeClasses(
              "mb-2 text-gray-700",
              "mb-2 text-gray-300",
            )}
          >
            No applications found
          </Title>
          <Text className={useThemeClasses("text-gray-500", "text-gray-400")}>
            Try adjusting your search or filter criteria
          </Text>
        </Paper>
      ) : (
        <SimpleGrid cols={{ base: 1, md: 2 }} spacing="md">
          {filteredApplications.map((application) => (
            <Card
              key={application.id}
              withBorder
              radius="md"
              className="h-full cursor-pointer transition-all hover:border-blue-300 hover:shadow-md"
              onClick={() => setSelectedApplication(application)}
            >
              <div className="relative">
                {/* Status indicator */}
                <div
                  className={`absolute top-0 right-0 h-3 w-3 rounded-full bg-${getStatusColor(
                    application.status,
                  )}-500 m-1`}
                ></div>

                <Group justify="space-between" className="mb-3">
                  <div className="flex items-center gap-3">
                    <div
                      className={`flex h-10 w-10 items-center justify-center rounded-md bg-${getStatusColor(
                        application.status,
                      )}-100 text-${getStatusColor(application.status)}-500`}
                    >
                      {getStatusIcon(application.status)}
                    </div>
                    <Badge size="lg" color={getStatusColor(application.status)}>
                      {application.status.charAt(0).toUpperCase() +
                        application.status.slice(1)}
                    </Badge>
                  </div>
                  <Text size="sm" c="dimmed">
                    ID: #{application.id}
                  </Text>
                </Group>

                <Title
                  order={3}
                  className={useThemeClasses(
                    "mb-1 line-clamp-1 text-xl text-[#0f172a]",
                    "mb-1 line-clamp-1 text-xl text-gray-200",
                  )}
                >
                  {application.jobTitle}
                </Title>

                <Group gap="xs" className="mb-3">
                  <BiBuildings className="text-primary-color" size={18} />
                  <Text className="text-primary-color line-clamp-1 font-medium">
                    {application.company}
                  </Text>
                </Group>

                <Divider className="my-3" />

                <Stack gap="sm" className="mb-3">
                  <Group
                    gap="xs"
                    className={useThemeClasses(
                      "text-gray-600",
                      "text-gray-400",
                    )}
                  >
                    <BiCalendar size={16} />
                    <Text size="sm">
                      Applied:{" "}
                      {new Date(application.appliedDate).toLocaleDateString(
                        "en-US",
                        {
                          year: "numeric",
                          month: "short",
                          day: "numeric",
                        },
                      )}
                    </Text>
                  </Group>

                  <Group
                    gap="xs"
                    className={useThemeClasses(
                      "text-gray-600",
                      "text-gray-400",
                    )}
                  >
                    <BiMap size={16} />
                    <Text size="sm" className="line-clamp-1">
                      {application.location}
                    </Text>
                  </Group>

                  <Group
                    gap="xs"
                    className={useThemeClasses(
                      "text-gray-600",
                      "text-gray-400",
                    )}
                  >
                    <BiBriefcase size={16} />
                    <Text size="sm">{application.jobType}</Text>
                  </Group>
                </Stack>

                <Button
                  variant="light"
                  color="blue"
                  fullWidth
                  rightSection={<BiChevronRight size={18} />}
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedApplication(application);
                  }}
                >
                  View Details
                </Button>
              </div>
            </Card>
          ))}
        </SimpleGrid>
      )}

      {selectedApplication && (
        <TrackingModal
          application={selectedApplication}
          opened={!!selectedApplication}
          onClose={() => setSelectedApplication(null)}
        />
      )}
    </PageContainer>
  );
}
