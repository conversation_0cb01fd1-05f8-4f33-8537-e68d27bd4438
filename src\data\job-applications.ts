import { type Candidate } from "@/types";
import { type Application } from "./applications";

export interface JobApplication extends Application {
  candidateId: number;
  jobId: number;
}

export interface JobCandidate extends Candidate {
  resumeUrl?: string;
  coverLetter?: string;
  skills?: string[];
  experience?: {
    company: string;
    role: string;
    duration: string;
  }[];
  education?: {
    institution: string;
    degree: string;
    duration: string;
  }[];
  questions?: {
    question: string;
    answer: string;
  }[];
}

// Mock candidates data
export const mockCandidates: JobCandidate[] = [
  {
    id: 1,
    name: "<PERSON>",
    email: "<EMAIL>",
    status: "Applied",
    jobId: 1,
    appliedDate: "2023-10-01",
    resumeUrl: "/resumes/john-doe-resume.pdf",
    coverLetter: "I am excited to apply for the Frontend Developer position...",
    skills: ["JavaScript", "React", "Node.js", "TypeScript"],
    experience: [
      {
        company: "Tech Corp",
        role: "Frontend Developer",
        duration: "2020 - Present",
      },
      {
        company: "Innovate Solutions",
        role: "Junior Developer",
        duration: "2018 - 2020",
      },
    ],
    education: [
      {
        institution: "University of Tech",
        degree: "Bachelor of Science in Computer Science",
        duration: "2014 - 2018",
      },
    ],
    questions: [
      {
        question: "Why do you want to work for our company?",
        answer:
          "I admire your company's innovative approach to solving real-world problems.",
      },
      {
        question: "What are your strengths and weaknesses?",
        answer:
          "My strengths include problem-solving and teamwork. My weakness is perfectionism.",
      },
    ],
  },
  {
    id: 2,
    name: "Jane Smith",
    email: "<EMAIL>",
    status: "Shortlisted",
    jobId: 1,
    appliedDate: "2023-10-02",
    resumeUrl: "/resumes/jane-smith-resume.pdf",
    coverLetter:
      "I believe my skills in frontend development make me a perfect fit...",
    skills: ["React", "CSS", "HTML", "UI/UX Design"],
    experience: [
      {
        company: "Design Studio",
        role: "UI Developer",
        duration: "2019 - Present",
      },
    ],
    education: [
      {
        institution: "Design University",
        degree: "Bachelor of Arts in Interactive Design",
        duration: "2015 - 2019",
      },
    ],
  },
  {
    id: 3,
    name: "Alice Johnson",
    email: "<EMAIL>",
    status: "Interviewed",
    jobId: 2,
    appliedDate: "2023-10-03",
    skills: ["Product Management", "Agile", "JIRA", "User Research"],
  },
  {
    id: 4,
    name: "Bob Brown",
    email: "<EMAIL>",
    status: "Rejected",
    jobId: 3,
    appliedDate: "2023-10-04",
  },
  {
    id: 5,
    name: "Charlie Davis",
    email: "<EMAIL>",
    status: "Hired",
    jobId: 2,
    appliedDate: "2023-10-05",
  },
  {
    id: 6,
    name: "Diana Evans",
    email: "<EMAIL>",
    status: "Applied",
    jobId: 1,
    appliedDate: "2023-10-06",
    skills: ["JavaScript", "Vue.js", "CSS", "Responsive Design"],
  },
  {
    id: 7,
    name: "Ethan Foster",
    email: "<EMAIL>",
    status: "Applied",
    jobId: 3,
    appliedDate: "2023-10-07",
  },
  // New candidates for job 4 (Digital Marketing Specialist)
  {
    id: 8,
    name: "Frank Miller",
    email: "<EMAIL>",
    status: "Applied",
    jobId: 4,
    appliedDate: "2023-10-12",
    skills: ["SEO", "Content Marketing", "Social Media Management"],
    resumeUrl: "/resumes/frank-miller-resume.pdf",
    coverLetter:
      "I am excited to apply for the Digital Marketing Specialist position...",
  },
  {
    id: 9,
    name: "Grace Wilson",
    email: "<EMAIL>",
    status: "Shortlisted",
    jobId: 4,
    appliedDate: "2023-10-14",
    skills: ["Google Ads", "Facebook Ads", "Email Marketing", "Analytics"],
    experience: [
      {
        company: "Marketing Agency",
        role: "Marketing Assistant",
        duration: "2021 - Present",
      },
    ],
  },
  {
    id: 10,
    name: "Henry Taylor",
    email: "<EMAIL>",
    status: "Applied",
    jobId: 4,
    appliedDate: "2023-10-16",
    skills: ["Content Creation", "SEO", "PPC", "Social Media"],
  },
  // New candidates for job 5 (Data Scientist)
  {
    id: 11,
    name: "Isabella Clark",
    email: "<EMAIL>",
    status: "Applied",
    jobId: 5,
    appliedDate: "2023-10-18",
    skills: ["Python", "R", "SQL", "Machine Learning"],
    education: [
      {
        institution: "Data Science University",
        degree: "Master of Science in Data Science",
        duration: "2019 - 2021",
      },
    ],
  },
  {
    id: 12,
    name: "Jack Robinson",
    email: "<EMAIL>",
    status: "Shortlisted",
    jobId: 5,
    appliedDate: "2023-10-20",
    skills: ["Python", "TensorFlow", "Deep Learning", "NLP"],
    experience: [
      {
        company: "AI Startup",
        role: "Junior Data Scientist",
        duration: "2022 - Present",
      },
    ],
  },
  {
    id: 13,
    name: "Kate Martinez",
    email: "<EMAIL>",
    status: "Hired",
    jobId: 5,
    appliedDate: "2023-10-22",
    skills: ["Statistical Analysis", "Data Visualization", "Python", "Tableau"],
    resumeUrl: "/resumes/kate-martinez-resume.pdf",
    experience: [
      {
        company: "Big Data Corp",
        role: "Data Analyst",
        duration: "2020 - 2023",
      },
    ],
    education: [
      {
        institution: "Statistics University",
        degree: "PhD in Statistics",
        duration: "2016 - 2020",
      },
    ],
  },
  {
    id: 14,
    name: "Leo Anderson",
    email: "<EMAIL>",
    status: "Rejected",
    jobId: 5,
    appliedDate: "2023-10-24",
    skills: ["Python", "R", "Data Mining"],
  },
];

// Mock job applications data
export const mockJobApplications: JobApplication[] = [
  {
    id: 1,
    jobId: 1,
    candidateId: 1,
    jobTitle: "Frontend Developer",
    company: "Tech Corp",
    appliedDate: "2023-10-01",
    status: "pending",
    jobType: "Full-time",
    location: "New York, USA",
  },
  {
    id: 2,
    jobId: 1,
    candidateId: 2,
    jobTitle: "Frontend Developer",
    company: "Tech Corp",
    appliedDate: "2023-10-02",
    status: "reviewed",
    jobType: "Full-time",
    location: "New York, USA",
  },
  {
    id: 3,
    jobId: 2,
    candidateId: 3,
    jobTitle: "Backend Developer",
    company: "Code Masters",
    appliedDate: "2023-10-03",
    status: "accepted",
    jobType: "Full-time",
    location: "San Francisco, USA",
  },
  {
    id: 4,
    jobId: 3,
    candidateId: 4,
    jobTitle: "UI/UX Designer",
    company: "Design Co",
    appliedDate: "2023-10-04",
    status: "rejected",
    jobType: "Part-time",
    location: "Remote",
  },
  {
    id: 5,
    jobId: 2,
    candidateId: 5,
    jobTitle: "Backend Developer",
    company: "Code Masters",
    appliedDate: "2023-10-05",
    status: "accepted",
    jobType: "Full-time",
    location: "San Francisco, USA",
  },
  {
    id: 6,
    jobId: 1,
    candidateId: 6,
    jobTitle: "Frontend Developer",
    company: "Tech Corp",
    appliedDate: "2023-10-06",
    status: "pending",
    jobType: "Full-time",
    location: "New York, USA",
  },
  {
    id: 7,
    jobId: 3,
    candidateId: 7,
    jobTitle: "UI/UX Designer",
    company: "Design Co",
    appliedDate: "2023-10-07",
    status: "pending",
    jobType: "Part-time",
    location: "Remote",
  },
  // New applications for job 4 (Digital Marketing Specialist)
  {
    id: 8,
    jobId: 4,
    candidateId: 8,
    jobTitle: "Digital Marketing Specialist",
    company: "Market Pros",
    appliedDate: "2023-10-12",
    status: "pending",
    jobType: "Contract",
    location: "Chicago, USA",
  },
  {
    id: 9,
    jobId: 4,
    candidateId: 9,
    jobTitle: "Digital Marketing Specialist",
    company: "Market Pros",
    appliedDate: "2023-10-14",
    status: "reviewed",
    jobType: "Contract",
    location: "Chicago, USA",
  },
  {
    id: 10,
    jobId: 4,
    candidateId: 10,
    jobTitle: "Digital Marketing Specialist",
    company: "Market Pros",
    appliedDate: "2023-10-16",
    status: "pending",
    jobType: "Contract",
    location: "Chicago, USA",
  },
  // New applications for job 5 (Data Scientist)
  {
    id: 11,
    jobId: 5,
    candidateId: 11,
    jobTitle: "Data Scientist",
    company: "Data Wizards",
    appliedDate: "2023-10-18",
    status: "pending",
    jobType: "Full-time",
    location: "Boston, USA",
  },
  {
    id: 12,
    jobId: 5,
    candidateId: 12,
    jobTitle: "Data Scientist",
    company: "Data Wizards",
    appliedDate: "2023-10-20",
    status: "reviewed",
    jobType: "Full-time",
    location: "Boston, USA",
  },
  {
    id: 13,
    jobId: 5,
    candidateId: 13,
    jobTitle: "Data Scientist",
    company: "Data Wizards",
    appliedDate: "2023-10-22",
    status: "accepted",
    jobType: "Full-time",
    location: "Boston, USA",
  },
  {
    id: 14,
    jobId: 5,
    candidateId: 14,
    jobTitle: "Data Scientist",
    company: "Data Wizards",
    appliedDate: "2023-10-24",
    status: "rejected",
    jobType: "Full-time",
    location: "Boston, USA",
  },
];

// Helper function to get applications for a specific job
export function getApplicationsByJobId(jobId: number): JobApplication[] {
  return mockJobApplications.filter((app) => app.jobId === jobId);
}

// Helper function to get candidate details by ID
export function getCandidateById(
  candidateId: number,
): JobCandidate | undefined {
  return mockCandidates.find((candidate) => candidate.id === candidateId);
}

// Helper function to get all candidates for a specific job
export function getCandidatesByJobId(jobId: number): JobCandidate[] {
  return mockCandidates.filter((candidate) => candidate.jobId === jobId);
}

// Helper function to get the count of applications for a specific job
export function getApplicationsCountByJobId(jobId: number): number {
  return mockJobApplications.filter((app) => app.jobId === jobId).length;
}
