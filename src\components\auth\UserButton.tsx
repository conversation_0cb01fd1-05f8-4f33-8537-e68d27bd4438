import { useDrawerContext } from "@/stores/ui-store";
import { URLS } from "@/utils/urls";
import {
  Avatar,
  Button,
  Menu,
  Text,
  UnstyledButton,
  useMantineColorScheme,
} from "@mantine/core";
import { useState } from "react";
import {
  FaBriefcase,
  FaChevronDown,
  FaClipboardList,
  FaCog,
  FaCreditCard,
  FaLock,
  FaSignOutAlt,
  FaTachometerAlt,
  FaUser,
  FaUserPlus,
} from "react-icons/fa";
import { Link, useNavigate } from "react-router";

interface UserButtonProps {
  inDrawer?: boolean;
}

export default function UserButton({ inDrawer }: UserButtonProps) {
  const navigate = useNavigate();
  const { isInDrawer } = useDrawerContext();
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  // Use the prop if provided, otherwise use the context
  const isInsideDrawer = inDrawer !== undefined ? inDrawer : isInDrawer;

  // For demo purposes, we'll use useState to simulate login state
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [user, setUser] = useState<{
    name: string;
    email: string;
    role: "candidate" | "employer" | "admin";
    avatar: string;
  } | null>(null);

  // For demo purposes, let's add a function to simulate login with different user profiles
  const simulateLogin = (role: "candidate" | "employer" | "admin") => {
    let userData = {
      name: "",
      email: "",
      role,
      avatar: "",
    };

    switch (role) {
      case "candidate":
        userData = {
          name: "Alex Johnson",
          email: "<EMAIL>",
          role,
          avatar:
            "https://images.unsplash.com/photo-1633332755192-727a05c4013d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dXNlcnxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=500&q=60",
        };
        break;
      case "employer":
        userData = {
          name: "Sarah Miller",
          email: "<EMAIL>",
          role,
          avatar:
            "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8dXNlcnxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=500&q=60",
        };
        break;
      case "admin":
        userData = {
          name: "Michael Chen",
          email: "<EMAIL>",
          role,
          avatar:
            "https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OHx8dXNlcnxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=500&q=60",
        };
        break;
    }

    setUser(userData);
    setIsLoggedIn(true);
  };

  // If not logged in, show login/register buttons
  if (!isLoggedIn) {
    // Adapt layout based on whether component is in a drawer
    const isInsideDrawer = inDrawer !== undefined ? inDrawer : isInDrawer;

    if (isInsideDrawer) {
      // Enhanced drawer view for not logged in
      return (
        <div className="w-full">
          <div
            className={`rounded-lg p-4 mb-4 text-center ${
              isDark
                ? "bg-gray-800 border border-gray-700"
                : "bg-gray-50 border border-gray-100"
            }`}
          >
            <Text
              size="sm"
              fw={500}
              className={`mb-2 ${isDark ? "text-gray-200" : ""}`}
            >
              Sign in to access your account
            </Text>
            <Text size="xs" c={isDark ? "gray.4" : "dimmed"} className="!mb-4">
              Manage your job applications, saved jobs, and profile
            </Text>

            <div className="flex flex-col gap-2 w-full">
              <Link to={URLS.auth.login} className="w-full">
                <Button
                  variant="filled"
                  radius="md"
                  leftSection={<FaLock size={14} />}
                  className="w-full bg-primary-color hover:bg-primary-color/90"
                  size="md"
                >
                  Log in
                </Button>
              </Link>

              <Link to={URLS.auth.register} className="w-full">
                <Button
                  variant="outline"
                  radius="md"
                  leftSection={<FaUserPlus size={14} />}
                  className="w-full border-gray-300"
                  size="md"
                >
                  Sign Up
                </Button>
              </Link>
            </div>
          </div>

          {/* Demo section */}
          <Text
            size="xs"
            fw={500}
            c={isDark ? "gray.4" : "dimmed"}
            className="uppercase tracking-wider mb-2 px-2"
          >
            Demo Options
          </Text>

          <div className="flex flex-col gap-1">
            <Button
              variant="subtle"
              leftSection={<FaUser size={14} />}
              onClick={() => simulateLogin("candidate")}
              className="w-full justify-start"
              radius="md"
              size="sm"
            >
              Login as Candidate
            </Button>

            <Button
              variant="subtle"
              leftSection={<FaBriefcase size={14} />}
              onClick={() => simulateLogin("employer")}
              className="w-full justify-start"
              radius="md"
              size="sm"
            >
              Login as Employer
            </Button>

            <Button
              variant="subtle"
              leftSection={<FaCog size={14} />}
              onClick={() => simulateLogin("admin")}
              className="w-full justify-start"
              radius="md"
              size="sm"
            >
              Login as Administrator
            </Button>
          </div>
        </div>
      );
    }

    // Regular view for not logged in (unchanged)
    return (
      <div className="flex items-center gap-3">
        <Link to={URLS.auth.login}>
          <Button
            variant="outline"
            radius="xl"
            leftSection={<FaLock size={14} />}
            className={`px-4 transition-all ${
              isDark
                ? "border-gray-700 bg-gray-800 text-gray-200 hover:border-gray-600 hover:bg-gray-700"
                : "border-gray-300 bg-white text-gray-700 hover:border-gray-400 hover:bg-gray-50"
            }`}
          >
            Log in
          </Button>
        </Link>
        <Link to={URLS.auth.register}>
          <Button
            radius="xl"
            leftSection={<FaUserPlus size={14} />}
            className="bg-primary-color hover:bg-primary-color/90 px-4 transition-all"
          >
            Sign Up
          </Button>
        </Link>
        {/* Demo dropdown for testing */}
        <Menu shadow="md" width={200}>
          <Menu.Target>
            <Button size="xs" variant="subtle" radius="xl" className="text-xs">
              Demo Login
            </Button>
          </Menu.Target>
          <Menu.Dropdown>
            <Menu.Label>Select User Type</Menu.Label>
            <Menu.Item
              leftSection={<FaUser size={14} />}
              onClick={() => simulateLogin("candidate")}
            >
              Candidate
            </Menu.Item>
            <Menu.Item
              leftSection={<FaBriefcase size={14} />}
              onClick={() => simulateLogin("employer")}
            >
              Employer
            </Menu.Item>
            <Menu.Item
              leftSection={<FaCog size={14} />}
              onClick={() => simulateLogin("admin")}
            >
              Administrator
            </Menu.Item>
          </Menu.Dropdown>
        </Menu>
      </div>
    );
  }

  // Get user initials for avatar
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .substring(0, 2);
  };

  // Get links based on user role
  const getLinks = () => {
    if (user?.role === "candidate") {
      return [
        {
          icon: <FaTachometerAlt size={14} />,
          label: "Dashboard",
          href: URLS.candidate.dashboard,
        },
        {
          icon: <FaBriefcase size={14} />,
          label: "Browse Jobs",
          href: URLS.candidate.jobs,
        },
        {
          icon: <FaClipboardList size={14} />,
          label: "My Applications",
          href: URLS.candidate.applications,
        },
        {
          icon: <FaUser size={14} />,
          label: "Profile",
          href: URLS.candidate.profile,
        },
      ];
    } else if (user?.role === "employer") {
      return [
        {
          icon: <FaTachometerAlt size={14} />,
          label: "Dashboard",
          href: URLS.employer.dashboard,
        },
        {
          icon: <FaBriefcase size={14} />,
          label: "Create a Job",
          href: URLS.employer.createJob,
        },
        {
          icon: <FaClipboardList size={14} />,
          label: "Manage Jobs",
          href: URLS.employer.manageJobs,
        },
        {
          icon: <FaUser size={14} />,
          label: "Candidates",
          href: URLS.employer.candidates,
        },
        {
          icon: <FaCog size={14} />,
          label: "Profile",
          href: URLS.employer.profile,
        },
        {
          icon: <FaCreditCard size={14} />,
          label: "Pricing Plans",
          href: URLS.employer.pricing,
        },
      ];
    } else if (user?.role === "admin") {
      return [
        {
          icon: <FaTachometerAlt size={14} />,
          label: "Dashboard",
          href: URLS.admin.dashboard,
        },
        {
          icon: <FaUser size={14} />,
          label: "Users",
          href: URLS.admin.users,
        },
        {
          icon: <FaBriefcase size={14} />,
          label: "Jobs",
          href: URLS.admin.jobs,
        },
        {
          icon: <FaClipboardList size={14} />,
          label: "Companies",
          href: URLS.admin.companies,
        },
      ];
    }

    return [];
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
    setUser(null);
    navigate("/");
  };

  // Use the drawer context for the logged-in view

  // For drawer view, we might want to show a different UI
  if (isInsideDrawer) {
    return (
      <div className="w-full">
        {/* User profile card */}
        <div
          className={`rounded-lg p-4 mb-6 shadow-sm ${
            isDark
              ? "bg-gray-800 border border-gray-700"
              : "bg-gray-50 border border-gray-100"
          }`}
        >
          <div className="flex items-center gap-4">
            <Avatar
              size={50}
              radius="xl"
              src={user?.avatar}
              color="blue"
              className={`border-2 shadow-sm ${
                isDark ? "border-gray-700" : "border-white"
              }`}
            >
              {getInitials(user?.name || "")}
            </Avatar>
            <div>
              <Text size="md" fw={600} className="text-primary-color">
                {user?.name}
              </Text>
              <Text size="xs" c={isDark ? "gray.4" : "dimmed"}>
                {user?.email}
              </Text>
              <div className="mt-2">
                <span
                  className={`inline-block rounded-full px-3 py-1 text-xs font-medium ${
                    isDark
                      ? "bg-blue-900/30 text-blue-300"
                      : "bg-blue-100 text-blue-700"
                  }`}
                >
                  {user?.role
                    ? user.role.charAt(0).toUpperCase() + user.role.slice(1)
                    : ""}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation section */}
        <Text
          size="xs"
          fw={500}
          c={isDark ? "gray.4" : "dimmed"}
          className="uppercase tracking-wider mb-2 px-2"
        >
          Navigation
        </Text>

        <div className="flex flex-col gap-1 w-full mb-6">
          {getLinks().map((link, index) => (
            <Link key={index} to={link.href} className="w-full">
              <Button
                variant="subtle"
                leftSection={link.icon}
                className={`w-full justify-start hover:text-primary-color ${
                  isDark
                    ? "text-gray-300 hover:bg-gray-800"
                    : "text-gray-700 hover:bg-gray-100"
                }`}
                radius="md"
                size="sm"
              >
                {link.label}
              </Button>
            </Link>
          ))}
        </div>

        {/* Account section */}
        <Text
          size="xs"
          fw={500}
          c={isDark ? "gray.4" : "dimmed"}
          className="uppercase tracking-wider mb-2 px-2"
        >
          Account
        </Text>

        <Button
          variant="subtle"
          leftSection={<FaSignOutAlt size={14} />}
          color="red"
          onClick={handleLogout}
          className="w-full justify-start"
          radius="md"
          size="sm"
        >
          Logout
        </Button>
      </div>
    );
  }

  // Default dropdown menu for normal view
  return (
    <Menu
      width={240}
      position="bottom-end"
      transitionProps={{ transition: "pop-top-right" }}
      shadow="md"
      withinPortal
    >
      <Menu.Target>
        <UnstyledButton
          className={`flex items-center gap-2 rounded-md px-2 py-1 transition-colors ${
            isDark ? "hover:bg-gray-800" : "hover:bg-gray-100"
          }`}
        >
          <Avatar size={38} radius="xl" src={user?.avatar} color="blue">
            {getInitials(user?.name || "")}
          </Avatar>
          <div className="hidden xl:block">
            <Text size="sm" fw={600} className="text-primary-color">
              {user?.name}
            </Text>
            <Text size="xs" c={isDark ? "gray.4" : "dimmed"}>
              {user?.email}
            </Text>
          </div>
          <FaChevronDown
            className={isDark ? "text-gray-400" : "text-gray-500"}
          />
        </UnstyledButton>
      </Menu.Target>
      <Menu.Dropdown>
        <div className="px-3 py-2">
          <Text size="sm" fw={600} className="text-primary-color">
            {user?.name}
          </Text>
          <Text size="xs" c={isDark ? "gray.4" : "dimmed"}>
            {user?.email}
          </Text>
          <Text size="xs" c={isDark ? "gray.4" : "dimmed"} className="mt-1">
            <span
              className={`inline-block rounded-full px-2 py-1 text-xs font-medium ${
                isDark
                  ? "bg-blue-900/30 text-blue-300"
                  : "bg-blue-100 text-blue-700"
              }`}
            >
              {user?.role
                ? user.role.charAt(0).toUpperCase() + user.role.slice(1)
                : ""}
            </span>
          </Text>
        </div>
        <Menu.Divider />
        <Menu.Label>Navigation</Menu.Label>
        {getLinks().map((link, index) => (
          <Link key={index} to={link.href} className="block">
            <Menu.Item leftSection={link.icon}>{link.label}</Menu.Item>
          </Link>
        ))}
        <Menu.Divider />
        <Menu.Item
          leftSection={<FaSignOutAlt size={14} />}
          onClick={handleLogout}
          color="red"
        >
          Logout
        </Menu.Item>
      </Menu.Dropdown>
    </Menu>
  );
}
