import { Ava<PERSON>, Card, useMantineColorScheme } from "@mantine/core";
import { FaQuoteLeft } from "react-icons/fa";

const testimonials = [
  {
    id: 1,
    name: "<PERSON>",
    position: "HR Manager at TechCorp",
    content:
      "JobNest has revolutionized our hiring process. We've found exceptional talent quickly and efficiently. The platform is intuitive and the candidate quality is outstanding.",
    avatar: "S<PERSON>",
  },
  {
    id: 2,
    name: "<PERSON>",
    position: "Software Developer",
    content:
      "I found my dream job through JobNest within just two weeks of signing up. The job matching algorithm is spot-on, and the application process was seamless.",
    avatar: "MC",
  },
  {
    id: 3,
    name: "<PERSON>",
    position: "Marketing Director at GrowthLabs",
    content:
      "As a hiring manager, JobNest has saved me countless hours. The filtering tools and candidate profiles provide exactly the information I need to make decisions.",
    avatar: "ER",
  },
];

export default function Testimonials() {
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";
  return (
    <section className={`py-24 ${isDark ? "bg-gray-900" : "bg-gray-50"}`}>
      <div className="container">
        <div className="mx-auto max-w-3xl text-center">
          <h2 className="text-primary-color mb-4 text-sm font-semibold tracking-wider uppercase">
            Testimonials
          </h2>
          <h3
            className={`mb-6 text-4xl font-bold tracking-tight sm:text-5xl ${
              isDark ? "text-white" : "text-gray-900"
            }`}
          >
            What our users say
          </h3>
          <p
            className={`mb-12 text-lg ${
              isDark ? "text-gray-300" : "text-gray-600"
            }`}
          >
            Don&apos;t just take our word for it. Here&apos;s what employers and
            job seekers have to say about their experience with JobNest.
          </p>
        </div>

        <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
          {testimonials.map(({ id, name, position, content, avatar }) => (
            <Card
              key={id}
              padding="xl"
              radius="md"
              className={`relative h-full border-0 shadow-sm transition-all duration-300 hover:shadow-md ${
                isDark ? "bg-gray-800" : "bg-white"
              }`}
            >
              <FaQuoteLeft className="text-primary-color/10 absolute -top-4 left-6 text-6xl" />

              <div className="relative">
                <p
                  className={`my-8 ${
                    isDark ? "text-gray-300" : "text-gray-600"
                  }`}
                >
                  {content}
                </p>

                <div className="flex items-center gap-4">
                  <Avatar color="primary_color" radius="xl" size="md">
                    {avatar}
                  </Avatar>
                  <div>
                    <h4
                      className={`font-semibold ${
                        isDark ? "text-white" : "text-gray-900"
                      }`}
                    >
                      {name}
                    </h4>
                    <p
                      className={`text-sm ${
                        isDark ? "text-gray-400" : "text-gray-500"
                      }`}
                    >
                      {position}
                    </p>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
