import { useSelectedCandidateStore } from "@/stores/employer-store";
import { useCandidateDetailsModal } from "@/stores/modal-store";
import { type Candidate } from "@/types";
import {
  ActionIcon,
  Avatar,
  Badge,
  Card,
  Group,
  Menu,
  Text,
} from "@mantine/core";
import {
  FaBriefcase,
  FaCalendarAlt,
  FaEllipsisV,
  FaEnvelope,
  FaEye,
  FaPhoneAlt,
  FaUserEdit,
} from "react-icons/fa";

type CandidateListItemProps = {
  candidate: Candidate;
  job: any;
};

export default function CandidateListItem({
  candidate,
  job,
}: CandidateListItemProps) {
  const setSelectedCandidateId = useSelectedCandidateStore(
    (state) => state.setSelectedCandidateId,
  );
  const candidateDetailsModal = useCandidateDetailsModal();

  const openModal = () => {
    setSelectedCandidateId(candidate.id);
    candidateDetailsModal.open();
  };

  // Function to get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "Applied":
        return "blue";
      case "Shortlisted":
        return "green";
      case "Interviewed":
        return "yellow";
      case "Rejected":
        return "red";
      case "Hired":
        return "violet";
      default:
        return "gray";
    }
  };

  // Function to get initials from name
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  return (
    <Card withBorder radius="md" className="shadow-sm" p="md">
      <Group justify="space-between" align="flex-start">
        {/* Candidate Info */}
        <Group align="flex-start" wrap="nowrap">
          <Avatar
            radius="xl"
            size="lg"
            color={getStatusColor(candidate.status)}
            className="hidden sm:flex"
          >
            {getInitials(candidate.name)}
          </Avatar>

          <div>
            <Group align="center" gap="xs">
              <Text fw={600} size="lg">
                {candidate.name}
              </Text>
              <Badge color={getStatusColor(candidate.status)} size="sm">
                {candidate.status}
              </Badge>
            </Group>

            <Text size="sm" c="dimmed" mb={8}>
              {candidate.email}
            </Text>

            <Group gap="lg">
              <Group gap="xs" wrap="nowrap">
                <FaBriefcase
                  size={14}
                  className="flex-shrink-0 text-blue-500"
                />
                <Text size="sm" className="line-clamp-1">
                  Applied for: <strong>{job?.title}</strong>
                </Text>
              </Group>

              <Group gap="xs" wrap="nowrap">
                <FaCalendarAlt
                  size={14}
                  className="flex-shrink-0 text-blue-500"
                />
                <Text size="sm">Applied on: {candidate.appliedDate}</Text>
              </Group>
            </Group>

            {/* Skills */}
            {candidate.skills && candidate.skills.length > 0 && (
              <Group gap="xs" mt={8}>
                {candidate.skills.map((skill, index) => (
                  <Badge key={index} color="blue" variant="light" size="sm">
                    {skill}
                  </Badge>
                ))}
              </Group>
            )}
          </div>
        </Group>

        {/* Actions */}
        <Group gap="xs">
          <ActionIcon
            variant="light"
            color="blue"
            onClick={() => openModal()}
            title="View Details"
          >
            <FaEye size={16} />
          </ActionIcon>

          <Menu position="bottom-end" shadow="md">
            <Menu.Target>
              <div className="cursor-pointer">
                <ActionIcon variant="subtle">
                  <FaEllipsisV size={16} />
                </ActionIcon>
              </div>
            </Menu.Target>

            <Menu.Dropdown>
              <Menu.Item
                leftSection={<FaEye size={14} />}
                onClick={() => openModal()}
              >
                View Details
              </Menu.Item>
              <Menu.Item leftSection={<FaUserEdit size={14} />}>
                Change Status
              </Menu.Item>
              <Menu.Item leftSection={<FaEnvelope size={14} />}>
                Send Email
              </Menu.Item>
              <Menu.Item leftSection={<FaPhoneAlt size={14} />}>
                Call Candidate
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
        </Group>
      </Group>
    </Card>
  );
}
