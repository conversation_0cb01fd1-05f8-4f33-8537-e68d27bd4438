import SectionHeading from "@/design-system/components/typography/SectionHeading";
import { useThemeClasses } from "@/design-system/utils/theme-utils";
import { URLS } from "@/utils/urls";
import { Box, Group, SimpleGrid, Text, ThemeIcon } from "@mantine/core";
import { type IconType } from "react-icons";
import {
  FaBriefcase,
  FaBuilding,
  FaClipboardCheck,
  FaPlus,
  FaUserPlus,
  FaUsers,
} from "react-icons/fa";
import { Link } from "react-router";

interface QuickActionItem {
  id: number;
  label: string;
  description: string;
  href: string;
  icon: IconType;
  color: string;
}

const quickActions: QuickActionItem[] = [
  {
    id: 1,
    label: "Manage Users",
    description: "View and manage user accounts",
    href: URLS.admin.users,
    icon: FaUsers,
    color: "blue",
  },
  {
    id: 2,
    label: "Manage Jobs",
    description: "Review and approve job listings",
    href: URLS.admin.jobs,
    icon: FaBriefcase,
    color: "green",
  },
  {
    id: 3,
    label: "Manage Companies",
    description: "Verify and manage company profiles",
    href: URLS.admin.companies,
    icon: FaBuilding,
    color: "purple",
  },
  {
    id: 4,
    label: "Add New User",
    description: "Create a new user account",
    href: URLS.admin.users,
    icon: FaUserPlus,
    color: "teal",
  },
  {
    id: 5,
    label: "Add New Job",
    description: "Create a new job listing",
    href: URLS.admin.jobs,
    icon: FaPlus,
    color: "indigo",
  },
  {
    id: 6,
    label: "Pending Approvals",
    description: "Review items waiting for approval",
    href: URLS.admin.dashboard,
    icon: FaClipboardCheck,
    color: "orange",
  },
];

export default function QuickActions() {
  return (
    <>
      <SectionHeading variant="admin">Quick Actions</SectionHeading>
      <SimpleGrid cols={{ base: 1, sm: 2, lg: 3 }} spacing="md" mt="md">
        {quickActions.map(
          ({ href, icon: Icon, id, label, description, color }) => (
            <Box
              key={id}
              component={Link}
              to={href}
              className={useThemeClasses(
                `border border-${color}-100 bg-gradient-to-br from-${color}-50 rounded-lg to-white p-4 no-underline transition-all hover:shadow-md`,
                `border border-${color}-900/30 bg-dark-6 rounded-lg p-4 no-underline transition-all hover:shadow-dark-lg`,
              )}
            >
              <Group>
                <ThemeIcon variant="light" color={color} size="lg" radius="md">
                  <Icon size={18} />
                </ThemeIcon>
                <div>
                  <Text
                    fw={600}
                    size="sm"
                    className={useThemeClasses(
                      `text-${color}-900`,
                      `text-${color}-300`,
                    )}
                  >
                    {label}
                  </Text>
                  <Text size="xs" c="dimmed">
                    {description}
                  </Text>
                </div>
              </Group>
            </Box>
          ),
        )}
      </SimpleGrid>
    </>
  );
}
