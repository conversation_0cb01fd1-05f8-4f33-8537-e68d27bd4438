import { companyStatusUpdateSchema } from "@/schemas/admin/company-management-schema";
import {
  Alert,
  Button,
  Checkbox,
  Divider,
  Group,
  Modal,
  Select,
  Stack,
  Text,
  Textarea,
} from "@mantine/core";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import { FaBan, FaExclamationTriangle } from "react-icons/fa";

interface CompanyBlockModalProps {
  opened: boolean;
  onClose: () => void;
  onConfirm: (reason: string) => void;
  companyId: number | null;
}

export default function CompanyBlockModal({
  opened,
  onClose,
  onConfirm,
  // companyId,
}: CompanyBlockModalProps) {
  const form = useForm({
    initialValues: {
      status: "blocked" as const,
      blockReason: "",
      blockDuration: "permanent",
      notifyCompany: true,
      blockJobs: true,
    },
    validate: zodResolver(companyStatusUpdateSchema),
  });

  const handleSubmit = (values: typeof form.values) => {
    onConfirm(values.blockReason);
    form.reset();
    onClose();
  };

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <Group gap="md">
          <FaBan size={18} className="text-red-500" />
          <Text fw={600}>Block Company</Text>
        </Group>
      }
      size="md"
      centered
    >
      <Alert
        icon={<FaExclamationTriangle size={16} />}
        title="Warning"
        color="yellow"
        mb="md"
      >
        Blocking a company will prevent them from posting new jobs and accessing
        certain features. This action can be reversed later.
      </Alert>

      <form onSubmit={form.onSubmit(handleSubmit)}>
        <Stack gap="md">
          <Textarea
            label="Block Reason"
            description="This reason will be visible to the company"
            placeholder="Please provide a detailed reason for blocking this company"
            required
            minRows={4}
            {...form.getInputProps("blockReason")}
          />

          <Select
            label="Block Duration"
            placeholder="Select duration"
            data={[
              { value: "7days", label: "7 Days" },
              { value: "30days", label: "30 Days" },
              { value: "90days", label: "90 Days" },
              { value: "permanent", label: "Permanent" },
            ]}
            defaultValue="permanent"
            {...form.getInputProps("blockDuration")}
          />

          <Divider my="xs" />

          <Checkbox
            label="Notify company via email"
            description="Send an email notification with the block reason"
            checked={form.values.notifyCompany}
            onChange={(event) =>
              form.setFieldValue("notifyCompany", event.currentTarget.checked)
            }
          />

          <Checkbox
            label="Block all active job listings"
            description="Make all current job listings inactive"
            checked={form.values.blockJobs}
            onChange={(event) =>
              form.setFieldValue("blockJobs", event.currentTarget.checked)
            }
          />

          <Group mt="lg" justify="flex-end">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button color="red" type="submit" leftSection={<FaBan size={14} />}>
              Block Company
            </Button>
          </Group>
        </Stack>
      </form>
    </Modal>
  );
}
