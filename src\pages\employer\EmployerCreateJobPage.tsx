import CreateJobForm from "@/components/employer/forms/CreateJobForm";
import { PageContainer, PageHeading } from "@/design-system/components";
import { useThemeClasses } from "@/design-system/utils/theme-utils";
import { Card } from "@mantine/core";

export default function EmployerCreateJobPage() {
  return (
    <PageContainer
      breadcrumbItems={[{ title: "Home", href: "/" }, { title: "Create Job" }]}
      variant="employer"
      className="pb-12"
    >
      {/* Hero Section */}
      <Card withBorder radius="md" className="mb-8 overflow-hidden" p={0}>
        <div className="relative">
          {/* Background gradient */}
          <div
            className={useThemeClasses(
              "absolute inset-0 bg-gradient-to-r from-blue-50 via-indigo-50 to-blue-50",
              "absolute inset-0 bg-gradient-to-r from-dark-6 via-dark-5 to-dark-6",
            )}
          ></div>

          <div className="relative p-6 md:p-8">
            <PageHeading
              title="Create New Job"
              subtitle="Post a job opening to find the perfect candidate for your company"
              className="mb-0"
            />
          </div>
        </div>
      </Card>

      <CreateJobForm />
    </PageContainer>
  );
}
